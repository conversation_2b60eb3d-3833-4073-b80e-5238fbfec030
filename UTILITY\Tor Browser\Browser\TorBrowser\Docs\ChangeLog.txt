Tor Browser 12.0.2 - January 18 2023
 * All Platforms
   * Updated tor to ********
   * Updated NoScript to 11.4.14
   * Bug 40565: do something with security.tls.version.enable-deprecated [tor-browser]
   * Bug 40713: Use the new tor-browser l10n branch in Firefox [tor-browser-build]
   * Bug 40727: Update list of Snowflake STUN servers in default bridge line [tor-browser-build]
   * Bug 41506: Remove TrustCor root certificates [tor-browser]
   * Bug 41525: Drop locales from torbutton, since we will inject them in tor-browser-build [tor-browser]
 * Windows + macOS + Linux
   * Updated Firefox to 102.7esr
   * Bug 26504: Browser version in about:preferences showing the Firefox ESR version [tor-browser]
   * Bug 32308: Stop inner letterbox jiggling as border is dragged [tor-browser]
   * Bug 41375: Clean unused strings [tor-browser]
   * Bug 41393: about:tbupdate semantic and accessibility problems [tor-browser]
   * Bug 41522: Backport torbutton -> tor-browser migration to 12.0 series [tor-browser]
   * Bug 41524: about:tbupdate needs UTF-8 [tor-browser]
   * Bug 41539: Crypto warning weaknesses [tor-browser]
   * Bug 41549: tor freeze when receiving to many http proxy requests on socks port [tor-browser]
   * Bug 41561: Maximize warning is broken (regression) [tor-browser]
   * Bug 41563: Old placeholders used in TorStrings.jsm [tor-browser]
 * macOS
   * Bug 40716: Unable to update to 12.0.1 on Apple Silicon-based Mac [tor-browser-build]
 * Android
   * Updated GeckoView to 102.7esr
   * Bug 41571: Backport Android-specific Firefox 109 to ESR 102.7-based Tor Browser [tor-browser]
 * Build System
   * All Platforms
     * Updated Go to 1.19.5
     * Bug 40735: Add command to list which translation components need to be updated [tor-browser-build]
     * Bug 40739: tor-expert-bundle should include ClientTransportPlugin torrc lines for each pluggable transport [tor-browser-build]
   * Windows + macOS + Linux
     * Bug 40734: Backport the translation project [tor-browser-build]
     * Bug 40746: Remove pt_config.json from pt dir [tor-browser-build]
   * macOS
     * Bug 40706: macos-signer-stapler should wait for user interaction before attempting stapling [tor-browser-build]

Tor Browser 12.5a1 - December 21 2022
 * All Platforms
   * Updated tor to 0.4.7.12
   * Bug 40711: Review and expand the stakeholders we communicate major changes to [tor-browser-build]
   * Bug 41478: Drop the torbutton submodule in 12.5 [tor-browser]
   * Bug 41514: eslint broken since migrating torbutton [tor-browser]
 * Windows + macOS + Linux
   * Updated Firefox to 102.6esr
   * Bug 26504: about:preferences shows Firefox's version instead of Tor Browser's
   * Bug 32308: Stop inner letterbox jiggling as border is dragged [tor-browser]
   * Bug 40347: URL bar lock icon says connection is not secure when on "view-source:[...].onion" URLs [tor-browser]
   * Bug 40678: Force all 11.5 users to update through 11.5.8 before 12.0 [tor-browser-build]
   * Bug 41375: Clean unused strings [tor-browser]
   * Bug 41435: Add a Tor Browser migration function [tor-browser]
   * Bug 41448: User `danger` style for primary button in new identity modal [tor-browser]
   * Bug 41483: Tor Browser says Firefox timed out, confusing users [tor-browser]
   * Bug 41503: Disable restart in case of reboot and restore in case of crash [tor-browser]
   * Bug 41520: Regression: rearranging bookmarks / place items by drag & drop doesn't work anymore [tor-browser]
   * Bug 41524: about:tbupdate needs UTF-8 [tor-browser]
   * Bug 41525: Drop locales from torbutton, since we will inject them in tor-browser-build [tor-browser]
 * macOS + Linux
   * Bug 41519: TOR_SOCKS_IPC_PATH environment variable not honored [tor-browser]
 * Windows
   * Bug 40708: tor.exe in tor-expert-bundle not writing stdout even when run from cmd.exe [tor-browser-build]
 * macOS
   * Bug 40716: Unable to update to 12.0.1 on Apple Silicon-based Mac [tor-browser-build]
 * Android
   * Updated GeckoView to 102.6esr
   * Bug 41001: Remove remaining security slider code [tor-browser]
 * Build System
   * All Platforms
     * Updated Go to 1.19.4
     * Bug 40645: Verify we no longer depend on any signed tags from sysrqb and gk, and remove them from torbutton.gpg [tor-browser-build]
     * Bug 40679: Use the latest translations for nightly builds [tor-browser-build]
     * Bug 40681: Run apt-get clean, after installing packages in projects/container-image/config [tor-browser-build]
     * Bug 40683: Install more packages in the default containers to reduce the number of custom containers [tor-browser-build]
     * Bug 40689: Update Ubuntu version from projects/mmdebstrap-image/config to 22.04.1 [tor-browser-build]
     * Bug 40717: Create a script to prepare changelogs [tor-browser-build]
   * Windows + macOS + Linux
     * Bug 40707: Update update_responses_config.yml to allow 11.5.8 to update to whatever latest is [tor-browser-build]
     * Bug 40713: Use the new tor-browser l10n branch in Firefox [tor-browser-build]
   * Linux + Android
     * Bug 40653: Build compiler-rt with runtimes instead of the main LLVM build [tor-browser-build]
   * macOS
     * Bug 40694: aarch64 tor-expert-bundle for macOS is not exported as part of the browser build [tor-browser-build]
     * Bug 40704: Building nightly macos incrementals fails [tor-browser-build]
   * Linux
     * Bug 40693: Can't build container-image in main [tor-browser-build]
   * Android
     * Bug 40702: Nightly builds fails with "error: pathspec 'tor-browser-102.5.0esr-12.0-2' did not match any file(s) known to git" [tor-browser-build]

Tor Browser 12.0.1 - December 14 2022
 * Windows + macOS + Linux
   * Updated Firefox to 102.6esr
   * Bug 41519: TOR_SOCKS_IPC_PATH environment variable not honored [tor-browser]
   * Bug 41520: Regression: rearranging  bookmarks / place items by drag & drop doesn't work anymore [tor-browser]
 * Android
   * Updated GeckoView to 102.6esr
 * Build System
   * All Platforms
     * Updated Go to 1.19.4
     * Bug 40653: Build compiler-rt with runtimes instead of the main LLVM build [tor-browser-build]
     * Bug 40681: Run apt-get clean, after installing packages in projects/container-image/config [tor-browser-build]
     * Bug 40683: Install more packages in the default containers to reduce the number of custom containers [tor-browser-build]
     * Bug 40693: Can't build container-image in main [tor-browser-build]
     * Bug 40705: Add empty commit mentioning that gitolite repo is no longer updated [tor-browser-build]
   * Windows
     * Bug 40708: tor.exe in tor-expert-bundle not writing stdout even when run from cmd.exe [tor-browser-build]
   * Linux
     * Bug 40689: Update Ubuntu version from projects/mmdebstrap-image/config to 22.04.1 [tor-browser-build]

Tor Browser 12.0 - December 5 2022
 * All Platforms
   * Update Translations
   * Update tor to 0.4.7.12
   * Bug 17228: Consideration for disabling/trimming referrers within TBB [tor-browser]
   * Bug 24686: In Tor Browser context, should network.http.tailing.enabled be set to false? [tor-browser]
   * Bug 27127: Audit and enable HTTP/2 push [tor-browser]
   * Bug 27258: font whitelist means we don't have to set gfx.downloadable_fonts.fallback_delay [tor-browser]
   * Bug 40057: ensure that CSS4 system colors are not a fingerprinting vector [tor-browser]
   * Bug 40058: ensure no locale leaks from new Intl APIs [tor-browser]
   * Bug 40183: Consider disabling TLS ciphersuites containing SHA-1 [tor-browser]
   * Bug 40251: Clear obsolete prefs after torbutton!27 [tor-browser]
   * Bug 40406: Remove Presentation API related prefs [tor-browser]
   * Bug 40491: Don't auto-pick a v2 address when it's in Onion-Location header [tor-browser]
   * Bug 40494: Update Startpage and Blockchair onion search providers [tor-browser]
   * Bug 40580: Add support for Ukranian (uk) [tor-browser-build]
   * Bug 40646: Don't build Español AR anymore [tor-browser-build]
   * Bug 40674: Add Secondary Snowflake Bridge [tor-browser-build]
   * Bug 40783: Review 000-tor-browser.js and 001-base-profile.js for 102 [tor-browser]
   * Bug 41098: Compare Tor Browser's and GeckoView's profiles [tor-browser]
   * Bug 41125: Review Mozilla 1732792: retry polling requests without proxy [tor-browser]
   * Bug 41154: Review Mozilla 1765167: Deprecate Cu.import [tor-browser]
   * Bug 41164: Add some #define for the base-browser section [tor-browser]
   * Bug 41306: Typo "will not able" in "Tor unexpectedly exited" dialog [tor-browser]
   * Bug 41317: Tor Browser leaks banned ports in network.security.ports.banned [tor-browser]
   * Bug 41345: fonts: windows whitelist contains supplemental fonts [tor-browser]
   * Bug 41398: Disable Web MIDI API [tor-browser]
   * Bug 41406: Do not define `--without-wasm-sandboxed-libraries` if `WASI_SYSROOT` is defined [tor-browser]
   * Bug 41420: Remove brand.dtd customization on nightly [tor-browser]
   * Bug 41457: Remove more Mozilla permissions [tor-browser]
   * Bug 41473: Add support for Albanian (sq) [tor-browser]
 * Windows + macOS + Linux
   * Update Firefox to 102.5.0esr
   * Bug 17400: Decide how to use the multi-lingual Tor Browser in the alpha/release series [tor-browser]
   * Bug 40595: Migrate to 102 on desktop [tor-browser-buid]
   * Bug 40638: Visit our website link after build-to-build upgrade in Nightly channel points to old v2 onion [tor-browser-build]
   * Bug 40648: Do not customize update.locale in multi-lingual builds [tor-browser-build]
   * Bug 40853: use Subprocess.jsm to launch tor [tor-browser]
   * Bug 40933: Migrate remaining tor-launcher functionality to tor-browser [tor-browser]
   * Bug 41011: The Internet and Tor status are visible when opening the settings [tor-browser]
   * Bug 41044: Content exceeding the height of the connection settings modals [tor-browser]
   * Bug 41116: Review Mozilla 1226042: add support for the new 'system-ui' generic font family [tor-browser]
   * Bug 41117: Review Mozilla 1512851:  Add Share Menu to File Menu on macOS [tor-browser]
   * Bug 41283: Toolbar buttons missing their label attribute [tor-browser]
   * Bug 41284: Stray security-level- fluent ids [tor-browser]
   * Bug 41287: New identity button inactive if added after customization [tor-browser]
   * Bug 41292: moreFromMozilla pane in about:preferences in 12.0a2 [tor-browser]
   * Bug 41293: Incomplete branding in German with 12.0a2 [tor-browser]
   * Bug 41337: Add a title to the new identity confirmation [tor-browser]
   * Bug 41342: Update the New Identity dialog to the proton modal style [tor-browser]
   * Bug 41344: Authenticated Onion Services do not prompt for auth key in 12.0 alpha series [tor-browser]
   * Bug 41352: Update or drop the show manual logic in torbutton [tor-browser]
   * Bug 41369: Consider a different list-order for locales in language menu [tor-browser]
   * Bug 41374: Missing a few torconnect strings in the DTD [tor-browser]
   * Bug 41377: Hide `Search for more languages...` from Language selector in multi-locale build [tor-browser]
   * Bug 41378: Inform users when Tor Browser sets their language automatically [tor-browser]
   * Bug 41385: Bootstrap failure is logged but not raised up to about:torconnect [tor-browser]
   * Bug 41386: The new tor-launcher has a problem when another Tor is running [tor-browser]
   * Bug 41387: New identity and new circuit ended up inside history [tor-browser]
   * Bug 41400: Missing onionAuthViewKeys causes "Onion Services Keys" dialog to be empty. [tor-browser]
   * Bug 41401: Missing some mozilla icons because we still loading them from "chrome://browser/skin" rather than "chrome://global/skin/icons" [tor-browser]
   * Bug 41404: Fix blockchair-onion search extension [tor-browser]
   * Bug 41409: Circuit display is broken on Tails [tor-browser]
   * Bug 41410: Opening and closing HTTPS-Only settings make the identity panel shrink [tor-browser]
   * Bug 41412: New Identity shows "Tor Browser" instead of "Restart Tor Browser" in unstranslated locales [tor-browser]
   * Bug 41417: Prompt users to restart after changing language [tor-browser]
   * Bug 41429: TorConnect and TorSettings are initialized twice [tor-browser]
   * Bug 41435: Add a Tor Browser migration function [tor-browser]
   * Bug 41436: The new tor-launcher handles arrays in the wrong way [tor-browser]
   * Bug 41437: Use the new media query for dark theme for the "Connected" pill in bridges [tor-browser]
   * Bug 41449: Onion authentication's learn more should link to the offline manual [tor-browser]
   * Bug 41451: Still using requestedLocales in torbutton [tor-browser]
   * Bug 41455: Tor Browser dev build cannot launch tor [tor-browser]
   * Bug 41458: Prevent `mach package-multi-locale` from actually creating a package [tor-browser]
   * Bug 41462: Add anchors to bridge-moji and onion authentication entries [tor-browser]
   * Bug 41466: Port YEC 2022 campaign to Tor Browser 12.0 (Desktop) [tor-browser]
   * Bug 41495: Clicking on "View Logs" in the "Establishing Connection" page takes you to about:preferences#connection and not logs [tor-browser]
   * Bug 41498: The Help panel is empty in 12.0a4 [tor-browser]
   * Bug 41508: Update the onboarding link for 12.0 [tor-browser]
 * Windows
   * Bug 41149: Review Mozilla 1762576: Firefox is not allowing Symantec DLP to inject DLL into the browser for Data Loss Prevention software [tor-browser]
   * Bug 41278: Create Tor Browser styled pdf logo similar to the vanilla Firefox one [tor-browser]
   * Bug 41426: base-browser nightly fails to build for windows-i686 [tor-browser]
 * macOS
   * Bug 23451: Adapt font whitelist to changes on macOS (zh locales) [tor-browser]
   * Bug 41004: The Bangla font does not display correctly on MacOs [tor-browser]
   * Bug 41108: Remove privileged macOS installation from 102 [tor-browser]
   * Bug 41294: Bookmarks manager broken in 12.0a2 on MacOS [tor-browser]
   * Bug 41348: cherry-pick macOS OSSpinLock  replacements [tor-browser]
   * Bug 41370: Find a way to ship custom default bookmarks without changing language-packs on macOS [tor-browser]
   * Bug 41372: "Japanese" language menu item is localised in multi-locale testbuild (on mac OS) [tor-browser]
   * Bug 41379: The new tor-launcher is broken also on macOS [tor-browser]
 * Linux
   * Bug 40359: Tor Browser Launcher has Wrong Icon [tor-browser]
   * Bug 40626: Define the replacements for generic families on Linux [tor-browser-build]
   * Bug 41163: Fixing loading of bundled fonts on linux [tor-browser]
 * Android
   * Update GeckoView to 102.5.0esr
   * Bug 40014: Check which of our mobile prefs and configuration changes are still valid for GeckoView [tor-browser]
   * Bug 40631: Stop bundling HTTPS Everywhere on Android [tor-browser-build]
   * Bug 41394: Implement a setting to always prefer onion sites [tor-browser]
   * Bug 41465: Port YEC 2022 campaign to Tor Browser 12.0 (Android) [tor-browser]
 * Build System
   * All Platforms
     * Update Go to 1.19.3
     * Bug 23656: Use .mozconfig files in tor-browser repo for rbm builds [tor-browser-build]
     * Bug 28754: make testbuild-android-armv7 stalls during sha256sum step [tor-browser-build]
     * Bug 40049: gpg_keyring should allow for array of keyrings [rbm]
     * Bug 40397: Create a new build target to package tor daemon, pluggable transports and dependencies [tor-browser-build]
     * Bug 40407: Bump binutils version to pick up security improvements for Windows users [tor-browser-build]
     * Bug 40585: Prune the manual more [tor-browser-build]
     * Bug 40587: Migrate tor-browser-build configs from gitolite to gitlab repos [tor-browser-build]
     * Bug 40591: Rust 1.60 not working to build 102 on Debian Jessie [tor-browser-build]
     * Bug 40592: Consider re-using our LLVM/Clang to build Rust [tor-browser-build]
     * Bug 40593: Update signing scripts to take into account new project names and layout [tor-browser-build]
     * Bug 40607: Add alpha-specific release prep template [tor-browser-build]
     * Bug 40610: src-*.tar.xz tarballs are missing in https://dist.torproject.org/torbrowser/12.0a1/ [tor-browser-build]
     * Bug 40612: Migrate Release Prep template to Release Prep - Stable [tor-browser-build]
     * Bug 40619: Make sure translations are taken from gitlab.tpo and not git.tpo [tor-browser-build]
     * Bug 40627: Add boklm to torbutton.gpg [torbrowser-build]
     * Bug 40634: Update the project/browser path in tools/changelog-format-blog-post and other files [tor-browser-build]
     * Bug 40636: Remove https-everywhere from projects/browser/config [tor-browser-build]
     * Bug 40639: Remove tor-launcher references [tor-browser-build]
     * Bug 40643: Update Richard's key in torbutton.gpg [tor-browser-build]
     * Bug 40645: Remove unused signing keys and create individual keyrings for Tor Project developers [tor-browser-build]
     * Bug 40655: Published tor-expert-bundle tar.gz files should not include their tor-browser-build build id [tor-browser-build]
     * Bug 40656: Improve get_last_build_version in tools/signing/nightly/sign-nightly [tor-browser-build]
     * Bug 40660: Update changelog-format-blog-post script to point gitlab rather than gitolite [tor-browser-build]
     * Bug 40662: Make base-browser nightly build from tag [tor-browser-build]
     * Bug 40663: Do not ship bookmarks in tor-browser-build anymore [tor-browser-build]
     * Bug 40667: Update Node.js to 12.22.12 [tor-browser-build]
     * Bug 40669: Remove HTTPS-Everywhere keyring [tor-browser-build]
     * Bug 40671: Update langpacks URL [tor-browser-build]
     * Bug 40675: Update tb_builders list in set-config [tor-browser-build]
     * Bug 40690: Revert fix for zlib build break [tor-browser-build]
     * Bug 41308: Use the same branch for Desktop and GeckoView [tor-browser]
     * Bug 41321: Delete various master branches after automated build/testing scripts are updated [tor-browser]
     * Bug 41340: Opt in to some of the NIGHTLY_BUILD features [tor-browser]
     * Bug 41357: Enable browser toolbox debugging by default for dev builds [tor-browser]
     * Bug 41446: Multi-lingual alpha bundles break make fetch [tor-browser]
   * Windows + macOS + Linux
     * Bug 40499: Update firefox to enable building from new 'base-browser' tag [tor-browser-build]
     * Bug 40500: Add base-browser package project [tor-browser-build]
     * Bug 40501: Makefile updates to support building base-browser packages [tor-browser-build]
     * Bug 40503: Update Release Prep issue template with base-browser and privacy browser changes [tor-browser-build]
     * Bug 40547: Remove container/remote_* from rbm.conf [tor-browser-build]
     * Bug 40581: Update reference to master branches [tor-browser-build]
     * Bug 40641: Fetch Firefox locales from l10n-central [tor-browser-build]
     * Bug 40678: Force all 11.5 users to update through 11.5.8 before 12.0 [tor-browser-build]
     * Bug 40685: Remove targets/nightly/var/mar_locales from rbm.conf [tor-browser-build]
     * Bug 40686: Add a temporary project to fetch Fluent tranlations for base-browser [tor-browser-build]
     * Bug 40691: Update firefox config to point to base-browser branch rather than a particular tag in nightly [tor-browser-build]
     * Bug 40699: Fix input_files in projects/firefox-l10n/config [tor-browser-build]
     * Bug 41099: Update+comment the update channels in update_responses.config.yaml [tor-browser]
   * Windows
     * Bug 29318: Use Clang for everything on Windows [tor-browser-buid]
     * Bug 29321: Use mingw-w64/clang toolchain to build tor [tor-browser-build]
     * Bug 29322: Use mingw-w64/clang toolchain to build OpenSSL [tor-browser-build]
     * Bug 40409: Upgrade NSIS to 3.08 [tor-browser-build]
     * Bug 40666: Fix compiler depedencies for Firefox on Windows [tor-browser-build]
   * macOS
     * Bug 40067: Rename "OS X" to "macOS" [tor-browser-build]
     * Bug 40158: Add support for macOS AArch64 [tor-browser-build]
     * Bug 40439: Create universal x86-64/arm64 mac builds [tor-browser-build]
     * Bug 40605: Reworked the macOS toolchain creation [tor-browser-build]
     * Bug 40620: Update macosx-sdk to 11.0 [tor-browser-build]
     * Bug 40687: macOS nightly builds with packaged locales fail [tor-browser-build]
     * Bug 40694: aarch64 tor-expert-bundle for macOS is not exported as part of the browser build [tor-browser-build]
   * Linux
     * Bug 31321: Add cc -> gcc link to projects/gcc [tor-browser-build]
     * Bug 40621: Update namecoin patches for linted TorButton [tor-browser-build]
     * Bug 40659: Error building goservice for linux in nightly build [tor-browser-build]
     * Bug 41343: Add -without-wam-sandboxed-libraries to mozconfig-linux-x86_64-dev for local builds [tor-browser]
   * Android
     * Bug 40574: Improve tools/signing/android-signing [tor-browser-build]
     * Bug 40604: Fix binutils build on android [tor-browser-build]
     * Bug 40640: Extract Gradle in the toolchain setup [tor-browser-build]
     * Bug 41304: Add Android-specific targets to makefiles [tor-browser]

Tor Browser 12.0a5 - November 30 2022
 * All Platforms
   * Update Translations
   * Update OpenSSL to 1.1.1s
   * Update NoScript to 11.4.13
   * Update tor to ********
   * Update zlib to 1.2.13
   * Bug 17228: Consideration for disabling/trimming referrers within TBB [tor-browser]
   * Bug 27258: font whitelist means we don't have to set gfx.downloadable_fonts.fallback_delay [tor-browser]
   * Bug 40183: Consider disabling TLS ciphersuites containing SHA-1 [tor-browser]
   * Bug 40622: Update obfs4proxy to 0.0.14 in Tor Browser [tor-browser-build]
   * Bug 40674: Add Secondary Snowflake Bridge [tor-browser-build]
   * Bug 40783: Review 000-tor-browser.js and 001-base-profile.js for 102 [tor-browser]
   * Bug 41406: Do not define `--without-wasm-sandboxed-libraries` if `WASI_SYSROOT` is defined [tor-browser]
   * Bug 41420: Remove brand.dtd customization on nightly [tor-browser]
   * Bug 41457: Remove more Mozilla permissions [tor-browser]
   * Bug 41460: Migrate new identity and security level preferences in 11.5.8 [tor-browser]
   * Bug 41473: Add support for Albanian (sq) [tor-browser]
 * Windows + macOS + Linux
   * Update Firefox to 102.5.0esr
   * Bug 31064: Letterboxing is enabled in priviledged contexts too [tor-browser]
   * Bug 31821: reapply window.open() clamping [tor-browser]
   * Bug 32411: Consider adding about:tor and others to the list of pages that do not need letterboxing [tor-browser]
   * Bug 40081: Letterboxing since 32220 affected by layout.css.devPixelsPerPx [tor-browser]
   * Bug 40767: 1px white border visible on fullscreen video playback [tor-browser]
   * Bug 41293: Incomplete branding in German with 12.0a2 [tor-browser]
   * Bug 41378: Inform users when Tor Browser sets their language automatically [tor-browser]
   * Bug 41409: Circuit display is broken on Tails [tor-browser]
   * Bug 41410: Opening and closing HTTPS-Only settings make the identity panel shrink [tor-browser]
   * Bug 41412: New Identity shows "Tor Browser" instead of "Restart Tor Browser" in unstranslated locales [tor-browser]
   * Bug 41417: Prompt users to restart after changing language [tor-browser]
   * Bug 41429: TorConnect and TorSettings are initialized twice [tor-browser]
   * Bug 41433: Should letterboxing take in account optional components' heights? [tor-browser]
   * Bug 41434: Letterboxing bypass through secondary tab (popup/popunder...) [tor-browser]
   * Bug 41436: The new tor-launcher handles arrays in the wrong way [tor-browser]
   * Bug 41437: Use the new media query for dark theme for the "Connected" pill in bridges [tor-browser]
   * Bug 41449: Onion authentication's learn more should link to the offline manual [tor-browser]
   * Bug 41451: Still using requestedLocales in torbutton [tor-browser]
   * Bug 41455: Tor Browser dev build cannot launch tor [tor-browser]
   * Bug 41458: Prevent `mach package-multi-locale` from actually creating a package [tor-browser]
   * Bug 41462: Add anchors to bridge-moji and onion authentication entries [tor-browser]
   * Bug 41498: The Help panel is empty in 12.0a4 [tor-browser]
 * Windows
   * Bug 41426: base-browser nightly fails to build for windows-i686 [tor-browser]
 * macOS
   * Bug 23451: Adapt font whitelist to changes on macOS (zh locales) [tor-browser]
   * Bug 40687: macOS nightly builds with packaged locales fail [tor-browser-build]
 * Android
   * Update GeckoView to 102.5.0esr
   * Bug 40014: Check which of our mobile prefs and configuration changes are still valid for GeckoView [tor-browser]
   * Bug 41471: Update targetSdkVersion to 31 [tor-browser]
   * Bug 41481: Tor Browser 11.5.9 for Android crashes on launch on Android 12+ after targetSdkVersion update [tor-browser]
 * Build System
   * All Platforms
     * Update Go to 1.19.3
     * Bug 40675: Update tb_builders list in set-config [tor-browser-build]
     * Bug 40667: Update Node.js to 12.22.12 [tor-browser-build]
     * Bug 40690: Revert fix for zlib build break [tor-browser-build]
     * Bug 41446: Multi-lingual alpha bundles break make fetch [tor-browser]
   * Windows + macOS + Linux
     * Bug 40503: Update Release Prep issue template with base-browser and privacy browser changes [tor-browser-build]
     * Bug 40641: Fetch Firefox locales from l10n-central [tor-browser-build]
     * Bug 40685: Remove targets/nightly/var/mar_locales from rbm.conf [tor-browser-build]
     * Bug 40686: Add a temporary project to fetch Fluent tranlations for base-browser [tor-browser-build]
     * Bug 40691: Update firefox config to point to base-browser branch rather than a particular tag in nightly [tor-browser-build]
     * Bug 40699: Fix input_files in projects/firefox-l10n/config [tor-browser-build]
   * Windows
     * Bug 40666: Fix compiler depedencies for Firefox on Windows [tor-browser-build]
   * macOS
     * Bug 40067: Rename "OS X" to "macOS" [tor-browser-build]
     * Bug 40439: Create universal x86-64/arm64 mac builds [tor-browser-build]

Tor Browser 11.5.10 - November 28 2022
 * Android
   * Update NoScript to 11.4.13
   * Bug 41481: Tor Browser 11.5.9 for Android crashes on launch on Android 12+ after targetSdkVersion update [tor-browser]

Tor Browser 11.5.9 - November 22 2022
 * Android
   * Bug 41471: Update targetSdkVersion to 31 [tor-browser]

Tor Browser 11.5.8 - November 18 2022
 * All Platforms
   * Update Translations
   * Update OpenSSL to 1.1.1s
   * Update NoScript to 11.4.12
   * Update tor to ********
   * Update zlib to 1.2.13
   * Bug 40622: Update obfs4proxy to 0.0.14 in Tor Browser [tor-browser-build]
  * Windows + macOS + Linux
   * Bug 31064: Letterboxing is enabled in priviledged contexts too [tor-browser]
   * Bug 31821: reapply window.open() clamping [tor-browser]
   * Bug 32411: Consider adding about:tor and others to the list of pages that do not need letterboxing [tor-browser]
   * Bug 40081: Letterboxing since 32220 affected by layout.css.devPixelsPerPx [tor-browser]
   * Bug 40767: 1px white border visible on fullscreen video playback [tor-browser]
   * Bug 41105: Tor Browser Does Not Clear CORS Preflight Cache despite creating a “New Identity” [tor-browser]
   * Bug 41413: Backup intl.locale.requested in 11.5.x [tor-browser]
   * Bug 41433: Should letterboxing take in account optional components' heights? [tor-browser]
   * Bug 41434: Letterboxing bypass through secondary tab (popup/popunder...) [tor-browser]
   * Bug 41456: Backport ESR 102.5 security fixes to 91.13-based Tor Browser [tor-browser]
   * Bug 41460: Migrate new identity and security level preferences in 11.5.8 [tor-browser]
   * Bug 41463: Backport fix for CVE-2022-43680 [tor-browser]
 * Android
   * Update GeckoView to 102.5.0esr
   * Bug 41461: Backport Android-specific 107-rr security fixes to 102.5-esr based Geckoview [tor-browser]
 * Build System
   * All Platforms
     * Update Go to 1.18.8
     * Bug 40658: Create an anticensorship team keyring [tor-browser-build]
     * Bug 40690: Revert fix for zlib build break [tor-browser-build]

Tor Browser 11.5.7 - November 2 2022
 * Windows + macOS + Linux
   * Bug 41413: Backup intl.locale.requested in 11.5.x [tor-browser]

Tor Browser 12.0a4 - October 28 2022
 * All Plaforms
   * Update Translations
   * Bug 24686: In Tor Browser context, should network.http.tailing.enabled be set to false? [tor-browser]
   * Bug 27127: Audit and enable HTTP/2 push [tor-browser]
   * Bug 40057: ensure that CSS4 system colors are not a fingerprinting vector [tor-browser]
   * Bug 40058: ensure no locale leaks from new Intl APIs [tor-browser]
   * Bug 40251: Clear obsolete prefs after torbutton!27 [tor-browser]
   * Bug 40406: Remove Presentation API related prefs [tor-browser]
   * Bug 40465: Onion Authentication fails when connecting to a subdomain [tor-browser]
   * Bug 40491: Don't auto-pick a v2 address when it's in Onion-Location header [tor-browser]
   * Bug 40494: Update Startpage and Blockchair onion search providers [tor-browser]
   * Bug 40629: Bump snowflake version to 9ce1de4eee4e [tor-browser-build]
   * Bug 40633: Remove Team Cymru hard-coded bridges [tor-browser-build]
   * Bug 40646: Don't build Español AR anymore [tor-browser-build]
   * Bug 40649: Update meek default bridge [tor-browser-build]
   * Bug 40654: Enable uTLS and use the full bridge line for snowflake [tor-browser-build]
   * Bug 40665: Snowflake bridge parameters are too long (535 bytes) in 11.5.5 [tor-browser-build]
   * Bug 41098: Compare Tor Browser's and GeckoView's profiles [tor-browser]
   * Bug 41154: Review Mozilla 1765167: Deprecate Cu.import [tor-browser]
   * Bug 41164: Add some #define for the base-browser section [tor-browser]
   * Bug 41306: Typo "will not able" in "Tor unexpectedly exited" dialog [tor-browser]
   * Bug 41317: Tor Browser leaks banned ports in network.security.ports.banned [tor-browser]
   * Bug 41326: Update preference for remoteRecipes [tor-browser]
   * Bug 41345: fonts: windows whitelist contains supplemental fonts [tor-browser]
   * Bug 41398: Disable Web MIDI API [tor-browser]
 * Windows + macOS + Linux
   * Update Firefox to 102.4.0esr
   * Bug 17400: Decide how to use the multi-lingual Tor Browser in the alpha/release series [tor-browser]
   * Bug 40638: Visit our website link after build-to-build upgrade in Nightly channel points to old v2 onion [tor-browser-build]
   * Bug 40648: Do not customize update.locale in multi-lingual builds [tor-browser-build]
   * Bug 40853: use Subprocess.jsm to launch tor [tor-browser]
   * Bug 40933: Migrate remaining tor-launcher functionality to tor-browser [tor-browser]
   * Bug 41117: Review Mozilla 1512851:  Add Share Menu to File Menu on macOS [tor-browser]
   * Bug 41323: Tor-ify notification bar gradient colors (branding) [tor-browser]
   * Bug 41337: Add a title to the new identity confirmation [tor-browser]
   * Bug 41342: Update the New Identity dialog to the proton modal style [tor-browser]
   * Bug 41344: Authenticated Onion Services do not prompt for auth key in 12.0 alpha series [tor-browser]
   * Bug 41352: Update or drop the show manual logic in torbutton [tor-browser]
   * Bug 41369: Consider a different list-order for locales in language menu [tor-browser]
   * Bug 41374: Missing a few torconnect strings in the DTD [tor-browser]
   * Bug 41377: Hide `Search for more languages...` from Language selector in multi-locale build [tor-browser]
   * Bug 41385: Bootstrap failure is logged but not raised up to about:torconnect [tor-browser]
   * Bug 41386: The new tor-launcher has a problem when another Tor is running [tor-browser]
   * Bug 41387: New identity and new circuit ended up inside history [tor-browser]
   * Bug 41400: Missing onionAuthViewKeys causes "Onion Services Keys" dialog to be empty. [tor-browser]
   * Bug 41401: Missing some mozilla icons because we still loading them from "chrome://browser/skin" rather than "chrome://global/skin/icons" [tor-browser]
   * Bug 41404: Fix blockchair-onion search extension [tor-browser]
 * Windows
   * Bug 41149: Review Mozilla 1762576:  Firefox is not allowing Symantec DLP to inject DLL into the browser for Data Loss Prevention software [tor-browser]
   * Bug 41278: Create Tor Browser styled pdf logo similar to the vanilla Firefox one [tor-browser]
 * macOS
   * Bug 41294: Bookmarks manager broken in 12.0a2 on MacOS [tor-browser]
   * Bug 41348: cherry-pick macOS OSSpinLock  replacements [tor-browser]
   * Bug 41370: Find a way to ship custom default bookmarks without changing language-packs on macOS [tor-browser]
   * Bug 41372: "Japanese" language menu item is localised in multi-locale testbuild (on mac OS) [tor-browser]
   * Bug 41379: The new tor-launcher is broken also on macOS [tor-browser]
 * Linux
   * Bug 40359: Tor Browser Launcher has Wrong Icon [tor-browser]
 * Android
   * Update GeckoView to 102.4.0esr
   * Bug 40631: Stop bundling HTTPS Everywhere on Android [tor-browser-build]
   * Bug 41360: Backport Android-specific Firefox 106 to ESR 102.4-based Tor Browser [tor-browser]
   * Bug 41394: Implement a setting to always prefer onion sites [tor-browser]
 * Build System
   * All Platforms
     * Update Go to 1.19.2
     * Bug 23656: Use .mozconfig files in tor-browser repo for rbm builds [tor-browser-build]
     * Bug 28754: make testbuild-android-armv7 stalls during sha256sum step [tor-browser-build]
     * Bug 40397: Create a new build target to package tor daemon, pluggable transports and dependencies [tor-browser-build]
     * Bug 40585: Prune the manual more [tor-browser-build]
     * Bug 40619: Make sure translations are taken from gitlab.tpo and not git.tpo [tor-browser-build]
     * Bug 40627: Add boklm to torbutton.gpg [torbrowser-build]
     * Bug 40634: Update the project/browser path in tools/changelog-format-blog-post and other files [tor-browser-build]
     * Bug 40636: Remove https-everywhere from projects/browser/config [tor-browser-build]
     * Bug 40639: Remove tor-launcher references [tor-browser-build]
     * Bug 40643: Update Richard's key in torbutton.gpg [tor-browser-build]
     * Bug 40655: Published tor-expert-bundle tar.gz files should not include their tor-browser-build build id [tor-browser-build]
     * Bug 40656: Improve get_last_build_version in tools/signing/nightly/sign-nightly [tor-browser-build]
     * Bug 40658: Create an anticensorship team keyring [tor-browser-build]
     * Bug 40660: Update changelog-format-blog-post script to point gitlab rather than gitolite [tor-browser-build]
     * Bug 40662: Make base-browser nightly build from tag [tor-browser-build]
     * Bug 40663: Do not ship bookmarks in tor-browser-build anymore [tor-browser-build]
     * Bug 40669: Remove HTTPS-Everywhere keyring [tor-browser-build]
     * Bug 40671: Update langpacks URL [tor-browser-build]
     * Bug 41308: Use the same branch for Desktop and GeckoView [tor-browser]
     * Bug 41340: Opt in to some of the NIGHTLY_BUILD features [tor-browser]
     * Bug 41343: Add -without-wam-sandboxed-libraries to mozconfig-linux-x86_64-dev for local builds [tor-browser]
     * Bug 41357: Enable browser toolbox debugging by default for dev builds [tor-browser]
   * macOS
     * Bug 40158: Add support for macOS AArch64 [tor-browser-build]
     * Bug 40464: go 1.18 fails to build on macOS [tor-browser-build]
   * Linux
     * Bug 40659: Error building goservice for linux in nightly build [tor-browser-build]
   * Android
     * Bug 40640: Extract Gradle in the toolchain setup [tor-browser-build]

Tor Browser 11.5.6 - October 25 2022
 * All Plaforms
   * Bug 40665: Shorten snowflake bridge line [tor-browser-build]

Tor Browser 11.5.5 - October 20 2022
 * All Platforms
   * Update Translations
   * Bug 40649: Update meek default bridge [tor-browser-build]
   * Bug 40654: Enable uTLS and use the full bridge line for snowflake [tor-browser-build]
 * Windows + macOS + Linux
   * Upate Manual
   * Bug 40465: Onion Authentication fails when connecting to a subdomain [tor-browser]
   * Bug 41355: Amends to YEC 2022 Takeover Desktop Stable 11.5.5 [tor-browser]
   * Bug 41359: Backport ESR 102.4 security fixes to 91.13-based Tor Browser [ tor-browser]
   * Bug 41364: Continued amends to YEC 2022 Takeover Desktop Stable 11.5.5 [tor-browser]
 * Android
   * Bug 40650: Rebase geckoview-102.3.0esr-11.5-1 to ESR 102.4 [tor-browser-build]
   * Bug 41360: Backport Android-specific Firefox 106 to ESR 102.4-based Tor Browser [tor-browser]
   * Bug 41365: Amends to YEC 2022 Takeover on Android [tor-browser]
 * Build System
   * Windows + macOS + Linux
     * Update Go to 1.18.7
     * Bug 40464: go 1.18 fails to build on macOS [tor-browser-build]

Tor Browser 11.5.4 - October 11 2022
 * All Platforms
   * Bug 40629: Bump snowflake version to 9ce1de4eee4e [tor-browser-build]
   * Bug 40633: Remove Team Cymru hard-coded bridges [tor-browser-build]
   * Bug 41326: Update preference for remoteRecipes [tor-browser]
 * Windows + macOS + Linux
   * Bug 40624: Change placeholder bridge addresses to make snowflake and meek work with ReachableAddresses/FascistFirewall [tor-browser-build]
   * Bug 41303: YEC 2022 Takeover for Desktop Stable [tor-browser]
   * Bug 41310: Backport ESR 102.3 security fixes to 91.13-based Tor Browser [tor-browser]
   * Bug 41323: Tor-ify notification bar gradient colors (branding) [tor-browser]
   * Bug 41338: The arrow on the search bar should be flipped for RTL languages [tor-browser]
 * Windows + macOS
   * Bug 41307: font whitelist typos [tor-browser]
 * Android
   * Updated GeckoView to 102.3.0esr
   * Bug 41089: Add tor-browser build scripts + Makefile to tor-browser [tor-browser]
   * Bug 41094: Enable HTTPS-Only Mode by default in Tor Browser Android [tor-browser]
   * Bug 41159: Remove HTTPS-Everywhere extension from esr102-based Tor Browser Android [tor-browser]
   * Bug 41166: Backport fix for CVE-2022-36317: Long URL would hang Firefox for Android (Bug 1759951) [tor-browser]
   * Bug 41167: Backport fix for CVE-2022-38474: Recording notification not shown when microphone was recording on Android (Bug 1719511) [tor-browser]
   * Bug 41302: YEC 2022 Takeover for Android Stable [tor-browser]
   * Bug 41312: Backport Firefox 105 Android security fixes to 102.3-based Tor Browser [tor-browser]
   * Bug 41314: Add YEC 2022 strings to torbutton and fenix [tor-browser]
 * Build System
   * Android
     * Update Go to 1.18.7

Tor Browser 12.0a3 - September 26 2022
 * All Platforms
   * Update Firefox to 102.3.0esr
   * Update NoScript to 11.4.11
   * Update Translations
   * Bug 40624: Change placeholder bridge addresses to make snowflake and meek work with ReachableAddresses/FascistFirewall [tor-browser-build]
   * Bug 41125: Review Mozilla 1732792: retry polling requests without proxy [tor-browser]
 * Windows + macOS + Linux
   * Bug 41116: Review Mozilla 1226042: add support for the new 'system-ui' generic font family [tor-browser]
   * Bug 41283: Toolbar buttons missing their label attribute [tor-browser]
   * Bug 41284: Stray security-level- fluent ids [tor-browser]
   * Bug 41287: New identity button inactive if added after customization [tor-browser]
   * Bug 41292: moreFromMozilla pane in about:preferences in 12.0a2 [tor-browser]
   * Bug 41307: font whitelist typos [tor-browser]
 * Linux
   * Bug 40626: Define the replacements for generic families on Linux [tor-browser-build]
   * Bug 41163: Fixing loading of bundled fonts on linux [tor-browser]
 * Android
   * Bug 41159: Remove HTTPS-Everywhere extension from esr102-based Tor Browser Android [tor-browser]
   * Bug 41312: Backport Firefox 105 Android security fixes to 102.3-based Tor Browser [tor-browser]
 * Build System
   * All Platforms
     * Bug 40587: Migrate tor-browser-build configs from gitolite to gitlab repos [tor-browser-build]
     * Bug 41321: Delete various master branches after automated build/testing scripts are updated [tor-browser]
   * Linux
     * Bug 40621: Update namecoin patches for linted TorButton [tor-browser-build]
   * Android
     * Bug 41304: Add Android-specific targets to makefiles [tor-browser]

Tor Browser 12.0a2 - September 1 2022
 * All Platforms
   * Update Firefox to 102.2.0esr
   * Update Tor to ********
   * Update NoScript to 11.4.10
   * Update Translations
   * Bug 40242: Tor Browser has two default bridges that share a fingerprint, and Tor ignores one [tor-browser]
 * Windows + macOS + Linux
   * Update Tor-Launcher to 0.2.39
   * Update Manual
   * Bug 40580: Add support for uk (ukranian) locale [tor-browser-build]
   * Bug 40595: Migrate to 102 on desktop [tor-browser-buid]
   * Bug 41075: The Tor Browser is showing caution sign but your document said it won't [tor-browser]
   * Bug 41089: Add tor-browser build scripts + Makefile to tor-browser [tor-browser]
 * macOS
   * Bug 41108: Remove privileged macOS installation from 102 [tor-browser]
 * Android
   * Bug 40225: Bundled extensions don't get updated with Android Tor Browser updates (they stay stuck at the first installed version) [fenix]
   * Bug 41094: Enable HTTPS-Only Mode by default in Tor Browser Android [tor-browser]
   * Bug 41156: User-installed addons are broken on Android [tor-browser]
   * Bug 41166: Backport fix for CVE-2022-36317: Long URL would hang Firefox for Android (Bug 1759951) [tor-browser]
   * Bug 41167: Backport fix for CVE-2022-38474: Recording notification not shown when microphone was recording on Android (Bug 1719511) [tor-browser]
 * Build System
   * All Platforms
     * Bug 40407: Bump binutils version to pick up security improvements for Windows users [tor-browser-build]
     * Bug 40591: Rust 1.60 not working to build 102 on Debian Jessie [tor-browser-build]
     * Bug 40592: Consider re-using our LLVM/Clang to build Rust [tor-browser-build]
     * Bug 40593: Update signing scripts to take into account new project names and layout [tor-browser-build]
     * Bug 40607: Add alpha-specific release prep template [tor-browser-build]
     * Bug 40610: src-*.tar.xz tarballs are missing in https://dist.torproject.org/torbrowser/12.0a1/ [tor-browser-build]
     * Bug 40612: Migrate Release Prep template to Release Prep - Stable [tor-browser-build]
   * Windows + macOS + Linux
     * Bug 41099: Update+comment the update channels in update_responses.config.yaml [tor-browser]
   * Windows
     * Bug 29318: Use Clang for everything on Windows [tor-browser-buid]
     * Bug 29321: Use mingw-w64/clang toolchain to build tor [tor-browser-build]
     * Bug 29322: Use mingw-w64/clang toolchain to build OpenSSL [tor-browser-build]
     * Bug 40409: Upgrade NSIS to 3.08 [tor-browser-build]
   * macOS
     * Bug 40605: Reworked the macOS toolchain creation [tor-browser-build]
     * Bug 40620: Update macosx-sdk to 11.0 [tor-browser-build]
   * Linux
     * Bug 31321: Add cc -> gcc link to projects/gcc [tor-browser-build]
   * Android
     * Update Go to 1.18.5
     * Bug 40574: Improve tools/signing/android-signing [tor-browser-build]
     * Bug 40582: Prepared TBA to use Mozilla 102 components [tor-browser-build]
     * Bug 40604: Fix binutils build on android [tor-browser-build]

Tor Browser 11.5.3 - August 29 2022
 * Android
   * Bug 40225: Bundled extensions don't get updated with Android Tor Browser
   updates (they stay stuck at the first installed version) [fenix]
   * Bug 41156: User-installed addons are broken on Android [tor-browser]

Tor Browser 11.5.2 - August 23 2022
 * All Platforms
   * Update Tor to ********
   * Update NoScript to 11.4.9
 * Windows + macOS + Linux
   * Update Firefox to 91.13.0esr
   * Bug 27719: Treat unsafe renegotiation as broken [tor-browser]
   * Bug 40242: Tor Browser has two default bridges that share a fingerprint, and Tor ignores one [tor-browser]
   * Bug 41067: Cherry-pick fixes for HTTPS-Only mode [tor-browser]
   * Bug 41070: Google vanished from default search options from 11.0.10 onwards [tor-browser]
   * Bug 41075: The Tor Browser is showing caution sign but your document said it won't [tor-browser]
   * Bug 41089: Add tor-browser build scripts + Makefile to tor-browser [tor-browser]
   * Bug 41095: "Learn more" link in onboarding slideshow still points to 11.0 release post [tor-browser]
 * Build System
   * Windows + macOS + Linux
     * Update Go to 1.17.3
   * Android
     * Update Go to 1.18.5

Tor Browser 12.0a1 - August 9 2022
 * Windows + OS X + Linux
   * Update Firefox to 91.12.0esr
   * Update Tor-Launcher to 0.2.38
   * Update Translations
   * Update OpenSSL to 1.1.1q
   * Update Manual
   * Bug 21740: Make sure Mozilla's own emoji font on Windows/Linux does not interfere with our font fingerprinting defense [tor-browser]
   * Bug 27719: Treat unsafe renegotiation as broken [tor-browser]
   * Bug 40584: Update tor-browser manual to latest [tor-browser-build]
   * Bug 40966: Investigate problems with Twemoji Mozilla [tor-browser]
   * Bug 41011: The Internet and Tor status are visible when opening the settings [tor-browser]
   * Bug 41035: OnionAliasService should use threadsafe ISupports [tor-browser]
   * Bug 41036: Add a preference to disable OnionAlias [tor-browser]
   * Bug 41037: User onboarding still points to about:preferences#tor [tor-browser]
   * Bug 41044: Content exceeding the height of the connection settings modals [tor-browser]
   * Bug 41049: QR codes in connection settings aren't recognized by some readers in dark theme [tor-browser]
   * Bug 41050: "Continue to HTTP Site" button doesn't work on IP addresses [tor-browser]
   * Bug 41053: remove HTTPS-Everywhere entry from browser.uiCustomization.state pref [tor-browser]
   * Bug 41054: Improve color contrast of purple elements in connection settings in dark theme [tor-browser]
   * Bug 41055: Icon fix from #40834 is missing in 11.5 stable [tor-browser]
   * Bug 41058: Hide `currentBridges` description when the section itself is hidden [tor-browser]
   * Bug 41059: Bridge cards aren't displaying, and toggle themselves off [tor-browser]
   * Bug 41067: Cherry-pick fixes for HTTPS-Only mode [tor-browser]
   * Bug 41070: Google vanished from default search options from 11.0.10 onwards [tor-browser]
   * Bug 41089: Add tor-browser build scripts + Makefile to tor-browser [tor-browser]
   * Bug 41095: "Learn more" link in onboarding slideshow still points to 11.0 release post [tor-browser]
 * Windows
   * Bug 30589: Tor Browser on Windows is lacking fonts to render some kind of scripts [tor-browser]
   * Bug 41039: Set 'startHidden' flag on tor process in tor-launcher [tor-browser]
 * OS X
   * Bug 41004: The Bangla font does not display correctly on MacOs [tor-browser]
 * Linux
   * Bug 41043: Investigate why STIX Two becomes the default font on Linux [tor-browser]
 * Build System
   * Windows + OS X + Linux
     * Update Go to 1.17.13
     * Bug 40499: Update firefox to enable building from new 'base-browser' tag [tor-browser-build]
     * Bug 40500: Add base-browser package project [tor-browser-build]
     * Bug 40501: Makefile updates to support building base-browser packages [tor-browser-build]
     * Bug 40547: Remove container/remote_* from rbm.conf [tor-browser-build]
     * Bug 40581: Update reference to master branches [tor-browser-build]

Tor Browser 11.5.1 - July 26 2022
 * Windows + OS X + Linux
   * Update Firefox to 91.12.0esr
   * Bug 41049: QR codes in connection settings aren't recognized by some readers in dark theme [tor-browser]
   * Bug 41050: "Continue to HTTP Site" button doesn't work on IP addresses [tor-browser]
   * Bug 41053: remove HTTPS-Everywhere entry from browser.uiCustomization.state pref [tor-browser]
   * Bug 41054: Improve color contrast of purple elements in connection settings in dark theme [tor-browser]
   * Bug 41055: Icon fix from #40834 is missing in 11.5 stable [tor-browser]
   * Bug 41058: Hide `currentBridges` description when the section itself is hidden [tor-browser]
   * Bug 41059: Bridge cards aren't displaying, and toggle themselves off [tor-browser]
 * Build System
   * Windows + OS X + Linux
     * Update Go to 1.17.12
     * Bug 40547: Remove container/remote_* from rbm.conf [tor-browser-build]
     * Bug 40584: Update tor-browser manual to latest [tor-browser-build]

Tor Browser 11.5 - July 7 2022
 * All Platforms
   * Update OpenSSL to 1.1.1q
 * Windows + OS X + Linux
   * Update Firefox to 91.11.0esr
   * Update Tor-Launcher to 0.2.37
   * Update Translations
   * Bug 11698: Incorporate Tor Browser Manual pages into Tor Browser [tor-browser]
   * Bug 19850: Disable Plaintext HTTP Clearnet Connections [tor-browser]
   * Bug 21740: Make sure Mozilla's own emoji font on Windows/Linux does not interfere with our font fingerprinting defense [tor-browser]
   * Bug 30589: Allowed fonts to render a bunch of missing scripts [tor-browser]
   * Bug 40458: Implement about:rulesets https-everywhere replacement [tor-browser]
   * Bug 40527: Remove https-everywhere from tor-browser alpha desktop [tor-browser-build]
   * Bug 40562: Reorganize patchset [tor-browser]
   * Bug 40598: Remove legacy settings read from TorSettings module [tor-browser]
   * Bug 40645: Migrate Moat APIs to Moat.jsm module [tor-browser]
   * Bug 40684: Misc UI bug fixes [tor-browser]
   * Bug 40773: Update the about:torconnect frontend page to match additional UI flows [tor-browser]
   * Bug 40774: Update about:preferences page to match new UI designs [tor-browser]
   * Bug 40775: about:ion should not be labeled as a Tor Browser page [tor-browser]
   * Bug 40793: moved Tor configuration options from old-configure.in to moz.configure [tor-browser]
   * Bug 40825: Redirect HTTPS-Only error page when not connected [tor-browser]
   * Bug 40912: Hide screenshots menu since we don't support it [tor-browser]
   * Bug 40916: Remove the browser.download.panel.shown preference [tor-browser]
   * Bug 40923: Consume country code to improve error report [tor-browser]
   * Bug 40966: Render emojis in bridgemoji with SVG files, and added emojii descriptions [tor-browser]
   * Bug 41011: Make sure the Tor Connection status is shown only in about:preferences#connection [tor-browser]
   * Bug 41023: Update manual URLs [tor-browser]
   * Bug 41035: OnionAliasService should use threadsafe ISupports [tor-browser]
   * Bug 41036: Add a preference to disable Onion Aliases [tor-browser]
   * Bug 41037: Fixed the connection preferences on the onboarding [tor-browser]
   * Bug 41039: Set 'startHidden' flag on tor process in tor-launcher [tor-browser]
 * OS X
   * Bug 40797: font-family: monospace renders incorrectly on macOS [tor-browser]
   * Bug 41004: Bundled fonts are not picked up on macOS [tor-browser]
 * Linux
   * Bug 41015: Add --name parameter to correctly setup WM_CLASS when running as native Wayland client [tor-browser]
   * Bug 41043: Hardcode the UI font on Linux [tor-browser]
 * Android
   * Update Fenix to 99.0.0b3
 * Build System
   * All Platforms
     * Bug 40288: Bump mmdebstrap version to 0.8.6 [tor-browser-build]
     * Bug 40426: Update Ubuntu base image to 22.04 [tor-browser-build]
     * Bug 40516: Remove aguestuser from tb_builders and torbutton.gpg [tor-browser-build]
     * Bug 40519: Add Alexis' latest PGP key to https-everywhere key ring [tor-browser-build]
   * Android
     * Update Go to 1.18.3
     * Bug 40433: Bump LLVM to 13.0.1 for android builds [tor-browser-build]
     * Bug 40470: Fix zlib build issue for android [tor-browser-build]
     * Bug 40485: Resolve Android reproducibility issues [tor-browser-build]
   * Windows + OS X + Linux
     * Bug 34451: Include Tor Browser Manual in packages during build [tor-browser-build]
     * Bug 40525: Update the mozconfig for tor-browser-91.9-11.5-2 [tor-browser-build]

Tor Browser 11.0.15 - June 19 2022
 * All Platforms
  * Update Tor to *******

Tor Browser 11.5a13 - June 18 2022
 * All Platforms
  * Update Tor to *******
  * Update NoScript to 11.4.6
  * Update translations
 * Windows + OS X + Linux
   * Update Firefox to 91.10.0esr
   * Update Tor-Launcher to 0.2.36
   * Bug 11698: Incorporate Tor Browser Manual pages into Tor Browser [tor-browser]
   * Bug 40458: Implement about:rulesets https-everywhere replacement [tor-browser]
   * Bug 40527: Remove https-everywhere from tor-browser alpha desktop [tor-browser-build]
   * Bug 40971: TB Alpha desktop minor issue Help button is not working [tor-browser]
   * Bug 41023: Update manual URLs [tor-browser]
 * Linux
   * Bug 41015: Add --name parameter to correctly setup WM_CLASS when running as native Wayland client [tor-browser]
 * Build System
   * All Platforms
     * Bug 40288: Bump mmdebstrap version to 0.8.6 [tor-browser-build]
     * Bug 40426: Update Ubuntu base image to 22.04 [tor-browser-build]
     * Bug 40497: Check that directory does not exist before starting macOS signing [tor-browser-build]
     * Bug 40508: ChangeLog in master is missing 11.0.x changelog stanzas [tor-browser-build]
     * Bug 40516: Remove aguestuser from tb_builders and torbutton.gpg [tor-browser-build]
     * Bug 40519: Add Alexis' latest PGP key to https-everywhere key ring [tor-browser-build]
     * Bug 40523: Add tor-announce to Release Prep template [tor-browser-build]
   * Android
     * Update Go to 1.18.3
   * Windows + OS X + Linux
     * Update Go to 1.17.10
     * Bug 34451: Include Tor Browser Manual in packages during build [tor-browser-build]
     * Bug 40525: Update the mozconfig for tor-browser-91.9-11.5-2 [tor-browser-build]

Tor Browser 11.0.14 - June 2 2022
 * All Platforms
   * Updated NoScript to 11.4.6
   * Bug 40474: Bump version of pion/webrtc to v3.1.41 [tor-browser-build]
 * Windows + OS X + Linux
   * Update Firefox to 91.10.0esr
 * Build System
   * All Platforms
     * Update Go to 1.17.11
     * Bug 40511: Changlog in stable is missing 11.5ax stanzas [tor-browser-build]

Tor Browser 11.5a12 - May 21 2022
 * Windows + OS X + Linux
   * Bug 40309: Avoid using regional OS locales [tor-browser]
   * Bug 40912: Hide screenshots menu since we don't support it [tor-browser]
   * Bug 40916: Remove the browser.download.panel.shown preference [tor-browser]
   * Bug 40918: In 11.5a11 the breadcrumbs are visible on the first bootstrap [tor-browser]
   * Bug 40923: Consume country code to improve error report [tor-browser]
 * All Platforms
   * Bug 40474: Bump version of pion/webrtc to v3.1.41 [tor-browser-build]
   * Bug 40967: Integrate Mozilla fix for Bug 1770137 [tor-browser]
   * Bug 40968: Integrate Mozilla fix for Bug 1770048 [tor-browser]
 * Build System
   * All Platforms
     * Bug 40476: Add tools/signing/do-all-signing script, and other signing scripts improvements [tor-browser-build]
   * Android
     * Update Go to 1.18.2
   * Windows + OS X + Linux
     * Update Go to 1.17.10

Tor Browser 11.0.13 - May 20 2022
 * Android
   * Bug 40212: Tor Browser crashing on launch [fenix]
 * All Platforms
   * Update Tor to *******
   * Bug 40967: Integrate Mozilla fix for Bug 1770137 [tor-browser]
   * Bug 40968: Integrate Mozilla fix for Bug 1770048 [tor-browser]
 * Build System
   * All Platforms
     * Update Go to 1.17.10
     * Bug 40319: Add build tag to downloads.json [tor-browser-build]
     * Bug 40476: Add tools/signing/do-all-signing script, and other signing scripts improvements [tor-browser-build]

Tor Browser 11.5a10 - May 20 2022
 * Android
    * Update Fenix to 99.0.0b3
    * Update NoScript to 11.4.5
    * Update Tor to *******
    * Update OpenSSL to 1.1.1o
    * Bug 40212: Tor Browser crashing on launch [fenix]
    * Bug 40433: Bump LLVM to 13.0.1 for android builds [tor-browser-build]
    * Bug 40469: Update zlib to 1.2.12 (CVE-2018-25032) [tor-browser-build]
    * Bug 40470: Fix zlib build issue for android [tor-browser-build]
    * Bug 40682: Set network.proxy.allow_bypass to false [tor-browser]
    * Bug 40830: cherry-picked Bugzilla 1760674 on GV 99 TBA 11.5 [tor-browser]
 * Build System
   * Android
     * Update Go to 1.18.1
     * Bug 40485: Resolve Android reproducibility issues [tor-browser-build]

 Tor Browser 11.0.12 - May 12 2022
  * Android
    * Update NoScript to 11.4.5
    * Update OpenSSL to 1.1.1o
    * Bug 40202: Install fails for nightly testbuild on emulator running Android API v30 [fenix]
    * Bug 40212: Tor Browser crashing on launch [fenix]
    * Bug 40908: Rebase to Geckoview v96.0.3 for TBA 11.0 [tor-browser]
    * Bug 40913: Investigate+possible revert fix for Bugzilla 1732388 [tor-browser]
  * Build System
    * Android
     * Update Go to 1.17.9

Tor Browser 11.5a11 - May 4 2022
 * Windows + OS X + Linux
   * Update Firefox to 91.9.0esr
   * Update NoScript to 11.4.5
   * Update Tor to *******
   * Update OpenSSL to 1.1.1o
   * Update Tor Launcher to 0.2.35
   * Bug 21484: Remove or hide "What's New" link from About dialog [tor-browser]
   * Bug 40482: Remove smallerrichard builtin bridge [tor-browser-build]
   * Bug 40886: Amend about:tor on Nightly and Alpha to encourage testing [tor-browser]
   * Bug 40887: Implement amends to torconnect and Connection Settings following 11.5a9 [tor-browser]
 * Build System
   * All Platforms
     * Bug 40319: Add build tag to downloads.json [tor-browser-build]

Tor Browser 11.0.11 - May 3 2022
 * Windows + OS X + Linux
   * Update Firefox to 91.9.0esr
   * Update NoScript to 11.4.5
   * Bug 21484: Remove or hide "What's New" link from About dialog [tor-browser]
   * Bug 34366: The onion-location mechanism does not redirect to full URL [tor-browser]
   * Bug 40482: Remove smallerrichard builtin bridge [tor-browser-build]
 * Build System
   * Windows + OS X + Linux
     * Update Go to 1.17.9

Tor Browser 11.5a9 - April 12 2022
 * Windows + OS X + Linux
   * Update Firefox to 91.8.0esr
   * Update NoScript to 11.4.4
   * Update Tor Launcher 0.2.34
   * Update Tor to *******-alpha
   * Bug 34366: The onion-location mechanism does not redirect to full URL [tor-browser]
   * Bug 40469: Update zlib to 1.2.12 (CVE-2018-25032) [tor-browser-build]
   * Bug 40773: Update the about:torconnect frontend page to match additional UI flows [tor-browser]
   * Bug 40774: Update about:preferences page to match new UI designs [tor-browser]
   * Bug 40822: Rebase tor-browser 11.5a9 to 91.8 Firefox [tor-browser]
   * Bug 40862: Backport 1760674 [tor-browser]

Tor Browser 11.0.10 - April 5 2022
 * Windows + OS X + Linux
   * Update Firefox to 91.8.0esr
   * Update NoScript to 11.4.3
   * Bug 14939: Support ipv6 address in Tor Circuit Display [tor-browser]
   * Bug 40469: Update zlib to 1.2.12 (CVE-2018-25032) [tor-browser-build]
   * Bug 40718: Application Menu items should be sentence case [tor-browser]
   * Bug 40725: about:torconnect missing identity block content on TB11 [tor-browser]
   * Bug 40776: Capitalize the "T" in "Tor" in application menu [tor-browser]
   * Bug 40862: Backport 1760674 [tor-browser]

Tor Browser 11.5a8 - March 15 2022
 * Windows + OS X + Linux
   * Bug 14939: Support ipv6 addresses in Tor Circuit Display [tor-browser]
   * Bug 40460: Upgrade to OpenSSL 1.1.1n [tor-browser]
   * Bug 40802: Client Auth dialog is broken in Tor Browser 11.06, works in 11.04 [tor-browser]
   * Bug 40830: cherry-pick fix for bugzilla 1758156

Tor Browser 11.0.9 - March 15 2022
 * Windows + OS X + Linux
   * Bug 40802: Client Auth dialog is broken in Tor Browser 11.0.6, works in 11.0.4 [tor-browser]
   * Bug 40831: Upgrade to OpenSSL 1.1.1n [tor-browser]
   * Bug 40830: cherry-pick fix for bugzilla 1758156 [tor-browser]
 * Build System
   * Windows + OS X + Linux
     * Update Go to 1.17.8
     * Bug 40422: Remove projects/ed25519 [tor-browser-build]
     * Bug 40431: added license info for edwards25519 and edwards25519-extra [tor-browser-build]
     * Bug 40436: Use intermediate files for default bridge lines [tor-browser-build]
     * Bug 40438: Use the same list of bridges for desktop and android [tor-browser-build]

Tor Browser 11.0.8 - March 11 2022
 * Android
   * Update Fenix to 96.3.0
   * Update Tor to ********
   * Update NoScript to 11.3.7
   * Bug 40075: Fix bug preventing downloads on Android Q and higher [android-components]
   * Bug 40829: Port fixes for Mozilla security advisory mfsa2022-09 to Android [tor-browser]
 * Build System
   * Android
     * Update Go to 1.16.15
     * Bug 40056: Download .aar and .jar files for all .pom files [tor-browser-build]
     * Bug 40298: Add documentation about subuid and subgid [tor-browser-build]
     * Bug 40385: Add android platform-31_r0 [tor-browser-build]
     * Bug 40422: Remove projects/ed25519 [tor-browser-build]
     * Bug 40431: added license info for edwards25519 and edwards25519-extra [tor-browser-build]
     * Bug 40432: Support bug reports by displaying Android Components commit hash in "About Tor Browser" display [tor-browser-build]
     * Bug 40436: Use intermediate files for default bridge lines [tor-browser-build]
     * Bug 40438: Use the same list of bridges for desktop and android [tor-browser-build]
     * Bug 40440: Bump version of snowflake to to include PT LOG events [tor-browser-build]
     * Bug 40441: Add aguestuser as valid git-tag signer [tor-browser-build]

Tor Browser 11.5a7 - March 9 2022
 * Android
   * Update NoScript to 11.3.7
   * Bug 40829: port fixes for Mozilla security advisory mfsa2022-09 to Android [tor-browser]
 * Build System
   * Android
   * Update Go to 1.17.8

Tor Browser 11.5a6 - March 8 2022
 * Windows + OS X + Linux
   * Update Firefox to 91.7.0esr
   * Update Tor to *******-alpha
   * Update NoScript to 11.3.7
   * Bug 19850: Disable Plaintext HTTP Clearnet Connections [tor-browser]
   * Bug 40440: Bump version of snowflake to include PT LOG events [tor-browser-build]
   * Bug 40819: Update Firefox to 91.7.0esr [tor-browser]
   * Bug 40824: Drop 16539 Patch (android screencasting disable) [tor-browser]
   * Bug 40825: Redirect HTTPS-Only error page when not connected [tor-browser]
   * Bug 40826: Cherry-pick fixes for Mozilla bug 1758062 [tor-browser]
 * Build System
   * Windows + OS X + Linux
     * Update Go to 1.17.8
     * Bug 40441: Add Austin as a valid git-tag signer [tor-browser-build]

Tor Browser 11.0.7 - March 8 2022
 * Windows + OS X + Linux
   * Update Firefox to 91.7.0esr
   * Update Tor to ********
   * Update NoScript to 11.3.7
   * Bug 40435: Remove default bridge "deusexmachina" [tor-browser-build]
   * Bug 40440: Bump version of snowflake to include PT LOG events [tor-browser-build]
   * Bug 40684: Misc UI bugs in 11.0a10 [tor-browser]
   * Bug 40687: Update message bar styling in about:preferences#tor [tor-browser]
   * Bug 40691: Make quickstart checkbox grey when "off" on about:torconnect [tor-browser]
   * Bug 40714: Next button closes "How do circuits work?" onboarding tour [tor-browser]
   * Bug 40824: Drop 16439 Patch (android screencasting disable)
   * Bug 40826: Cherry-pick fixes for Mozilla bug 1758062
 * Build System
   * Windows + OS X + Linux
    * Update Go to 1.16.14
    * Bug 40441: Add Austin as valid git-tag signer

Tor Browser 11.5a5 - March 1 2022
 * Android
   * Update Fenix to 96.3.0
   * Update Tor to *******-alpha
   * Update NoScript to 11.3.4
   * Bug 40075: Fix problem preventing file downloads on Android Q and above [android-components]
   * Bug 40432: Support bug reports by displaying Android Components commit hash in "About Tor Browser" display [tor-browser-build]
 * Build System
   * Android
     * Update Go to 1.17.7
     * Bug 40056: Download .aar and .jar files for all .pom files [tor-browser-build]
     * Bug 40385: Update components for mozilla95 [tor-browser-build]
     * Bug 40418: Update components for mozilla96 [tor-browser-build]
     * Bug 40435: Remove default obfs4 bridge "deusexmachina" [tor-browser-build]
     * Bug 40436: Use intermediate files for default bridge lines [tor-browser-build]
     * Bug 40438: Use the same list of bridges for desktop and android [tor-browser-build]
     * Bug 40441: Add Austin as a valid git-tag signer [tor-browser-build]

Tor Browser 11.5a4 - February 15 2022
 * Windows + OS X + Linux
   * Update Firefox to 91.6.0esr
   * Update NoScript to 11.2.19
   * Tor Launcher 0.2.33
   * Bug 40562: Reorganize patchset [tor-browser]
   * Bug 40598: Remove legacy settings read from TorSettings module [tor-browser]
   * Bug 40679: Missing features on first-time launch in esr91 [tor-browser]
   * Added extensions.torlauncher.launch_delay debug pref to simulate slow tor daemon launch [tor-launcher]
   * Bug 40752: Misleading UX when about:tor as New Tab [tor-browser]
   * Bug 40775: about:ion should no tbe labeled as a Tor Browser page [tor-browser]
   * Bug 40793: moved Tor configuration options from old-configure.in to moz.configure [tor-browser]
   * Bug 40795: Revert Deutsche Welle v2 redirect [tor-browser]
 * Build System
   * Windows + OS X + Linux
     * Update Go to 1.17.6
     * Bug 40416: Pick up obfsproxy 0.0.12 [tor-browser-build]

Tor Browser 11.0.6 - February 8 2022
 * Windows + OS X + Linux
   * Update Firefox to 91.6.0esr
   * Update NoScript to 11.2.16
   * Update OpenSSL to 1.1.1m
   * Tor Launcher 0.2.33
   * Bug 40795: Revert Deutsche Welle v2 redirect [tor-browser]
   * Bug 40679: Missing features on first-time launch in esr91 [tor-browser]
   * Added extensions.torlauncher.launch_delay debug pref to simulate slow tor daemon launch [tor-launcher]
  * Build System
   * Windows + OS X + Linux + Android
    * Update Go to 1.16.13
    * Bug 40413: Removed lsb_release from Python build script [tor-browser-build]
    * Bug 40416: Pick up obfsproxy 0.0.12 [tor-browser-build]

Tor Browser 11.0.5 - February 3 2022
 * Android
   * Update Fenix to 94.1.1
   * Update Tor to *******
   * Update NoScript to 11.2.16
   * Update OpenSSL to 1.1.1m
   * Bug 40006: Add new default obfs4 bridge "deusexmachina" [tor-android-service]
   * Bug 40198: Spoof English toggle now overlaps with locale list [fenix]
   * Bug 40393: Point to a forked version of pion/dtls with fingerprinting fix [tor-browser-build]
   * Bug 40394: Bump version of Snowflake to 221f1c41 [tor-browser-build]
   * Bug 40398: Jetify tor-android-service packages [tor-browser-build]
   * Bug 40682: Disable network.proxy.allow_bypass [tor-browser]
   * Bug 40736: Disable third-party cookies in Private Browsing Mode [tor-browser]
 * Build System
   * Android
     * Bug 40366: Use bullseye to build https-everywhere [tor-browser-build]
     * Bug 40368: Use system's python3 for android builds [tor-browser-build]
     * Bug 40373: Update components for mozilla93 [tor-browser-build]
     * Bug 40379: Update components for mozilla94 [tor-browser-build]
     * Bug 40395: Update node to 12.22.1 [tor-browser-build]
     * Bug 40403: Update Go to 1.16.12 [tor-browser-build]

Tor Browser 11.5a3 - January 14 2022
 * Android
   * Update Fenix to 94.1.1
   * Update NoScript to 11.2.14
   * Update OpenSSL to 1.1.1m
   * Update Tor to *******-alpha
   * Bug 40070: Temporarily redirect DW's v2 address to their new v3 address [android-components]
   * Bug 40198: Spoof English toggle now overlaps with locale list [fenix]
   * Bug 40393: Point to a forked version of pion/dtls with fingerprinting fix [tor-browser-build]
   * Bug 40394: Bump version of Snowflake to 221f1c41 [tor-browser-build]
   * Bug 40398: Jetify tor-android-service packages [tor-browser-build]
   * Bug 40682: Disable network.proxy.allow_bypass [tor-browser]
   * Bug 40736: Disable third-party cookies in Private Browsing Mode [tor-browser]
 * Build System
   * Android
     * Bug 40345: Update Go to 1.17.5 [tor-browser-build]
     * Bug 40366: Use bullseye to build https-everywhere [tor-browser-build]
     * Bug 40368: Use system's python3 for android builds [tor-browser-build]
     * Bug 40373: Update components for mozilla93 [tor-browser-build]
     * Bug 40379: Update components for mozilla94 [tor-browser-build]
     * Bug 40395: Update node to 12.22.1 [tor-browser-build]
     * Bug 40413: Removed lsb_release from Python build script [tor-browser-build]

Tor Browser 11.5a2 - January 11 2022
 * Windows + OS X + Linux
   * Update Firefox to 91.5.0esr
   * Update Tor to *******-alpha
   * Update OpenSSL to 1.1.1m
   * Update NoScript to 11.2.14
   * Bug 40405: Rotate deusexmachina IP address [tor-browser-build]
   * Bug 40645: Migrate Moat APIs to Moat.jsm module [tor-browser]
   * Bug 40684: Misc UI bug fixes [tor-browser]
   * Bug 40736: Disable third-party cookies in Private Browsing Mode [tor-browser]
   * Bug 40756: Fix up wrong observer removals [tor-browser]
   * Bug 40758: Remove YEC takeover from about:tor [torbutton]
   * Translations update
 * Windows
   * Bug 40742: Remove workaround for fixing --disable-maintenance-service build bustage [tor-browser]
   * Bug 40753: Revert fix for Youtube videos not playing on Windows [tor-browser]
 * Linux
   * Bug 40387: Fonts of the GUI do not render after update [tor-browser-build]
   * Bug 40399: Bring back Noto Sans Gurmukhi and Sinhala fonts [tor-browser-build]
   * Bug 40685: Monospace font in browser chrome [tor-browser]
 * Build System
   * Windows + OS X + Linux
     * Bug 40345: Update Go to 1.17.5 [tor-browser-build]
     * Bug 40395: Update node to 12.22.1 [tor-browser-build]
   * OS X
     * Bug 40390: Remove workaround for macOS OpenSSL build breakage [tor-browser-build]

Tor Browser 11.0.4 - January 11 2022
 * Windows + OS X + Linux
   * Update Firefox to 91.5.0esr
   * Update NoScript to 11.2.14
   * Bug 40405: Rotate deusexmachina IP address [tor-browser-build]
   * Bug 40756: Fix up wrong observer removals [tor-browser]
   * Bug 40758: Remove YEC takeover from about:tor [torbutton]
 * Linux
   * Bug 40399: Bring back Noto Sans Gurmukhi and Sinhala fonts [tor-browser-build]

Tor Browser 11.0.3 - December 20 2021
 * Windows + OS X + Linux
   * Update Firefox to 91.4.1esr
   * Update Tor to *******
   * Update OpenSSL to 1.1.1m
   * Bug 40393: Point to a forked version of pion/dtls with fingerprinting fix [tor-browser-build]
   * Bug 40394: Bump version of Snowflake to 221f1c41 [tor-browser-build]
   * Bug 40646: Revert tor-browser#40475 and inherit upstream fix [tor-browser]
   * Bug 40705: "visit our website" link on about:tbupdate pointing to different locations [tor-browser]
   * Bug 40736: Disable third-party cookies in Private Browsing Mode [tor-browser]
 * Windows
   * Bug 40389: Remove workaround for HTTPS-Everywhere WASM breakage [tor-browser-build]
   * Bug 40698: Addon menus missing content in TB11 [tor-browser]
   * Bug 40706: Fix issue in HTTPS-Everywhere WASM [tor-browser]
   * Bug 40721: Tabs crashing on certain pages in TB11 on Win 10 [tor-browser]
   * Bug 40742: Remove workaround for fixing --disable-maintenance-service build bustage [tor-browser]
 * Linux
   * Bug 40387: Fonts of the GUI do not render after update [tor-browser-build]
   * Bug 40685: Monospace font in browser chrome [tor-browser]
 * Build System
   * Windows + OS X + Linux
     * Bug 40403: Update Go to 1.16.12 [tor-browser-build]
   * OS X
     * Bug 40390: Remove workaround for macOS OpenSSL build breakage [tor-browser-build]

Tor Browser 11.5a1 - December 13 2021
 * Windows + OS X + Linux
   * Update Firefox to 91.4.0esr
   * Tor Launcher 0.2.32
   * Bug 40059: YEC activist sign empty in about:tor on RTL locales [torbutton]
   * Bug 40386: Add new default obfs4 bridge "deusexmachina" [tor-browser-build]
   * Bug 40393: Point to a forked version of pion/dtls with fingerprinting fix [tor-browser-build]
   * Bug 40394: Bump version of Snowflake to 221f1c41 [tor-browser-build]
   * Bug 40438: Add Blockchair as a search engine [tor-browser]
   * Bug 40646: Revert tor-browser#40475 and inherit upstream fix [tor-browser]
   * Bug 40680: Prepare update to localized assets for YEC [tor-browser]
   * Bug 40682: Disable network.proxy.allow_bypass [tor-browser]
   * Bug 40684: Misc UI bug fixes in 11.0a10 [tor-browser]
   * Bug 40686: Update Onboarding link for 11.0 [tor-browser]
   * Bug 40689: Change Blockchair Search provider's HTTP method [tor-browser]
   * Bug 40690: Browser chrome breaks when private browsing mode is turned off [tor-browser]
   * Bug 40691: Make quickstart checkbox gray when "off" on about:torconnect [tor-browser]
   * Bug 40700: Switch Firefox recommendations off by default [tor-browser]
   * Bug 40705: "visit our website" link on about:tbupdate pointing to different locations [tor-browser]
   * Bug 40714: Next button closes "How do circuits work?" onboarding tour [tor-browser]
   * Bug 40718: Application Menu items should be sentence case [tor-browser]
   * Bug 40725: about:torconnect missing identity block content on TB11 [tor-browser]
   * Translations update
 * Windows
   * Bug 40389: Remove workaround for HTTPS-Everywhere WASM breakage [tor-browser-build]
   * Bug 40698: Addon menus missing content in TB11 [tor-browser]
   * Bug 40706: Fix issue in HTTPS-Everywhere WASM [tor-browser]
   * Bug 40721: Tabs crashing on certain pages in TB11 on Win 10 [tor-browser]
   * Bug 40668: Document is freezing with file: scheme [tor-browser]
 * Linux
   * Bug 40318: Remove check for DISPLAY env var in start-tor-browser [tor-browser-build]
   * Bug 40387: Remove some fonts on Linux [tor-browser-build]

Tor Browser 11.0.2 - December 7 2021
 * Windows + OS X + Linux
   * Update Firefox to 91.4.0esr
   * Bug 40318: Remove check for DISPLAY env var in start-tor-browser [tor-browser-build]
   * Bug 40386: Add new default obfs4 bridge "deusexmachina" [tor-browser-build]
   * Bug 40682: Disable network.proxy.allow_bypass [tor-browser]
 * Linux
   * Bug 40387: Remove some fonts on Linux [tor-browser-build]

Tor Browser 11.0.1 - November 14 2021
 * Windows + OS X + Linux
   * Tor Launcher 0.2.32
   * Bug 40059: YEC activist sign empty in about:tor on RTL locales [torbutton]
   * Bug 40438: Add Blockchair as a search engine [tor-browser]
   * Bug 40689: Change Blockchair Search provider's HTTP method [tor-browser]
   * Bug 40690: Browser chrome breaks when private browsing mode is turned off [tor-browser]
   * Bug 40700: Switch Firefox recommendations off by default [tor-browser]
 * Windows
   * Bug 40383: Workaround issue in https-e wasm [tor-browser-build]
   * Bug 40668: Document is freezing with file: scheme [tor-browser]

Tor Browser 11.0 - November 8 2021
 * Windows + OS X + Linux
   * Update Firefox to 91.3.0esr
   * Update Tor to tor-0.4.6.8
   * Bug 27845: New window height is aftected by bookmarks toolbar with icons [tor-browser]
   * Bug 32624: localStorage is not shared between tabs [tor-browser]
   * Bug 33125: Remove xpinstall.whitelist.add* as they don't do anything anymore [tor-browser]
   * Bug 34188: Cleanup extensions.* prefs [tor-browser]
   * Bug 40004: Convert tl-protocol to async. [tor-launcher]
   * Bug 40012: Watch all requested tor events [tor-launcher]
   * Bug 40027: Make torbutton_send_ctrl_cmd async [torbutton]
   * Bug 40042: Add missing parameter of createTransport [torbutton]
   * Bug 40043: Delete all plugin-related protections [torbutton]
   * Bug 40045: Teach the controller about status_client [torbutton]
   * Bug 40046: Support arbitrary watch events [torbutton]
   * Bug 40047: New string for Security Level panel [torbutton]
   * Bug 40048: Protonify Circuit Display Panel [torbutton]
   * Bug 40053: investigate fingerprinting potential of extended TextMetrics interface [tor-browser]
   * Bug 40057: Error popup when using New Identity feature [torbutton]
   * Bug 40083: Make sure Region.jsm fetching is disabled [tor-browser]
   * Bug 40177: Clean up obsolete preferences in our 000-tor-browser.js [tor-browser]
   * Bug 40220: Make sure tracker cookie purging is disabled [tor-browser]
   * Bug 40342: Set `gfx.bundled-fonts.activate = 1` to preserve current bundled fonts behaviour [tor-browser]
   * Bug 40463: Disable network.http.windows10-sso.enabled in FF 91 [tor-browser]
   * Bug 40483: Deutsche Welle v2 redirect [tor-browser]
   * Bug 40534: Cannot open URLs on command line with Tor Browser 10.5 [tor-browser]
   * Bug 40547: UX: starting in offline mode can result in difficulty to connect later [tor-browser]
   * Bug 40548: Set network.proxy.failover_direct to false in FF 91 [tor-browser]
   * Bug 40561: Refactor about:torconnect implementation [tor-browser]
   * Bug 40567: RFPHelper is not init until after about:torconnect bootstraps [tor-browser]
   * Bug 40597: Implement TorSettings module [tor-browser]
   * Bug 40600: Multiple pages as home page unreliable in 11.0a4 [tor-browser]
   * Bug 40616: UX: multiple about:torconnect [tor-browser]
   * Bug 40624: TorConnect banner always visible in about:preferences#tor even after bootstrap [tor-browser]
   * Bug 40626: Update Security Level styling to match Proton UI [tor-browser]
   * Bug 40628: Checkbox wrong color in about:torconnect in dark mode theme [tor-browser]
   * Bug 40630: Update New Identity and New Circuit icons [tor-browser]
   * Bug 40631: site identity icons are not being displayed properly [tor-browser]
   * Bug 40632: Proton'ify Circuit Display Panel [tor-browser]
   * Bug 40634: Style updates for Onion Error Pages [tor-browser]
   * Bug 40636: Fix about:torconnect 'Connect' border radius in about:preferences#tor [tor-browser]
   * Bug 40641: Update Security Level selection in about:preferences to match style as tracking protection option bubbles [tor-browser]
   * Bug 40648: Replace onion pattern divs/css with tiling SVG [tor-browser]
   * Bug 40653: Onion Available text not aligned correctly in toolbar in ESR91 [tor-browser]
   * Bug 40655: esr91 is suggesting to make Tor Browser the default browser [tor-browser]
   * Bug 40657: esr91 is missing "New identity" in hamburger menu [tor-browser]
   * Bug 40680: Prepare update to localized assets for YEC [tor-browser]
   * Bug 40684: Misc UI bug fixes in 11.0a10
   * Bug 40686: Update Onboarding link for 11.0 [tor-browser]
 * Build System
   * Windows + OS X + Linux
     * Update Go to 1.16.9
     * Bug 40048: Remove projects/clang-source [tor-browser-build]
     * Bug 40347: Make the list of toolchain updates needed for firefox91 [tor-browser-build]
     * Bug 40363: Change bsaes git url [tor-browser-build]
     * Bug 40366: Use bullseye to build https-everywhere [tor-browser-build]
     * Bug 40368: Use system's python3 for https-everywhere [tor-browser-build]
   * Windows + Linux
     * Bug 40357: Update binutils to 2.35.2 [tor-browser-build]
   * Windows
     * Bug 28240: Switch from SJLJ exception handling to Dwarf2 in mingw for win32 [tor-browser-build]
     * Bug 40306: Update Windows toolchain to switch to mozilla91 [tor-browser-build]
     * Bug 40376: Use python3 for running pe_checksum_fix.py [tor-browser-build]
   * OS X
     * Bug 40307: Update macOS toolchain to switch to mozilla91 [tor-browser-build]
   * Linux
     * Bug 40222: Bump GCC to 10.3.0 for Linux [tor-browser-build]
     * Bug 40305: Update Linux toolchain to switch to mozilla91 [tor-browser-build]
     * Bug 40353: Temporarily disable rlbox for linux builds [tor-browser-build]

Tor Browser 11.0a10 - November 4 2021
 * Windows + OS X + Linux
   * Update Firefox to 91.3.0esr
   * Update Tor to 0.4.7.2-alpha
   * Bug 32624: localStorage is not shared between tabs [tor-browser]
   * Bug 33125: Remove xpinstall.whitelist.add* as they don't do anything anymore [tor-browser]
   * Bug 34188: Cleanup extensions.* prefs [tor-browser]
   * Bug 40053: investigate fingerprinting potential of extended TextMetrics interface [tor-browser]
   * Bug 40057: Error popup when using New Identity feature [torbutton]
   * Bug 40083: Make sure Region.jsm fetching is disabled [tor-browser]
   * Bug 40177: Clean up obsolete preferences in our 000-tor-browser.js [tor-browser]
   * Bug 40220: Make sure tracker cookie purging is disabled [tor-browser]
   * Bug 40342: Set `gfx.bundled-fonts.activate = 1` to preserve current bundled fonts behaviour [tor-browser]
   * Bug 40463: Disable network.http.windows10-sso.enabled in FF 91 [tor-browser]
   * Bug 40483: Deutsche Welle v2 redirect [tor-browser]
   * Bug 40548: Set network.proxy.failover_direct to false in FF 91 [tor-browser]
   * Bug 40630: New Identity and New Circuit icons [tor-browser]
   * Bug 40641: Update Security Level selection in about:preferences to match style as tracking protection option bubbles [tor-browser]
   * Bug 40648: Replace onion pattern divs/css with tiling SVG [tor-browser]
   * Bug 40653: Onion Available text not aligned correctly in toolbar in ESR91 [tor-browser]
   * Bug 40655: esr91 is suggesting to make Tor Browser the default browse [tor-browser]
   * Bug 40657: esr91 is missing "New identity" in hamburger menu [tor-browser]
   * Bug 40680: Prepare update to localized assets for YEC [tor-browser]
 * Build System
   * Windows + OS X + Linux
     * Bug 40366: Use bullseye to build https-everywhere [tor-browser-build]
     * Bug 40368: Use system's python3 for https-everywhere [tor-browser-build]

Tor Browser 10.5.10 -- October 23 2021
 * All Platforms
   * Translations Update
 * Windows + OS X + Linux
   * Revert bug 40049 [torbutton]
   * Bug 40051: Implement 2021 Year End Campaign look in about:tor [torbutton]
 * Android
   * Revert bug 40193 [fenix]
   * Bug 40194: Add 2021 YEC home screen [fenix]

Tor Browser 11.0a9 -- October 15 2021
 * Windows + OS X + Linux
   * Update Firefox to 91.2.0esr
   * Update Tor to 0.4.7.1-alpha
   * Bug 27845: New window height is aftected by bookmarks toolbar with icons [tor-browser]
   * Bug 40004: Convert tl-protocol to async. [tor-launcher]
   * Bug 40012: Watch all requested tor events [tor-launcher]
   * Bug 40027: Make torbutton_send_ctrl_cmd async [torbutton]
   * Bug 40042: Add missing parameter of createTransport [torbutton]
   * Bug 40043: Delete all plugin-related protections [torbutton]
   * Bug 40045: Teach the controller about status_client [torbutton]
   * Bug 40046: Support arbitrary watch events [torbutton]
   * Bug 40047: New string for Security Level panel [torbutton]
   * Bug 40048: Protonify Circuit Display Panel [torbutton]
   * Bug 40600: Multiple pages as home page unreliable in 11.0a4 [tor-browser]
   * Bug 40616: UX: multiple about:torconnect [tor-browser]
   * Bug 40624: TorConnect banner always visible in about:preferences#tor even after bootstrap [tor-browser]
   * Bug 40626: Update Security Level styling to match Proton UI [tor-browser]
   * Bug 40628: Checkbox wrong color in about:torconnect in dark mode theme [tor-browser]
   * Bug 40630: Update New Identity and New Circuit icons [tor-browser]
   * Bug 40631: site identity icons are not being displayed properly [tor-browser]
   * Bug 40632: Proton'ify Circuit Display Panel [tor-browser]
   * Bug 40634: Style updates for Onion Error Pages [tor-browser]
   * Bug 40636: Fix about:torconnect 'Connect' border radius in about:preferences#tor [tor-browser]
 * Build System
   * Windows + OS X + Linux
     * Update Go to 1.16.9
     * Bug 40048: Remove projects/clang-source [tor-browser-build]
     * Bug 40347: Make the list of toolchain updates needed for firefox91 [tor-browser-build]
     * Bug 40363: Change bsaes git url [tor-browser-build]
   * Windows + Linux
     * Bug 40357: Update binutils to 2.35.2 [tor-browser-build]
   * Windows
     * Bug 28240: switch from SJLJ exception handling to Dwarf2 in mingw for win32 [tor-browser-build]
     * Bug 40306: Update Windows toolchain to switch to mozilla91 [tor-browser-build]
     * Bug 40376: Use python3 for running pe_checksum_fix.py [tor-browser-build]
   * OS X
     * Bug 40307: Update macOS toolchain to switch to mozilla91 [tor-browser-build]
   * Linux
     * Bug 40222: Bump GCC to 10.3.0 for Linux [tor-browser-build]
     * Bug 40305: Update Linux toolchain to switch to mozilla91 [tor-browser-build]
     * Bug 40353: Temporarily disable rlbox for linux builds [tor-browser-build]

Tor Browser 11.0a8 -- October 10 2021
 * Android
    * Bug 40052: Skip L10nRegistry source registration on Android [torbutton]

Tor Browser 10.5.9 -- October 9 2021
 * Android
   * Bug 40052: Skip L10nRegistry source registration on Android [torbutton]

Tor Browser 10.5.8 -- October 5 2021
 * Windows + OS X + Linux
   * Update Firefox to 78.15.0esr
   * Bug 40049: Add banner for VPN survey to about:tor [torbutton]
 * Android
   * Bug 40193: Add banner for VPN survey to Android homepage [fenix]
 * Build System
   * All Platforms
     * Bug 40363: Change bsaes git url [tor-browser-build]

Tor Browser 10.5.7 -- September 24 2021
 * Android
   * Update Openssl to 1.1.1l
   * Bug 40639: Rebase geckoview patches onto 92.0 build3 [tor-browser]

Tor Browser 11.0a7 -- September 10 2021
 * All Platforms
   * Update Openssl to 1.1.1l
 * Windows + OS X + Linux
   * Update Firefox to 78.14.0esr
   * Bug 40597: Implement TorSettings module [tor-browser]
 * Android
   * Bug 40611: Rebase geckoview patches onto 92.0 [tor-browser]
* OS X
   * Bug 40358: Make OpenSSL 1.1.1l buildable for macOS [tor-browser-build]

Tor Browser 10.5.6 -- September 7 2021
 * Windows + OS X + Linux
   * Update Openssl to 1.1.1l
   * Update Firefox to 78.14.0esr
 * Build System
   * OS X
     * Bug 40358: Make OpenSSL 1.1.1l buildable for macOS [tor-browser-build]

Tor Browser 11.0a6 -- September 2 2021
 * Android
   * Bug 40611: Rebase geckoview patches onto 92.0b9 [tor-browser]

Tor Browser 11.0a5 -- August 22 2021
 * All Platforms
   * Update Tor to *******
 * Linux
   * Bug 40582: Tor Browser 10.5.2 tabs always crash on Fedora Xfce Rawhide [tor-browser]
 * Android
   * Update Fenix to 91.2.0

Tor Browser 10.5.5 -- August 18 2021
 * All Platforms
   * Update Tor to ********
 * Linux
   * Bug 40582: Tor Browser 10.5.2 tabs always crash on Fedora Xfce Rawhide [tor-browser]
 * Android
   * Update Fenix to 91.2.0
   * Update NoScript to 11.2.11
   * Bug 40063: Move custom search providers [android-components]
   * Bug 40176: TBA: sometimes I only see the banner and can't tap on the address bar [fenix]
   * Bug 40181: Remove V2 Deprecation banner on about:tor for Android [fenix]
   * Bug 40184: Rebase fenix patches to fenix v91.0.0-beta.5 [fenix]
   * Bug 40185: Use NimbusDisabled [fenix]
   * Bug 40186: Hide Credit Cards in Settings [fenix]
 * Build System
   * Android
     * Update Go to 1.15.15
     * Bug 40331: Update components for mozilla91 [tor-browser-build]

Tor Browser 11.0a4 -- August 11 2021
 * All Platforms
 * Windows + OS X + Linux
   * Update Firefox to 78.13.0esr
   * Bug 40041: Remove V2 Deprecation banner on about:tor for desktop [torbutton]
   * Bug 40534: Cannot open URLs on command line with Tor Browser 10.5 [tor-browser]
   * Bug 40547: UX: starting in offline mode can result in difficulty to connect later [tor-browser]
   * Bug 40561: Refactor about:torconnect implementation [tor-browser]
   * Bug 40567: RFPHelper is not init until after about:torconnect bootstraps [tor-browser]
 * Android
   * Update Fenix to 91.1.0
   * Bug 40186: Hide Credit Cards in Settings [fenix]
 * Build System
   * All Platforms
     * Update Go to 1.16.7

Tor Browser 10.5.4 -- August 10 2021
 * Windows + OS X + Linux
   * Update Firefox to 78.13.0esr
   * Update NoScript to 11.2.11
   * Bug 40041: Remove V2 Deprecation banner on about:tor for desktop [torbutton]
   * Bug 40506: Saved Logins not available in 10.5 [tor-browser]
   * Bug 40524: Update DuckDuckGo onion site URL in search preferences and onboarding [tor-browser]
 * Build System
   * Windows + OS X + Linux
     * Update Go to 1.15.14

Tor Browser 11.0a3 -- August 5 2021
 * Android
   * Update NoScript to 11.2.11
   * Bug 40176: TBA: sometimes I only see the banner and can't tap on the address bar [fenix]
   * Bug 40185: Use NimbusDisabled [fenix]
   * Bug 40181: Remove V2 Deprecation banner on about:tor for Android [fenix]
   * Bug 40184: Rebase fenix patches to fenix v91.0.0-beta.5 [fenix]
   * Bug 40063: Move custom search providers [android-components]
 * Build System
   * Android
     * Bug 40331: Update components for mozilla91 [tor-browser-build]

Tor Browser 11.0a2 -- July 19 2021
 * All Platforms
   * Update HTTPS Everywhere to 2021.7.13
 * Windows + OS X + Linux
   * Update Firefox to 78.12.0esr
   * Bug 40497: Cannot set multiple pages as home pages in 10.5a17 [tor-browser]
   * Bug 40506: Saved Logins not available in 10.5 [tor-browser]
   * Bug 40507: Full update is not downloaded after applying partial update fails [tor-browser]
   * Bug 40510: open tabs get redirected to about:torconnect on restart [tor-browser]
   * Bug 40524: Update DuckDuckGo onion site URL in search preferences and onboarding [tor-browser]
 * Android
   * Update Fenix to 90.1.1
   * Bug 40062: Update DuckDuckGo onion search plugin [android-components]
   * Bug 40177: Hide Tor icons in settings [fenix]
 * Build System
   * All Platforms
     * Update Go to 1.16.6

Tor Browser 10.5.3 -- July 17 2021
 * Android
   * Update HTTPS Everywhere to 2021.7.13
   * Update Fenix to 90.1.1
   * Bug 40172: Find the Quit button [fenix]
   * Bug 40173: Rebase fenix patches to fenix v90.0.0-beta.6 [fenix]
   * Bug 40177: Hide Tor icons in settings [fenix]
   * Bug 40179: Show Snowflake bridge option on Release [fenix]
   * Bug 40180: Rebase fenix patches to fenix v90.1.1 [fenix]
 * Build System
   * Android
     * Bug 40312: Update components for mozilla90 [tor-browser-build]

Tor Browser 10.5.2 -- July 13 2021
 * Windows + OS X + Linux
   * Update Firefox to 78.12.0esr
   * Bug 40497: Cannot set multiple pages as home pages in 10.5a17 [tor-browser]
   * Bug 40507: Full update is not downloaded after applying partial update fails [tor-browser]
   * Bug 40510: open tabs get redirected to about:torconnect on restart [tor-browser]

Tor Browser 11.0a1 -- July 9 2021
 * Android
   * Bug 40172: Find the Quit button [fenix]
   * Bug 40173: Rebase fenix patches to fenix v90.0.0-beta.6 [fenix]
   * Bug 40179: Show Snowflake bridge option on Release [fenix]
 * Build System
   * Android
     * Bug 40312: Update components for mozilla90 [tor-browser-build]
     * Bug 40323: Remove unused gomobile project [tor-browser-build]

Tor Browser 10.5.1 -- July 7 2021
 * Android
   * Bug 40324: Change Fenix variant to Release [fenix]

Tor Browser 10.5 -- July 6 2021
 * All Platforms
   * Update NoScript to 11.2.9
   * Update Tor Launcher to 0.2.30
   * Translations update
   * Bug 25483: Provide Snowflake based on Pion for Windows, macOS, and Linux
   * Bug 33761: Remove unnecessary snowflake dependencies
   * Bug 40064: Bump libevent to 2.1.12 [tor-browser-build]
   * Bug 40137: Migrate https-everywhere storage to idb [tor-browser]
   * Bug 40261: Bump versions of snowflake and webrtc [tor-browser-build]
   * Bug 40263: Update domain front for Snowflake [tor-browser-build]
   * Bug 40302: Update version of snowflake [tor-browser-build]
   * Bug 40030: DuckDuckGo redirect to html doesn't work [torbutton]
 * Windows + OS X + Linux
   * Bug 27476: Implement about:torconnect captive portal within Tor Browser [tor-browser]
   * Bug 32228: Bookmark TPO support domains in Tor Browser
   * Bug 33803: Add a secondary nightly MAR signing key [tor-browser]
   * Bug 33954: Consider different approach for Bug 2176
   * Bug 34345: "Don't Bootstrap" Startup Mode
   * Bug 40011: Rename tor-browser-brand.ftl to brand.ftl [torbutton]
   * Bug 40012: Fix about:tor not loading some images in 82 [torbutton]
   * Bug 40138: Move our primary nightly MAR signing key to tor-browser [tor-browser-build]
   * Bug 40209: Implement Basic Crypto Safety [tor-browser]
   * Bug 40428: Correct minor Cryptocurrency warning string typo [tor-browser]
   * Bug 40429: Update Onboarding for 10.5 [tor-browser]
   * Bug 40455: Block or recover background requests after bootstrap [tor-browser]
   * Bug 40456: Update the SecureDrop HTTPS-Everywhere update channel [tor-browser]
   * Bug 40475: Include clearing CORS preflight cache [tor-browser]
   * Bug 40478: Onion alias url rewrite is broken [tor-browser]
   * Bug 40484: Bootstrapping page show Quickstart text [tor-browser]
   * Bug 40490: BridgeDB bridge captcha selection is broken in alpha [tor-browser]
   * Bug 40495: Onion pattern is focusable by click on about:torconnect [tor-browser]
   * Bug 40499: Onion Alias doesn't work with TOR_SKIP_LAUNCH [tor-browser]
 * Android
   * Bug 30318: Integrate snowflake into mobile Tor Browser
   * Bug 40206: Disable the /etc/hosts parser [tor-browser]
 * Linux
   * Bug 40089: Remove CentOS 6 support for Tor Browser 10.5 [tor-browser]
 * Build System
   * All Platforms
     * Update Go to 1.15.13
     * Bug 23631: Use rootless containers [tor-browser-build]
     * Bug 33693: Change snowflake and meek dummy address [tor-browser]
     * Bug 40016: getfpaths is not setting origin_project [rbm]
     * Bug 40169: Update apt package cache after calling pre_pkginst, too [tor-browser-build]
     * Bug 40194: Remove osname part in cbindgen filename [tor-browser-build]
   * Windows + OS X + Linux
     * Bug 40081: Build Mozilla code with --enable-rust-simd [tor-browser-build]
     * Bug 40104: Use our TMPDIR when creating our .mar files [tor-browser-build]
     * Bug 40133: Bump Rust version for ESR 78 to 1.43.0 [tor-browser-build]
     * Bug 40166: Update apt cache before calling pre_pkginst in container-image config [tor-browser-build]
   * Android
     * Bug 28672: Android reproducible build of Snowflake
     * Bug 40313: Use apt-get to install openjdk-8 .deb files with their dependencies [tor-browser-build]
   * Windows
     * Bug 34360: Bump binutils to 2.35.1
     * Bug 40131: Remove unused binutils patches [tor-browser-build]
   * Linux
     * Bug 26238: Move to Debian Jessie for our Linux builds
     * Bug 31729: Support Wayland
     * Bug 40041: Remove CentOS 6 support for 10.5 series [tor-browser-build]
     * Bug 40103: Add i386 pkg-config path for linux-i686 [tor-browser-build]
     * Bug 40112: Strip libstdc++ we ship [tor-browser-build]
     * Bug 40118: Add missing libdrm dev package to firefox container [tor-browser-build]
     * Bug 40235: Bump apt for Jessie containers [tor-browser-build]

Tor Browser 10.5a17 -- June 27 2021
 * All Platforms
   * Update NoScript to 11.2.9
   * Update Tor to *******
   * Update Tor Launcher to 0.2.29
 * Windows + OS X + Linux
   * Bug 34345: "Don't Bootstrap" Startup Mode
   * Bug 40302: Update version of snowflake [tor-browser-build]
   * Bug 40455: Block or recover background requests after bootstrap [tor-browser]
   * Bug 40456: Update the SecureDrop HTTPS-Everywhere update channel [tor-browser]
   * Bug 40475: Include clearing CORS preflight cache [tor-browser]
   * Bug 40478: Onion alias url rewrite is broken [tor-browser]
 * Build System
   * All Platforms
     * Update Go to 1.15.13
   * Android
     * Bug 40313: Use apt-get to install openjdk-8 .deb files with their dependencies [tor-browser-build]

Tor Browser 10.0.18 -- June 16 2021
 * All Platforms
   * Update Tor to *******
 * Android
   * Update Fenix to 89.1.1
   * Update NoScript to 11.2.8
   * Bug 40055: Rebase android-componets patches on 75.0.22 for Fenix 89 [android-components]
   * Bug 40165: Announce v2 onion service deprecation on about:tor [fenix]
   * Bug 40166: Hide "Normal" tab (again) and Sync tab in TabTray [fenix]
   * Bug 40167: Hide "Save to Collection" in menu [fenix]
   * Bug 40169: Rebase fenix patches to fenix v89.1.1 [fenix]
   * Bug 40170: Error building tor-browser-89.1.1-10.5-1 [fenix]
   * Bug 40432: Prevent probing installed applications [tor-browser]
   * Bug 40470: Rebase 10.0 patches onto 89.0 [tor-browser]
 * Build System
   * Android
     * Bug 40290: Update components for mozilla89-based Fenix [tor-browser-build]

Tor Browser 10.5a16 -- June 5 2021
 * All Platforms
   * Update HTTPS Everywhere to 2021.4.15
   * Update NoScript to 11.2.8
   * Update Tor to *******-rc
   * Bug 40432: Prevent probing installed applications [tor-browser]
 * Windows + OS X + Linux
   * Update Firefox to 78.11.0esr
   * Bug 27476: Implement about:torconnect captive portal within Tor Browser [tor-browser]
   * Bug 40037: Announce v2 onion service deprecation on about:tor [torbutton]
   * Bug 40428: Correct minor Cryptocurrency warning string typo [tor-browser]
 * Android
   * Update Fenix to 89.1.1
   * Bug 40055: Rebase android-componets patches on 75.0.22 for Fenix 89 [android-components]
   * Bug 40165: Announce v2 onion service deprecation on about:tor [fenix]
   * Bug 40169: Rebase fenix patches to fenix v89.1.1 [fenix]
   * Bug 40170: Error building tor-browser-89.1.1-10.5-1 [fenix]
   * Bug 40453: Rebase tor-browser patches to 89.0 [tor-browser]
 * Build System
   * All Platforms
     * Update Go to 1.15.12
   * Android
     * Bug 40290: Update components for mozilla89-based Fenix [tor-browser-build]

Tor Browser 10.0.17 -- June 1 2021
 * Windows + OS X + Linux
   * Update Firefox to 78.11.0esr
   * Update HTTPS Everywhere to 2021.4.15
   * Update NoScript to 11.2.8
   * Update Tor to *******
   * Bug 27002: (Mozilla 1673237) Always allow SVGs on about: pages [tor-browser]
   * Bug 40432: Prevent probing installed applications [tor-browser]
   * Bug 40037: Announce v2 onion service deprecation on about:tor [torbutton]

Tor Browser 10.5a15 -- April 23 2021
 * All Platforms
   * Update Tor to *******-alpha
 * Windows + OS X + Linux
   * Update Firefox to 78.10.0esr
   * Bug 40408: Disallow SVG Context Paint in all web content [tor-browser]
 * Android
   * Update Fenix to 88.1.1
   * Bug 40051: Rebase android-components patches for Fenix 88 [android-components]
   * Bug 40158: Rebase Fenix patches to Fenix 88.1.1 [fenix]
   * Bug 40399: Rebase 10.5 patches on 88.0 [tor-browser]
 * Build System
   * All Platforms
     * Update Go to 1.15.11
   * Android
    * Bug 40259: Update components for mozilla88-based Fenix [tor-browser-build]

Tor Browser 10.0.16 -- May 7 2021
 * Android
   * Update Fenix to 88.1.3
   * Update HTTPS Everywhere to 2021.4.15
   * Update NoScript to 11.2.6
   * Translations update
   * Bug 40052: Rebase android-components patches for Fenix 88 [android-components]
   * Bug 40162: Disable Numbus experiments [fenix]
   * Bug 40163: Rebase Fenix patches to Fenix 88.1.3 [fenix]
   * Bug 40423: Disable http/3 [tor-browser]
   * Bug 40425: Rebase 10.5 patches on 88.0.1 [tor-browser]
 * Build System
   * Android
     * Bug 40259: Update components for mozilla88-based Fenix [tor-browser-build]
     * Bug 40293: Patch app-services' vendored uniffi_bindgen [tor-browser-build]

Tor Browser 10.0.16 -- April 20 2021
 * Windows + OS X + Linux
   * Update Firefox to 78.10.0esr
   * Update NoScript to 11.2.4
   * Bug 40007: Update domain fronting config for Moat [tor-launcher]
   * Bug 40390: Add Burmese as a new locale [tor-browser]
   * Bug 40408: Disallow SVG Context Paint in all web content [tor-browser]

Tor Browser 10.5a14 -- April 11 2021
 * All Platforms
   * Update NoScript to 11.2.4
   * Translations update
   * Bug 40261: Bump versions of snowflake and webrtc [tor-browser-build]
   * Bug 40263: Update domain front for Snowflake [tor-browser-build]
   * Bug 40390: Add Burmese as a new locale [tor-browser]
 * Windows + OS X + Linux
   * Bug 40007: Update domain fronting config for Moat [tor-launcher]

Tor Browser 10.0.15 -- April 7 2021
 * All Platforms
   * Update Openssl to 1.1.1k
   * Bug 40030: Add 'noscript' capability to NoScript [torbutton]
 * Android
   * Update Fenix to 87.0.0
   * Update NoScript to 11.2.4
   * Update Tor to *******
   * Translations update
   * Bug 40045: Add External App Prompt for Sharing Images [android-components]
   * Bug 40047: Rebase android-components patches for Fenix 87.0.0 [android-components]
   * Bug 40151: Remove survey banner on TBA-stable [fenix]
   * Bug 40153: Rebase Fenix patches to Fenix 87.0.0 [fenix]
   * Bug 40365: Rebase 10.5 patches on 87.0 [tor-browser]
   * Bug 40383: Disable dom.enable_event_timing [tor-browser]
 * Build System
   * Android
     * Bug 40162: Build Fenix instrumented tests apk [tor-browser-build]
     * Bug 40172: Move Gradle compilers out of android-toolchain to own gradle project [tor-browser-build]
     * Bug 40241: Update components for mozilla87-based Fenix [tor-browser-build]

Tor Browser 10.5a13 -- March 24 2021
 * Windows + OS X + Linux
   * Update Firefox to 78.9.0esr
   * Update NoScript to 11.2.3
   * Update Openssl to 1.1.1k
   * Update Tor to *******-alpha
   * Translations update
   * Bug 40030: DuckDuckGo redirect to html doesn't work [torbutton]
   * Bug 40032: Remove Snowflake survey banner from TB-alpha [torbutton]
 * Android
   * Update Fenix to 87.0.0
   * Bug 40047: Rebase android-components patches for Fenix 87.0.0 [android-components]
   * Bug 40153: Rebase Fenix patches to Fenix 87.0.0 [fenix]
   * Bug 40365: Rebase 10.5 patches on 87.0 [tor-browser]
   * Bug 40383: Disable dom.enable_event_timing [tor-browser]
 * Build System
   * Windows + OS X + Linux
     * Update Go to 1.15.10
     * Bug 23631: Use rootless containers [tor-browser-build]
     * Bug 40016: getfpaths is not setting origin_project [rbm]
   * Windows
     * Bug 40252: Bump mingw-w64 and clang for Firefox 78.9 [tor-browser-build]

Tor Browser 10.0.14 -- March 23 2021
 * Windows + OS X + Linux
   * Update Firefox to 78.9.0esr
   * Update NoScript to 11.2.3
   * Update Tor to *******
   * Bug 40031: Remove survey banner on TB-stable [torbutton]
 * Build System
   * Windows
     * Bug 40249: Bump mingw-w64 and clang for Firefox 78.9 [tor-browser-build]

Tor Browser 10.5a12 -- March 20 2021
 * Android
   * Update Fenix to 87.0.0-beta.2
   * Update NoScript to 11.2.3
   * Update Tor to *******-alpha
   * Translations update
   * Bug 40030: DuckDuckGo redirect to html doesn't work [torbutton]
   * Bug 40043: Rebase android-components patches for Fenix 87 beta 2 builds [android-components]
   * Bug 40045: Add External App Prompt for Sharing Images [android-components]
   * Bug 40150: Rebase Fenix patches to Fenix 87 beta 2 [fenix]
   * Bug 40361: Rebase tor-browser patches to 87.0b4 [tor-browser]
 * Build System
   * Android
     * Update Go to 1.15.10
     * Bug 23631: Use rootless containers [tor-browser-build]
     * Bug 40016: getfpaths is not setting origin_project [rbm]
     * Bug 40172: Move Gradle compilers out of android-toolchain to own gradle project [tor-browser-build]
     * Bug 40241: Update components for mozilla87-based Fenix [tor-browser-build]

Tor Browser 10.0.13 -- March 3 2021
 * Linux
   * Bug 40328: Fix instability after upgrading to glibc 2.33

Tor Browser 10.5a11 -- February 23 2021
 * All Platforms
   * Update NoScript to 11.2.2
   * Update Openssl to 1.1.1j
   * Update Tor to *******
 * Windows + OS X + Linux
   * Update Firefox to 78.8.0esr
   * Bug 40029: Create survey banner on about:tor for snowflake [torbutton]
   * Bug 40209: Implement Basic Crypto Safety [tor-browser]
 * OS X + Linux
   * Update HTTPS Everywhere to 2021.1.27
   * Bug 40212: Bump version of snowflake and webrtc [tor-browser-build]
 * Android
   * Update Firefox to 86.1.0
   * Bug 40144: Hide Download Manager [fenix]
   * Bug 40148: Create survey banner on about:tor for Snowflake [fenix]
   * Bug 40344: Set privacy.window.name.update.enabled=false [tor-browser]
 * Build System
   * Linux
     * Bug 40235: Bump apt for Jessie containers [tor-browser-build]

Tor Browser 10.0.12 -- February 23 2021
 * All Platforms
   * Update NoScript to 11.2.2
   * Update Openssl to 1.1.1j
   * Update Tor to *******
 * Windows + OS X + Linux
   * Update Firefox to 78.8.0esr
   * Bug 40026: Create survey banner on about:tor for desktop [torbutton]
   * Bug 40287: Switch DDG search from POST to GET [tor-browser]
 * Android
   * Update Firefox to 86.1.0
   * Bug 40138: Create survey banner on about:tor for Android [fenix]
   * Bug 40144: Hide Download Manager [fenix]
   * Bug 40171: Make WebRequest and GeckoWebExecutor First-Party aware [tor-browser]
   * Bug 40188: Build and ship snowflake only if it is enabled [tor-browser-build]
   * Bug 40309: Avoid using regional OS locales [tor-browser]
   * Bug 40344: Set privacy.window.name.update.enabled=false [tor-browser]
 * Build System
   * Android
     * Bug 40214: Update AMO Collection URL [tor-browser-build]
     * Bug 40217: Update components for switch to mozilla86-based Fenix [tor-browser-build]

Tor Browser 10.5a10 -- February 7 2021
 * Windows
   * Update Firefox to 78.7.1esr
   * Update HTTPS Everywhere to 2021.1.27
   * Bug 40212: Bump version of snowflake and webrtc [tor-browser-build]

Tor Browser 10.0.11 -- February 6 2021
 * Windows
    * Update Firefox to 78.7.1esr

Tor Browser 10.5a9 -- February 5 2021
 * Android
   * Update Fenix to 86.0.0-beta.2
   * Update HTTPS Everywhere to 2021.1.27
   * Update NoScript to 11.2
   * Bug 40041: Rebase android-components patches for Fenix 86 beta 2 builds [android-components]
   * Bug 40109: Reduce requested permissions [fenix]
   * Bug 40141: Hide EME site permission [fenix]
   * Bug 40143: Use deterministic date in Test apk [fenix]
   * Bug 40146: Rebase Fenix patches to Fenix 86 beta 2 [fenix]
   * Bug 40188: Build and ship snowflake only if it is enabled [tor-browser-build}
   * Bug 40212: Bump version of snowflake and webrtc [tor-browser-build]
   * Bug 40308: Disable network partitioning until we evaluate dFPI [tor-browser]
   * Bug 40309: Avoid using regional OS locales [tor-browser]
   * Bug 40320: Rebase tor-browser patches to 86.0b5 [tor-browser]
 * Build System
   * Android
     * Bug 40214: Update AMO Collection URL [tor-browser-build]
     * Bug 40217: Update components for switch to mozilla86-based Fenix [tor-browser-build]

Tor Browser 10.0.10 -- February 3 2021
 * All Platforms
   * Update NoScript to 11.2
   * Update HTTPS Everywhere to 2021.1.27
   * Bug 40224: Backport Tor patch for v3 onion services [tor-browser-build]
 * Android
   * Pick up fix for Mozilla's bug 1688783
   * Pick up fix for Mozilla's bug 1688017

Tor Browser 10.5a8 -- January 26 2021
 * All Platforms
   * Update NoScript to 11.1.9
   * Update Tor to *******-rc
 * Windows + OS X + Linux
   * Update Firefox to 78.7.0esr
   * Bug 40249: Remove EOY 2020 Campaign [tor-browser]
   * Bug 40307: Rebase 10.5 patches onto 78.7.0esr [tor-browser]
 * Android
   * Update Fenix to 85.1.0
   * Bug 40037: Rebase 10.5 patches onto 70.0.16 [android-components]
   * Bug 40137: Remove EOY 2020 Campaign [fenix]
   * Bug 40139: Rebase 10.5 patches onto 85.1.0 [fenix]
   * Bug 40305: Rebase 10.5 patches onto 85.0 [tor-browser]
 * Build System
   * All Platforms
     * Update Go to 1.15.7
     * Bug 33693: Change snowflake and meek dummy address [tor-browser]
   * Android
     * Bug 40208: Mitigate uniffi non-deterministic code generation [tor-browser-build]
   * Linux
     * Bug 40112: Strip libstdc++ we ship [tor-browser-build]

Tor Browser 10.0.9 -- January 26 2021
 * All Platforms
   * Update NoScript to 11.1.9
 * Windows + OS X + Linux
   * Update Firefox to 78.7.0esr
   * Bug 40249: Remove EOY 2020 Campaign [tor-browser]
 * Android
   * Update Fenix to 85.1.0
   * Bug 40137: Remove EOY 2020 Campaign [fenix]
   * Bug 40165: Update zstd to 1.4.8 [tor-browser-build]
   * Bug 40308: Disable network state partitioning until audit [tor-browser]
 * Build System
   * All Platforms
     * Update Go to 1.14.14
   * Android
     * Bug 40190: Update toolchain for Fenix 85 [tor-browser-build]
     * Bug 40191: Update Fenix and dependencies to 85.0.0-beta1 [tor-browser-build]
     * Bug 40193: Build all mobile Rust targets in a single step [tor-browser-build]
     * Bug 40208: Mitigate uniffi non-deterministic code generation [tor-browser-build]

Tor Browser 10.5a7 -- January 19 2021
 * All Platforms
   * Update NoScript to 11.1.8
   * Bug 40204: Update Tor to *******-rc
   * Translations update
 * Windows + OS X + Linux
   * Update Firefox to 78.6.1esr
   * Bug 40287: Switch DDG search from POST to GET [tor-browser]
   * Bug 40297: Rebase 10.5 patches onto 78.6.1esr [tor-browser]
 * Android
   * Bug 40036: Rebase patches onto v70.0.11 [android-components]
   * Bug 40134: Rebase patches onto v85.0.0-beta.7 [fenix]
   * Bug 40293: Rebase patches onto 85.0b9-build1 [tor-browser]
   * Bug 40165: Update zstd to 1.4.8 [tor-browser-build]
 * OS X
   * Bug 40262: Browser tabs crashing on the new Macbooks with the M1 chip [tor-browser]
 * Build System
   * All Platforms
     * Bug 40194: Remove osname part in cbindgen filename [tor-browser-build]
   * Android
     * Bug 40162: Build Fenix instrumented tests apk [tor-browser-build]
     * Bug 40190: Update toolchain for Fenix 85 [tor-browser-build]
     * Bug 40191: Update Fenix and dependencies to 85.0.0-beta1 [tor-browser-build]
     * Bug 40193: Build all mobile Rust targets in a single step [tor-browser-build]
     * Bug 40195: repo.spring.io is not usable anymore [tor-browser-build]

Tor Browser 10.0.8 -- January 12 2021
 * All Platforms
   * Update NoScript to 11.1.7
 * Windows + OS X + Linux
   * Update Firefox to 78.6.1esr
 * Android
   * Update Fenix to 84.1.4
 * OS X
   * Bug 40262: Browser tabs crashing on the new Macbooks with the M1 chip [tor-browser]
 * Build System
   * Android
     * Bug 40195: repo.spring.io is not usable anymore [tor-browser-build]

Tor Browser 10.5a6 -- December 15 2020
 * All Platforms
   * Update NoScript to 11.1.6
   * Bug 40175: Update obfs4proxy's TLS certificate public key pinning [tor-browser-build]
   * Bug 40176: Update openssl to 1.1.1i [tor-browser-build]
 * Windows + OS X + Linux
   * Update Firefox to 78.6.0esr
   * Update HTTPS Everywhere to 2020.11.17
   * Update Tor to *******-alpha
   * Bug 33803: Add a secondary nightly MAR signing key [tor-browser]
   * Bug 40138: Move our primary nightly MAR signing key to tor-browser [tor-browser-build]
   * Bug 40159: Update snowflake to ece43cbf [tor-browser-build]
 * Android
   * Update Fenix to 84.1.0
 * Linux
   * Bug 40226: Crash on Fedora Workstation Rawhide GNOME [tor-browser]
 * Build System
   * All Platforms
     * Bug 40169: Update apt package cache after calling pre_pkginst, too [tor-browser-build]
     * Bug 40183: Pick up Go 1.15.6 [tor-browser-build]
   * Windows + OS X + Linux
     * Bug 40081: Build Mozilla code with --enable-rust-simd [tor-browser-build]
     * Bug 40166: Update apt cache before calling pre_pkginst in container-image config [tor-browser-build]
   * Android
     * Bug 40184: Update Fenix and deps to 84.1.0 [tor-browser-build]
   * OS X
     * Bug 40147: Remove RANLIB workaround once we pick up *******-alpha [tor-browser-build]

Tor Browser 10.0.7 -- December 15 2020
 * All Platforms
   * Update HTTPS Everywhere to 2020.11.17
   * Bug 40166: Disable security.certerrors.mitm.auto_enable_enterprise_roots [tor-browser]
   * Bug 40176: Update openssl to 1.1.1i [tor-browser-build]
 * Windows + OS X + Linux
   * Update Firefox to 78.6.0esr
 * Android
   * Update Fenix to 84.1.0
   * Update NoScript to 11.1.6
 * Linux
   * Bug 40226: Crash on Fedora Workstation Rawhide GNOME [tor-browser]
 * Build System
   * All Platforms
     * Bug 40139: Pick up rbm commit for bug 40008 [tor-browser-build]
     * Bug 40161: Update Go compiler to 1.14.13 [tor-browser-build]
   * Android
     * Bug 40128: Allow updating Fenix allowed_addons.json [tor-browser-build]
     * Bug 40140: Create own Gradle project [tor-browser-build]
     * Bug 40155: Update toolchain for Fenix 84 [tor-browser-build]
     * Bug 40156: Update Fenix and dependencies to 84.0.0-beta2 [tor-browser-build]
     * Bug 40163: Avoid checking hash of .pom files [tor-browser-build]
     * Bug 40171: Include all uniffi-rs artifacts into application-services [tor-browser-build]
     * Bug 40184: Update Fenix and deps to 84.1.0 [tor-browser-build]

Tor Browser 10.0.6 -- December 8 2020
 * All Platforms
   * Bug 40175: Update obfs4proxy's TLS certificate public key pinning [tor-browser-build]

Tor Browser 10.5a5 -- December 7 2020
 * Android
   * Update Fenix to 84.0.0-beta.2
   * Update HTTPS Everywhere to 2020.11.17
   * Update Tor to *******-alpha
   * Translations update
   * Bug 40159: Update snowflake to ece43cbf [tor-browser-build]
   * Bug 40236: Rebase tor-browser patches to 84.0b1 [tor-browser]
   * Bug 40258: Rebase tor-browser patches to 84.0b7 [tor-browser]
   * Bug 40119: Rebase Fenix patches to Fenix 84 beta 2 [fenix]
   * Bug 40027: Rebase android-components patches for Fenix 84 beta 2 builds [android-components]
 * Build System
   * Android
     * Bug 40081: Build Mozilla code with --enable-rust-simd [tor-browser-build]
     * Bug 40128: Allow updating Fenix allowed_addons.json [tor-browser-build]
     * Bug 40140: Create own Gradle project [tor-browser-build]
     * Bug 40155: Update toolchain for Fenix 84 [tor-browser-build]
     * Bug 40156: Update Fenix and dependencies to 84.0.0-beta2 [tor-browser-build]
     * Bug 40161: Pick up Go 1.15.5 [tor-browser-build]
     * Bug 40163: Bug 40163: Avoid checking hash of .pom files [tor-browser-build]
     * Bug 40166: Update apt cache before calling pre_pkginst in container-image config [tor-browser-build]
     * Bug 40171: Include all uniffi-rs artifacts into application-services [tor-browser-build]

Tor Browser 10.5a4 -- November 17 2020
 * All Platforms
   * Update Tor to 0.4.5.1-alpha
   * Bug 40212: Add new default bridge "PraxedisGuerrero" [tor-browser]
 * Windows + OS X + Linux
   * Update Firefox to 78.5.0esr
 * Android
   * Update Fenix to 83.1.0
   * Bug 27002: (Mozilla 1673237) Always allow SVGs on about: pages
   * Bug 40137: Built-in https-everywhere storage is not migrated to idb [tor-browser]
   * Bug 40152: Top Crash: android.database.sqlite.SQLiteConstraintException [tor-browser-build]
   * Bug 40171: Make WebRequest and GeckoWebExecutor First-Party aware [tor-browser]
   * Bug 40205: Replace occurrence of EmptyCString with 0-length _ns literal [tor-browser]
   * Bug 40206: Disable the /etc/hosts parser [tor-browser]
   * Translations update
 * Build System
   * OS X
     * Bug 40139: Set RANLIB in macOS tor build [tor-browser-build]
   * Android
     * Bug 40211: Lower required build-tools version to 29.0.2 [tor-browser]
     * Bug 40126: Bump Node to 10.22.1 for mozilla83 [tor-browser-build]
     * Bug 40127: Update components for switch to mozilla83-based Fenix [tor-browser-build]

Tor Browser 10.0.5 -- November 27 2020
 * All Platforms
   * Update Tor to *******
   * Bug 40212: Add new default obfs4 bridge [tor-browser]
 * Windows + OS X + Linux
   * Update Firefox to 78.5.0esr
 * Android
   * Update Fenix to 83.1.0
   * Translations update
   * Bug 40152: Top Crash: android.database.sqlite.SQLiteConstraintException [tor-browser-build]
   * Bug 40205: Replace occurrence of EmptyCString with 0-length _ns literal [tor-browser]
 * Build System
   * Android
     * Bug 40126: Update toolchains for Fenix 83 [tor-browser-build]
     * Bug 40126: Bump Node to 10.22.1 for mozilla83 [tor-browser-build]
     * Bug 40127: Update GeckoView to 83, android-components to 63.0.1, and Fenix to 83.0.0b2 [tor-browser-build]
     * Bug 40160: Update Fenix to 83.1.0, and android-components to 63.0.9 [tor-browser-build]
     * Bug 40211: Lower required build-tools version to 29.0.2 [tor-browser]

Tor Browser 10.5a3 -- November 10 2020
 * All Platforms
   * Update NoScript to 11.1.5
   * Bug 40022: EOY November Update - Matching [torbutton]
   * Bug 40064: Bump libevent to 2.1.12 [tor-browser-build]
   * Translations update
 * Windows + OS X + Linux
   * Bug 27002: (Mozilla 1673237) Always allow SVGs on about: pages
   * Bug 40021: Keep page shown after Tor Browser update purple [torbutton]
   * Bug 40137: Migrate https-everywhere storage to idb [tor-browser]
   * Bug 40219: Backport fix for Mozilla's bug 1675905 [tor-browser]
 * Android
   * Pick up fix for Mozilla's bug 1675905 (with GeckoView 82.0.3)
   * Bug 40106: EOY November Update - Matching [fenix]
 * Build System
   * All Platforms
     * Bug 40079: Bump Go to 1.15.4 [tor-browser-build]
   * Windows + OS X + Linux
     * Bug 40133: Bump Rust version for ESR 78 to 1.43.0 [tor-browser-build]

Tor Browser 10.0.4 -- November 9 2020
 * All Platforms
   * Update NoScript to 11.1.5
   * Bug 40022: EOY November Update - Matching [torbutton]
   * Bug 40219: Backport Mozilla Bug 1675905 [tor-browser]
   * Translations update
 * Windows + OS X + Linux
   * Bug 40021: Keep page shown after Tor Browser update purple [torbutton]
 * Android
   * Bug 40106: EOY November Update - Matching [fenix]
   * Translations update
 * Build System
   * All Platforms
     * Update Go to 1.14.11
   * Windows + OS X + Linux
     * Bug 40141: Include "desktop" in signed tag [tor-browser-build]
   * Android
     * Bug 40141: Include "android" in signed tag [tor-browser-build]

Tor Browser 10.0.3 -- November 2 2020
 * Android
   * Update Fenix to 82.1.1
   * Update NoScript to 11.1.4
   * Update OpenSSL to 1.1.1h
   * Update Tor to *******
   * Bug 10394: Let Tor Browser update HTTPS Everywhere
   * Bug 11154: Disable TLS 1.0 (and 1.1) by default
   * Bug 16931: Sanitize the add-on blocklist update URL
   * Bug 17374: Disable 1024-DH Encryption by default
   * Bug 21601: Remove unused media.webaudio.enabled pref
   * Bug 30682: Disable Intermediate CA Preloading
   * Bug 30812: Exempt about: pages from Resist Fingerprinting
   * Bug 32886: Separate treatment of @media interaction features for desktop and android
   * Bug 33534: Review FF release notes from FF69 to latest (FF78)
   * Bug 33594: Disable telemetry collection (Glean)
   * Bug 33851: Patch out Parental Controls detection and logging
   * Bug 33856: Set browser.privatebrowsing.forceMediaMemoryCache to True
   * Bug 33862: Fix usages of createTransport API
   * Bug 33962: Uplift patch for bug 5741 (dns leak protection)
   * Bug 34125: API change in protocolProxyService.registerChannelFilter
   * Bug 34338: Disable the crash reporter
   * Bug 34377: Port padlock states for .onion services
   * Bug 34378: Port external helper app prompting
   * Bug 34401: Re-design Connect screen on Android
   * Bug 34402: Re-design Network Settings Screen on Android
   * Bug 34403: UI changes for "Only Private Browsing Mode" on Android
   * Bug 34405: Re-design about:tor on Android
   * Bug 34406: Re-design onion indicators for Android
   * Bug 34407: Review all Fenix menu items
   * Bug 30605: Honor privacy.spoof_english
   * Bug 40001: Start Tor as part of the Fenix initialization [fenix]
   * Bug 40001: Generate tor-browser-brand.ftl when importing translations [torbutton]
   * Bug 40002: Ensure system download manager is not used [android-components]
   * Bug 40002: Fix generateNSGetFactory being moved to ComponentUtils [torbutton]
   * Bug 40003: Adapt code for L10nRegistry API changes [torbutton]
   * Bug 40003: Block starting Tor when setup is not complete [tor-android-service]
   * Bug 40004: "Tor Browser" string is used instead of "Alpha"/"Nightly" for non en-US locales [tor-android-service]
   * Bug 40004: Fix noscript message passing for Firefox 79 [torbutton]
   * Bug 40005: Modify WebExtensions Menu [android-components]
   * Bug 40006: "Only Private Browsing Mode" on Android [fenix]
   * Bug 40006: Add Security Level plumbing [android-components]
   * Bug 40007: Port external helper app prompting [android-components]
   * Bug 40007: Move SecurityPrefs initialization to the StartupObserver component [torbutton]
   * Bug 40008: Style fixes for 78 [torbutton]
   * Bug 40009: Change the default search engines [android-components]
   * Bug 40010: Verify Sentry is disabled [fenix]
   * Bug 40011: Verify Leanplum is disabled [fenix]
   * Bug 40011: Hide option for disallowing addons in private mode [android-components]
   * Bug 40012: Verify Adjust is disabled [fenix]
   * Bug 40013: Timestamp is embedded in extension manifest files [android-components]
   * Bug 40013: Verify InstallReferrer is disabled [fenix]
   * Bug 40014: Verify Google Ads ID is disabled [fenix]
   * Bug 40014: Set correct default Security Level [android-components]
   * Bug 40015: Modify Fenix Home Menu [fenix]
   * Bug 40016: Modify Fenix Settings Menu [fenix]
   * Bug 40016: Allow inheriting from AddonCollectionProvider [android-components]
   * Bug 40017: Rebase android-components patches to 60 [android-components]
   * Bug 40017: Audit Firefox 68-78 diff for proxy issues [tor-browser]
   * Bug 40018: Disable Push functionality [fenix]
   * Bug 40019: Ensure missing Adjust token does not throw an exception [fenix]
   * Bug 40019: Expose spoofEnglish pref [android-components]
   * Bug 40020: Disable third-party cookies [android-components]
   * Bug 40021: Force telemetry=false in Fennec settings migration [android-components]
   * Bug 40022: Migrate tor security settings [android-components]
   * Bug 40023: Stop Private Notification Service [android-components]
   * Bug 40023: Rebase Tor Browser esr78 patches onto 80 beta [tor-browser]
   * Bug 40024: Disable tracking protection by default [android-components]
   * Bug 40026: Implement Security Level settings [fenix]
   * Bug 40028: Implement bootstrapping and about:tor [fenix]
   * Bug 40029: Rebase Fenix patches to 81.1.0b1 [fenix]
   * Bug 40030: Install https-everywhere and noscript addons [fenix]
   * Bug 40031: Hide Mozilla-specific items on About page [fenix]
   * Bug 40032: Disallow Cleartext Traffic [fenix]
   * Bug 40034: Disable PWA [fenix]
   * Bug 40035: Maybe hide Quick Start in release [fenix]
   * Bug 40038: Review RemoteSettings for ESR 78 [tor-browser]
   * Bug 40039: Implement Bridge configuration from Connect screen [fenix]
   * Bug 40040: Investigate why bootstrapping fails [fenix]
   * Bug 40041: Implement Network settings [fenix]
   * Bug 40042: Timestamp is embedded in extension manifest files [fenix]
   * Bug 40044: Fixup Connect, Onboarding, and Home screens [fenix]
   * Bug 40048: Disable various ESR78 features via prefs [tor-browser]
   * Bug 40050: Rebase Fenix patches to Fenix 82 [fenix]
   * Bug 40053: Select your security settings panel on start page is confusing [fenix]
   * Bug 40054: Search engines on mobile Tor Browser don't match the desktop ones [fenix]
   * Bug 40058: Disabling/Enabling addon still shows option to disallow in private mode [fenix]
   * Bug 40058: Hide option for disallowing addon in private mode [fenix]
   * Bug 40061: Do not show "Send to device" in sharing menu [fenix]
   * Bug 40062: HTTPS Everywhere is not shown as installed [fenix]
   * Bug 40063: Do not sort search engines alphabetically [fenix]
   * Bug 40064: Modify Nighty (and Debug) build variants [fenix]
   * Bug 40066: Remove default bridge ************* [tor-browser-build]
   * Bug 40066: Update existing prefs for ESR 78 [tor-browser]
   * Bug 40067: Make date on Fenix about page reproducible [fenix]
   * Bug 40068: Tor Service closes when changing theme [fenix]
   * Bug 40069: Add helpers for message passing with extensions [tor-browser]
   * Bug 40071: Show only supported locales [fenix]
   * Bug 40072: Bug 40072: Disable Tracking Protection [fenix]
   * Bug 40073: Use correct branding on About page [fenix]
   * Bug 40073: Repack omni.ja to include builtin HTTPS Everywhere [tor-browser-build]
   * Bug 40073: Disable remote Public Suffix List fetching [tor-browser]
   * Bug 40076: "Explore privately" not visible [fenix]
   * Bug 40078: Crash at Android startup from background service [fenix]
   * Bug 40082: Security level is reset when the app is killed [fenix]
   * Bug 40082: Let JavaScript on safest setting handled by NoScript again [tor-browser]
   * Bug 40083: Locale ordering in BuildConfig is non-deterministic [fenix]
   * Bug 40087: Implement a switch for english locale spoofing [fenix]
   * Bug 40088: Use Tor Browser logo in migration screen [fenix]
   * Bug 40091: Load HTTPS Everywhere as a builtin addon [tor-browser]
   * Bug 40093: Enable Quit menu button [fenix]
   * Bug 40094: Do not use MasterPasswordTipProvider in HomeFragment [fenix]
   * Bug 40095: Hide "Sign in to sync" in bookmarks [fenix]
   * Bug 40095: Review Mozilla developer notes for 79-81 (including) [tor-browser]
   * Bug 40096: Review closed Mozilla bugs between 79-81 (inclusive) for GeckoView [tor-browser]
   * Bug 40097: Rebase browser patches to 81.0b1 [tor-browser]
   * Bug 40097: Bump allowed_addons.json [fenix]
   * Bug 40098: Implement EOY home screen [fenix]
   * Bug 40100: Resolve startup crashes in debug build [fenix]
   * Bug 40112: Check that caching stylesheets per document group adheres to FPI [tor-browser]
   * Bug 40119: Update Fenix dependencies for 81.1.2 [fenix]
   * Bug 40125: Geckoview: Expose security level interface [tor-browser]
   * Bug 40133: Rebase tor-browser patches to 82.0b1 [tor-browser]
   * Bug 40166: Disable security.certerrors.mitm.auto_enable_enterprise_roots [tor-browser]
   * Bug 40172: Security UI not updated for non-https .onion pages in Fenix [tor-browser]
   * Bug 40173: Initialize security_slider in GeckoView at 4 [tor-browser]
   * Bug 40198: Expose privacy.spoof_english pref [tor-browser]
   * Bug 40199: Avoid using system locale for intl.accept_languages [tor-browser]
   * Translations update
 * Build System
   * Android
     * Update Go to 1.14.10
     * Bug 33556: Add TBB project for android-components
     * Bug 33557: Update Android toolchain for Fenix
     * Bug 33558: Update tor-onion-proxy-library to use toolchain for Fenix
     * Bug 33559: Update tor-android-service to use toolchain for Fenix
     * Bug 33561: Update OpenSSL to use Android NDK 20
     * Bug 33563: Update Tor to use Android NDK 20
     * Bug 33564: Update ZSTD to use Android NDK 20
     * Bug 33626: Add project for GeckoView
     * Bug 33670: Update rbm.conf to match NDK 20
     * Bug 33801: Update Go project to use new Android toolchain
     * Bug 33833: Update Rust project to use Android NDK 20
     * Bug 33927: Add tor-browser-build project for fenix
     * Bug 33935: Fenix's classes5.dex files are not reproducible
     * Bug 33973: Create fat .aar for GeckoView
     * Bug 34011: Bump clang to 9.0.1
     * Bug 34012: Bump cbindgen to 0.14.3
     * Bug 34013: Bump Node to 10.21.0
     * Bug 34014: Enable sqlite3 support in Python
     * Bug 34101: Add tor-browser-build project for application-services
     * Bug 34163: testbuild target is broken for Tor Browser 64 bit
     * Bug 34187: Update zlib to use Android NDK 20
     * Bug 34360: Bump binutils version to 2.35.1
     * Bug 40010: Add nss project for application-services [tor-browser-build]
     * Bug 40011: Add sqlcipher for application-services [tor-browser-build]
     * Bug 40029: Clean-up all projects to remove fennec bits we don't need for fenix [tor-browser-build]
     * Bug 40031: Add licenses for kcp-go and smux. [tor-browser-build]
     * Bug 40039: Remove version_path in nss project [tor-browser-build]
     * Bug 40040: Wire geckoview, application-services, android-components, and fenix together [tor-browser-build]
     * Bug 40054: Adapt build.android script in tor-browser project for fenix [tor-browser-build]
     * Bug 40055: Integrate building Glean in offline mode [tor-browser-build]
     * Bug 40057: Include translations into build process in the fenix world [tor-browser-build]
     * Bug 40058: Build Fenix with tor-android-service and tor-onion-proxy-library [tor-browser-build]
     * Bug 40060: Set Fenix Version Name in build [tor-browser-build]
     * Bug 40061: Remove Android SDK 28 [tor-browser-build]
     * Bug 40065: Bump debootstrap-image ubuntu_version to 20.04.1 [tor-browser-build]
     * Bug 40068: Bump versions for Fenix 81.1.0b1 dependencies [tor-browser-build]
     * Bug 40072: Tor libraries are missing in final .apk after switch to 81.1.0b1 [tor-browser-build]
     * Bug 40076: Use our android-components repo on GitLab [tor-browser-build]
     * Bug 40078: Bump Gradle version for Fenix to 6.5.1 [tor-browser-build]
     * Bug 40084: Generation of AndroidManifest.xml is not reproducible [tor-browser-build]
     * Bug 40085+40086: classes.dex files are not reproducible in Fenix [tor-browser-build]
     * Bug 40087: Deterministically add HTTPS Everywhere into omni.ja [tor-browser-build]
     * Bug 40088+40117: Use MOZ_BUILD_DATE for extension manifest timestamps [tor-browser-build]
     * Bug 40093: Ensure application-services libs do not include libc networking symbols [tor-browser-build]
     * Bug 40094: Aarch64 fenix rust cross-compilation fails [tor-browser-build]
     * Bug 40095: The pattern for the apk variable in build.android is matching too much [tor-browser-build]
     * Bug 40097: Update toolchain for Fenix 82 [tor-browser-build]
     * Bug 40101: Pick up Fenix 81.1.1 [tor-browser-build]
     * Bug 40105: Enhance Gradle dependency script (sort deterministically and exclude .module files) [tor-browser-build]
     * Bug 40106: Support using geckoview as well [tor-browser-build]
     * Bug 40108: android-components does not bundle tooling-glean-gradle archive, only .pom file [tor-browser-build]
     * Bug 40113: Nightly Android should use Nightly branding [tor-browser-build]
     * Bug 40115: Update components for switch to mozilla82-based Fenix [tor-browser-build]
     * Bug 40121: Use updated glean_parser for application-services as well [tor-browser-build]
     * Bug 40124: Remove unused torbrowser-android-all (and related) targets [tor-browser-build]
     * Bug 40125: Remove fenix-* projects [tor-browser-build]
     * Bug 40129: application-services is missing rustc in PATH [tor-browser-build]
     * Bug 40130: More mobile clean-up [tor-browser-build]

Tor Browser 10.0a9 -- October 26 2020
 * Android
   * Update Fenix to 82.1.1
   * Update NoScript to 11.1.3
   * Update OpenSSL to 1.1.1h
   * Bug 30605: Honor privacy.spoof_english
   * Bug 40003: Block starting Tor when setup is not complete [tor-android-service]
   * Bug 40004: "Tor Browser" string is used instead of "Alpha"/"Nightly" for non en-US locales [tor-android-service]
   * Bug 40016: Allow inheriting from AddonCollectionProvider [android-components]
   * Bug 40017: Rebase android-components patches to 60 [android-components]
   * Bug 40019: Expose spoofEnglish pref [android-components]
   * Bug 40020: Disable third-party cookies [android-components]
   * Bug 40021: Force telemetry=false in Fennec settings migration [android-components]
   * Bug 40022: Migrate tor security settings [android-components]
   * Bug 40023: Stop Private Notification Service [android-components]
   * Bug 40024: Disable tracking protection by default [android-components]
   * Bug 40050: Rebase Fenix patches to Fenix 82 [fenix]
   * Bug 40053: Select your security settings panel on start page is confusing [fenix]
   * Bug 40058: Disabling/Enabling addon still shows option to disallow in private mode [fenix]
   * Bug 40062: HTTPS Everywhere is not shown as installed [fenix]
   * Bug 40068: Tor Service closes when changing theme [fenix]
   * Bug 40071: Show only supported locales [fenix]
   * Bug 40073: Use correct branding on About page [fenix]
   * Bug 40076: "Explore privately" not visible [fenix]
   * Bug 40078: Crash at Android startup from background service [fenix]
   * Bug 40082: Security level is reset when the app is killed [fenix]
   * Bug 40083: Locale ordering in BuildConfig is non-deterministic [fenix]
   * Bug 40087: Implement a switch for english locale spoofing [fenix]
   * Bug 40088: Use Tor Browser logo in migration screen [fenix]
   * Bug 40094: Do not use MasterPasswordTipProvider in HomeFragment [fenix]
   * Bug 40095: Hide "Sign in to sync" in bookmarks [fenix]
   * Bug 40097: Bump allowed_addons.json [fenix]
   * Bug 40133: Rebase tor-browser patches to 82.0b1 [tor-browser]
   * Bug 40166: Disable security.certerrors.mitm.auto_enable_enterprise_roots [tor-browser]
   * Bug 40198: Expose privacy.spoof_english pref [tor-browser]
   * Bug 40199: Avoid using system locale for intl.accept_languages [tor-browser]
   * Translations update
 * Build System
   * Android
     * Update Go to 1.14.10
     * Bug 34360: Bump binutils version to 2.35.1
     * Bug 40097: Update toolchain for Fenix 82 [tor-browser-build]
     * Bug 40108: Package tooling-glean-gradle archive, too [tor-browser-build]
     * Bug 40115: Update components for switch to mozilla82-based Fenix [tor-browser-build]
     * Bug 40121: Use updated glean_parser for application-services as well [tor-browser-build]
     * Bug 40124: Remove unused torbrowser-android-all (and related) targets [tor-browser-build]
     * Bug 40125: Remove fenix-* projects [tor-browser-build]
     * Bug 40129: application-services is missing rustc in PATH [tor-browser-build]
     * Bug 40130: More mobile clean-up [tor-browser-build]

Tor Browser 10.5a2 -- October 20 2020
 * Windows + OS X + Linux
   * Update Firefox to 78.4.0esr
   * Update NoScript to 11.1.3
   * Update OpenSSL to 1.1.1h
   * Update Tor Launcher to 0.2.26
     * Translations update
   * Bug 31767: Avoid using intl.locale.requested preference directly
   * Bug 33954: Consider different approach for Bug 2176
   * Bug 40011: Rename tor-browser-brand.ftl to brand.ftl [torbutton]
   * Bug 40012: Fix about:tor not loading some images in 82 [torbutton]
   * Bug 40013: End of year 2020 Fundraising campaign [torbutton]
   * Bug 40016: Fix onion pattern for LTR locales [torbutton]
   * Bug 40139: Update Onboarding icon for 10.0 [tor-browser]
   * Bug 40148: Disable Picture-in-Picture until we investigate and possibly fix it [tor-browser]
   * Bug 40166: Disable security.certerrors.mitm.auto_enable_enterprise_roots [tor-browser]
   * Bug 40192: Backport Mozilla Bug 1658881 [tor-browser]
   * Translations update
 * Windows
   * Bug 40140: Videos stop working with Tor Browser 10.0 on Windows [tor-browser]
 * Build System
   * Windows + OS X + Linux
     * Update Go to 1.14.10
     * Bug 40104: Use our TMPDIR when creating our .mar files [tor-browser-build]
   * Linux
     * Bug 40118: Add missing libdrm dev package to firefox container [tor-browser-build]
   * Windows
     * Bug 34360: Bump binutils to 2.35.1
     * Bug 40051: Remove SOURCE_DATE_EPOCH patch [tor-browser-build]
     * Bug 40131: Remove unused binutils patches [tor-browser-build]

Tor Browser 10.0.2 -- October 20 2020
 * Windows + OS X + Linux
   * Update Firefox to 78.4.0esr
   * Update NoScript to 11.1.3
   * Bug 40192: Backport Mozilla Bug 1658881 [tor-browser]
   * Translations update
 * Linux
   * Bug 40193: Add `AT_EMPTY_PATH` definition [tor-browser]

Tor Browser 10.0.1 -- October 13 2020
 * Windows + OS X + Linux
   * Update NoScript to 11.1.1
   * Bump OpenSSL to 1.1.1h
   * Update Tor Launcher to 0.2.26
   * Bug 31767: Avoid using intl.locale.requested preference directly
   * Bug 40013: End of year 2020 Fundraising campaign [torbutton]
   * Bug 40016: Fix onion pattern for LTR locales [torbutton]
   * Bug 40139: Update Onboarding icon for 10.0 [tor-browser]
   * Bug 40148: Disable Picture-in-Picture until we investigate and possibly fix it [tor-browser]
   * Translations update
 * Windows
   * Bug 40140: Videos stop working with Tor Browser 10.0 on Windows [tor-browser]
 * Build System
   * Windows + OS X + Linux
     * Bump Go to 1.14.9
   * Windows
     * Bug 40051: Remove SOURCE_DATE_EPOCH patch for OpenSSL [tor-browser-build]

Tor Browser 10.0a8 -- October 8 2020
 * Android
   * Update Fenix to 81.1.2
   * Update Tor to *******
   * Update NoScript to 11.0.46
   * Bug 10394: Let Tor Browser update HTTPS Everywhere
   * Bug 11154: Disable TLS 1.0 (and 1.1) by default
   * Bug 16931: Sanitize the add-on blocklist update URL
   * Bug 17374: Disable 1024-DH Encryption by default
   * Bug 21601: Remove unused media.webaudio.enabled pref
   * Bug 30682: Disable Intermediate CA Preloading
   * Bug 30812: Exempt about: pages from Resist Fingerprinting
   * Bug 32886: Separate treatment of @media interaction features for desktop and android
   * Bug 33534: Review FF release notes from FF69 to latest (FF78)
   * Bug 33594: Disable telemetry collection (Glean)
   * Bug 33851: Patch out Parental Controls detection and logging
   * Bug 33856: Set browser.privatebrowsing.forceMediaMemoryCache to True
   * Bug 33862: Fix usages of createTransport API
   * Bug 33962: Uplift patch for bug 5741 (dns leak protection)
   * Bug 34125: API change in protocolProxyService.registerChannelFilter
   * Bug 34338: Disable the crash reporter
   * Bug 34377: Port padlock states for .onion services
   * Bug 34378: Port external helper app prompting
   * Bug 34401: Re-design Connect screen on Android
   * Bug 34402: Re-design Network Settings Screen on Android
   * Bug 34403: UI changes for "Only Private Browsing Mode" on Android
   * Bug 34405: Re-design about:tor on Android
   * Bug 34406: Re-design onion indicators for Android
   * Bug 34407: Review all Fenix menu items
   * Bug 40001: Start Tor as part of the Fenix initialization [fenix]
   * Bug 40001: Generate tor-browser-brand.ftl when importing translations [torbutton]
   * Bug 40002: Ensure system download manager is not used [android-components]
   * Bug 40002: Fix generateNSGetFactory being moved to ComponentUtils [torbutton]
   * Bug 40003: Adapt code for L10nRegistry API changes [torbutton]
   * Bug 40004: Fix noscript message passing for Firefox 79 [torbutton]
   * Bug 40005: Modify WebExtensions Menu [android-components]
   * Bug 40006: "Only Private Browsing Mode" on Android [fenix]
   * Bug 40006: Add Security Level plumbing [android-components]
   * Bug 40007: Port external helper app prompting [android-components]
   * Bug 40007: Move SecurityPrefs initialization to the StartupObserver component [torbutton]
   * Bug 40008: Style fixes for 78 [torbutton]
   * Bug 40009: Change the default search engines [android-components]
   * Bug 40010: Verify Sentry is disabled [fenix]
   * Bug 40011: Verify Leanplum is disabled [fenix]
   * Bug 40011: Hide option for disallowing addons in private mode [android-components]
   * Bug 40012: Verify Adjust is disabled [fenix]
   * Bug 40013: Timestamp is embedded in extension manifest files [android-components]
   * Bug 40013: Verify InstallReferrer is disabled [fenix]
   * Bug 40014: Verify Google Ads ID is disabled [fenix]
   * Bug 40014: Set correct default Security Level [android-components]
   * Bug 40015: Modify Fenix Home Menu [fenix]
   * Bug 40016: Modify Fenix Settings Menu [fenix]
   * Bug 40017: Audit Firefox 68-78 diff for proxy issues [tor-browser]
   * Bug 40018: Disable Push functionality [fenix]
   * Bug 40019: Ensure missing Adjust token does not throw an exception [fenix]
   * Bug 40023: Rebase Tor Browser esr78 patches onto 80 beta [tor-browser]
   * Bug 40026: Implement Security Level settings [fenix]
   * Bug 40028: Implement bootstrapping and about:tor [fenix]
   * Bug 40029: Rebase Fenix patches to 81.1.0b1 [fenix]
   * Bug 40030: Install https-everywhere and noscript addons [fenix]
   * Bug 40031: Hide Mozilla-specific items on About page [fenix]
   * Bug 40032: Disallow Cleartext Traffic [fenix]
   * Bug 40034: Disable PWA [fenix]
   * Bug 40038: Review RemoteSettings for ESR 78 [tor-browser]
   * Bug 40035: Maybe hide Quick Start in release [fenix]
   * Bug 40039: Implement Bridge configuration from Connect screen [fenix]
   * Bug 40040: Investigate why bootstrapping fails [fenix]
   * Bug 40041: Implement Network settings [fenix]
   * Bug 40042: Timestamp is embedded in extension manifest files [fenix]
   * Bug 40044: Fixup Connect, Onboarding, and Home screens [fenix]
   * Bug 40048: Disable various ESR78 features via prefs [tor-browser]
   * Bug 40054: Search engines on mobile Tor Browser don't match the desktop ones [fenix]
   * Bug 40058: Hide option for disallowing addon in private mode [fenix]
   * Bug 40061: Do not show "Send to device" in sharing menu [fenix]
   * Bug 40063: Do not sort search engines alphabetically [fenix]
   * Bug 40064: Modify Nighty (and Debug) build variants [fenix]
   * Bug 40066: Remove default bridge ************* [tor-browser-build]
   * Bug 40066: Enable Snowflake on Beta [fenix]
   * Bug 40066: Update existing prefs for ESR 78 [tor-browser]
   * Bug 40067: Make date on Fenix about page reproducible [fenix]
   * Bug 40069: Add helpers for message passing with extensions [tor-browser]
   * Bug 40072: Bug 40072: Disable Tracking Protection [fenix]
   * Bug 40073: Repack omni.ja to include builtin HTTPS Everywhere [tor-browser-build]
   * Bug 40073: Disable remote Public Suffix List fetching [tor-browser]
   * Bug 40082: Let JavaScript on safest setting handled by NoScript again [tor-browser]
   * Bug 40091: Load HTTPS Everywhere as a builtin addon [tor-browser]
   * Bug 40095: Review Mozilla developer notes for 79-81 (including) [tor-browser]
   * Bug 40096: Review closed Mozilla bugs between 79-81 (inclusive) for GeckoView [tor-browser]
   * Bug 40097: Rebase browser patches to 81.0b1 [tor-browser]
   * Bug 40098: Initialize torbutton for Geckoview and make sure its features work as expected in Fenix [tor-browser]
   * Bug 40112: Check that caching stylesheets per document group adheres to FPI [tor-browser]
   * Bug 40119: Update Fenix dependencies for 81.1.2 [fenix]
   * Bug 40125: Geckoview: Expose security level interface [tor-browser]
   * Bug 40172: Security UI not updated for non-https .onion pages in Fenix [tor-browser]
   * Bug 40173: Initialize security_slider in GeckoView at 4 [tor-browser]
   * Translations update
 * Build System
   * Android
     * Bump Go to 1.14.7
     * Bug 33556: Add TBB project for android-components
     * Bug 33557: Update Android toolchain for Fenix
     * Bug 33558: Update tor-onion-proxy-library to use toolchain for Fenix
     * Bug 33559: Update tor-android-service to use toolchain for Fenix
     * Bug 33561: Update OpenSSL to use Android NDK 20
     * Bug 33563: Update Tor to use Android NDK 20
     * Bug 33564: Update ZSTD to use Android NDK 20
     * Bug 33626: Add project for GeckoView
     * Bug 33670: Update rbm.conf to match NDK 20
     * Bug 33801: Update Go project to use new Android toolchain
     * Bug 33833: Update Rust project to use Android NDK 20
     * Bug 33927: Add tor-browser-build project for fenix
     * Bug 33935: Fenix's classes5.dex files are not reproducible
     * Bug 33973: Create fat .aar for GeckoView
     * Bug 34011: Bump clang to 9.0.1
     * Bug 34012: Bump cbindgen to 0.14.3
     * Bug 34013: Bump Node to 10.21.0
     * Bug 34014: Enable sqlite3 support in Python
     * Bug 34101: Add tor-browser-build project for application-services
     * Bug 34163: testbuild target is broken for Tor Browser 64 bit
     * Bug 34187: Update zlib to use Android NDK 20
     * Bug 40010: Add nss project for application-services [tor-browser-build]
     * Bug 40011: Add sqlcipher for application-services [tor-browser-build]
     * Bug 40029: Clean-up all projects to remove fennec bits we don't need for fenix [tor-browser-build]
     * Bug 40031: Add licenses for kcp-go and smux. [tor-browser-build]
     * Bug 40039: Remove version_path in nss project [tor-browser-build]
     * Bug 40040: Wire geckoview, application-services, android-components, and fenix together [tor-browser-build]
     * Bug 40054: Adapt build.android script in tor-browser project for fenix [tor-browser-build]
     * Bug 40055: Integrate building Glean in offline mode [tor-browser-build]
     * Bug 40057: Include translations into build process in the fenix world [tor-browser-build]
     * Bug 40058: Build Fenix with tor-android-service and tor-onion-proxy-library [tor-browser-build]
     * Bug 40060: Set Fenix Version Name in build [tor-browser-build]
     * Bug 40061: Remove Android SDK 28 [tor-browser-build]
     * Bug 40065: Bump debootstrap-image ubuntu_version to 20.04.1 [tor-browser-build]
     * Bug 40068: Bump versions for Fenix 81.1.0b1 dependencies [tor-browser-build]
     * Bug 40072: Tor libraries are missing in final .apk after switch to 81.1.0b1 [tor-browser-build]
     * Bug 40076: Use our android-components repo on GitLab [tor-browser-build]
     * Bug 40078: Bump Gradle version for Fenix to 6.5.1 [tor-browser-build]
     * Bug 40084: Generation of AndroidManifest.xml is not reproducible [tor-browser-build]
     * Bug 40085+40086: classes.dex files are not reproducible in Fenix [tor-browser-build]
     * Bug 40087: Deterministically add HTTPS Everywhere into omni.ja [tor-browser-build]
     * Bug 40088+40117: Use MOZ_BUILD_DATE for extension manifest timestamps [tor-browser-build]
     * Bug 40093: Ensure application-services libs do not include libc networking symbols [tor-browser-build]
     * Bug 40094: Aarch64 fenix rust cross-compilation fails [tor-browser-build]
     * Bug 40095: The pattern for the apk variable in build.android is matching too much [tor-browser-build]
     * Bug 40101: Pick up Fenix 81.1.1 [tor-browser-build]
     * Bug 40105: Enhance Gradle dependency script (sort deterministically and exclude .module files) [tor-browser-build]
     * Bug 40106: Support using geckoview as well [tor-browser-build]
     * Bug 40108: android-components does not bundle tooling-glean-gradle archive, only .pom file [tor-browser-build]
     * Bug 40113: Nightly Android should use Nightly branding [tor-browser-build]

Tor Browser 10.5a1 -- September 22 2020
 * Windows + OS X + Linux
   * Update Firefox to 78.3.0esr
   * Update Tor to *******
   * Update Tor Launcher to 0.2.25
     * Translations update
   * Update NoScript to 11.0.44
     * Bug 40093: Youtube videos on safer produce an error [tor-browser]
   * Translations update
 * Linux
   * Bug 40089: Remove CentOS 6 support for Tor Browser 10.5 [tor-browser]
 * Build System
   * Linux
     * Bug 26238: Move to Debian Jessie for our Linux builds
     * Bug 31729: Support Wayland
     * Bug 40041: Remove CentOS 6 support for 10.5 series [tor-browser-build]
     * Bug 40103: Add i386 pkg-config path for linux-i686 [tor-browser-build]

Tor Browser 10.0 -- September 22 2020
 * Windows + OS X + Linux
   * Update Firefox to 78.3.0esr
   * Update Tor to *******
   * Update Tor Launcher to 0.2.25
     * Bug 32174: Replace XUL <textbox> with <html:input>
     * Bug 33890: Rename XUL files to XHTML
     * Bug 33862: Fix usages of createTransport API
     * Bug 33906: Fix Tor-Launcher issues for Firefox 75
     * Bug 33998: Use CSS grid instead of XUL grid
     * Bug 34164: Tor Launcher deadlocks during startup (Firefox 77)
     * Bug 34206: Tor Launcher button labels are missing (Firefox 76)
     * Bug 40002: After rebasing to 80.0b2 moat is broken [tor-launcher]
     * Translations update
   * Update NoScript to 11.0.44
     * Bug 40093: Youtube videos on safer produce an error [tor-browser]
   * Translations update
   * Bug 10394: Let Tor Browser update HTTPS Everywhere
   * Bug 11154: Disable TLS 1.0 (and 1.1) by default
   * Bug 16931: Sanitize the add-on blocklist update URL
   * Bug 17374: Disable 1024-DH Encryption by default
   * Bug 21601: Remove unused media.webaudio.enabled pref
   * Bug 30682: Disable Intermediate CA Preloading
   * Bug 30812: Exempt about: pages from Resist Fingerprinting
   * Bug 31918+33533+40024+40037: Rebase Tor Browser esr68 patches for ESR 78 [tor-browser]
   * Bug 32612: Update MAR_CHANNEL_ID for the alpha
   * Bug 32886: Separate treatment of @media interaction features for desktop and android
   * Bug 33534: Review FF release notes from FF69 to latest (FF78)
   * Bug 33697: Use old search config based on list.json
   * Bug 33721: PDF Viewer is not working in the safest security level
   * Bug 33734: Set MOZ_NORMANDY to False
   * Bug 33737: Fix aboutDialog.js error for Firefox nightlies
   * Bug 33848: Disable Enhanced Tracking Protection
   * Bug 33851: Patch out Parental Controls detection and logging
   * Bug 33852: Clean up about:logins to not mention Sync
   * Bug 33856: Set browser.privatebrowsing.forceMediaMemoryCache to True
   * Bug 33862: Fix usages of createTransport API
   * Bug 33867: Disable password manager and password generation
   * Bug 33890: Rename XUL files to XHTML
   * Bug 33892: Add brandProductName to brand.dtd and brand.properties
   * Bug 33962: Uplift patch for bug 5741 (dns leak protection)
   * Bug 34125: API change in protocolProxyService.registerChannelFilter
   * Bug 40001: Generate tor-browser-brand.ftl when importing translations [torbutton]
   * Bug 40002: Remove about:pioneer [tor-browser]
   * Bug 40002: Fix generateNSGetFactory being moved to ComponentUtils [torbutton]
   * Bug 40003: Adapt code for L10nRegistry API changes [torbutton]
   * Bug 40005: Initialize the identity UI before setting up the circuit display [torbutton]
   * Bug 40006: Fix new identity for 81 [torbutton]
   * Bug 40007: Move SecurityPrefs initialization to the StartupObserver component [torbutton]
   * Bug 40008: Style fixes for 78 [torbutton]
   * Bug 40017: Audit Firefox 68-78 diff for proxy issues [tor-browser]
   * Bug 40022: Update new icons in Tor Browser branding [tor-browser]
   * Bug 40025: Revert add-on permissions due to Mozilla's 1560059 [tor-browser]
   * Bug 40036: Remove product version/update channel from #13379 patch [tor-browser]
   * Bug 40038: Review RemoteSettings for ESR 78 [tor-browser]
   * Bug 40048: Disable various ESR78 features via prefs [tor-browser]
   * Bug 40059: Verify our external helper patch is still working [tor-browser]
   * Bug 40066: Update existing prefs for ESR 78 [tor-browser]
   * Bug 40066: Remove default bridge ************* [tor-browser-build]
   * Bug 40073: Disable remote Public Suffix List fetching [tor-browser]
   * Bug 40073: Repack omni.ja to include builtin HTTPS Everywhere [tor-browser-build]
   * Bug 40078: Backport patches for bug 1651680 for now [tor-browser]
   * Bug 40082: Let JavaScript on safest setting handled by NoScript again [tor-browser]
   * Bug 40088: Moat "Submit" button does not work
   * Bug 40090: Disable v3 add-on blocklist for now [tor-browser]
   * Bug 40091: Load HTTPS Everywhere as a builtin addon [tor-browser]
   * Bug 40102: Fix UI bugs in Tor Browser 10.0 alpha [tor-browser]
   * Bug 40106: Cannot install addons in full screen mode [tor-browser]
   * Bug 40109: Playing video breaks after reloading pages [tor-browser]
   * Bug 40119: Enable v3 extension blocklisting again [tor-browser]
 * Windows
   * Bug 33855: Don't use site's icon as window icon in Windows in private mode
   * Bug 40061: Omit the Windows default browser agent from the build [tor-browser]
 * OS X
   * Bug 32252: Tor Browser does not display correctly in VMWare Fusion on macOS (mojave)
 * Build System
   * Windows + OS X + Linux
     * Bump Go to 1.14.7
     * Bug 31845: Bump GCC version to 9.3.0
     * Bug 34011: Bump clang to 9.0.1
     * Bug 34014: Enable sqlite3 support in Python
     * Bug 34390: Don't copy DBM libraries anymore
     * Bug 34391: Remove unused --enable-signmar option
     * Bug 40004: Adapt Rust project for Firefox 78 ESR [tor-browser-build]
     * Bug 40005: Adapt Node project for Firefox 78 ESR [tor-browser-build]
     * Bug 40006: Adapt cbindgen for Firefox 78 ESR [tor-browser-build]
     * Bug 40037: Move projects over to clang-source [tor-browser-build]
     * Bug 40026: Fix full .mar creation for esr78 [tor-browser-build]
     * Bug 40027: Fix incremental .mar creation for esr78 [tor-browser-build]
     * Bug 40028: Do not reference unset env variables [tor-browser-build]
     * Bug 40031: Add licenses for kcp-go and smux. [tor-browser-build]
     * Bug 40045: Fix complete .mar file creation for dmg2mar [tor-browser-build]
     * Bug 40065: Bump debootstrap-image ubuntu_version to 20.04.1 [tor-browser-build]
     * Bug 40087: Deterministically add HTTPS Everywhere into omni.ja [tor-browser-build]
   * Windows
     * Bug 34230: Update Windows toolchain for Firefox 78 ESR
     * Bug 40015: Use only 64bit fxc2 [tor-browser-build]
     * Bug 40017: Enable stripping again on Windows [tor-browser-build]
     * Bug 40052: Bump NSIS to 3.06.1 [tor-browser-build]
     * Bug 40061: Omit the Windows default browser agent from the build [tor-browser]
     * Bug 40071: Be explicit about no SEH with mingw-w64 on 32bit systems [tor-browser-build]
     * Bug 40077: Don't pass --no-insert-timestamp when building Firefox [tor-browser-build]
     * Bug 40090: NSIS 3.06.1 based builds are not reproducible anymore [tor-browser-build]
   * OS X
     * Bug 34229: Update macOS toolchain for Firefox 78 ESR
     * Bug 40003: Update cctools version for Firefox 78 ESR [tor-browser-build]
     * Bug 40018: Add libtapi project for cctools [tor-browser-build]
     * Bug 40019: Ship our own runtime library for macOS [tor-browser-build]
   * Linux
     * Bug 34359: Adapt abicheck.cc to deal with newer GCC version
     * Bug 34386: Fix up clang compilation on Linux
     * Bug 40053: Also create the langpacks tarball for non-release builds [tor-browser-build]

Tor Browser 10.0a7 -- September 14 2020
 * Windows + OS X + Linux
   * Update Tor Launcher to 0.2.24
   * Update NoScript to 11.0.43
   * Translations update
   * Bug 10394: Let Tor Browser update HTTPS Everywhere
   * Bug 32017: Use ExtensionStorageIDB again
   * Bug 40006: Fix new identity for 81 [torbutton]
   * Bug 40007: Move SecurityPrefs initialization to the StartupObserver component [torbutton]
   * Bug 40008: Style fixes for 78 [torbutton]
   * Bug 40066: Remove default bridge ************* [tor-browser-build]
   * Bug 40073: Repack omni.ja to include builtin HTTPS Everywhere [tor-browser-build]
   * Bug 40091: Load HTTPS Everywhere as a builtin addon [tor-browser]
   * Bug 40102: Fix UI bugs in Tor Browser 10.0 alpha [tor-browser]
   * Bug 40109: Playing video breaks after reloading pages [tor-browser]
   * Big 40119: Enable v3 extension blocklisting again [tor-browser]
 * Build System
   * Windows + OS X + Linux
     * Bump Go to 1.14.7
     * Bug 40031: Add licenses for kcp-go and smux. [tor-browser-build]
     * Bug 40045: Fix complete .mar file creation for dmg2mar [tor-browser-build]
     * Bug 40065: Bump debootstrap-image ubuntu_version to 20.04.1 [tor-browser-build]
     * Bug 40087: Deterministically add HTTPS Everywhere into omni.ja [tor-browser-build]
   * Windows
     * Bug 40052: Bump NSIS to 3.06.1 [tor-browser-build]
     * Bug 40071: Be explicit about no SEH with mingw-w64 on 32bit systems [tor-browser-build]
     * Bug 40077: Don't pass --no-insert-timestamp when building Firefox [tor-browser-build]
     * Bug 40090: NSIS 3.06.1 based builds are not reproducible anymore [tor-browser-build]

Tor Browser 10.0a6 -- August 26 2020
 * All Platforms
   * Update HTTPS Everywhere to 2020.08.13
 * Windows + OS X + Linux
   * Update Firefox to 78.2.0esr
   * Update Tor Launcher to 0.2.23
     * Bug 40002: After rebasing to 80.0b2 moat is broken [tor-launcher]
     * Translations update
   * Update NoScript to 11.0.39
   * Bug 21601: Remove unused media.webaudio.enabled pref
   * Bug 40002: Remove about:pioneer [tor-browser]
   * Bug 40082: Let JavaScript on safest setting handled by NoScript again [tor-browser]
   * Bug 40088: Moat "Submit" button does not work
   * Bug 40090: Disable v3 add-on blocklist for now [tor-browser]
 * OS X
   * Bug 40015: Tor Browser broken on MacOS 11 Big Sur
 * Android
   * Update Firefox to 68.12.0esr
   * Update NoScript to 11.0.38
   * Update Tor to *******-rc
 * Build System
   * Windows + OS X + Linux
     * Bump Go to 1.13.15
   * Linux
     * Bug 40053: Also create the langpacks tarball for non-release builds [tor-browser-build]

Tor Browser 9.5.4 -- August 25 2020
 * All Platforms
   * Update Firefox to 68.12.0esr
   * Update HTTPS Everywhere to 2020.08.13
   * Update NoScript to 11.0.38
 * Windows + OS X + Linux
   * Bug 40019: Onion-Location should not be processed on .onion webpages [tor-browser]
 * OS X
   * Bug 40015: Tor Browser is broken on MacOS 11 Big Sur [tor-browser]

Tor Browser 10.0a5 -- August 18 2020
 * Windows + OS X + Linux
   * Update Firefox to 78.1.0esr
   * Update Tor to *******-rc
   * Update Tor Launcher to 0.2.22
     * Bug 32174: Replace XUL <textbox> with <html:input>
     * Bug 33890: Rename XUL files to XHTML
     * Bug 33862: Fix usages of createTransport API
     * Bug 33906: Fix Tor-Launcher issues for Firefox 75
     * Bug 33998: Use CSS grid instead of XUL grid
     * Bug 34164: Tor Launcher deadlocks during startup (Firefox 77)
     * Bug 34206: Tor Launcher button labels are missing (Firefox 76)
     * Translations update
   * Update NoScript to 11.0.37
   * Bug 11154: Disable TLS 1.0 (and 1.1) by default
   * Bug 16931: Sanitize the add-on blocklist update URL
   * Bug 17374: Disable 1024-DH Encryption by default
   * Bug 30682: Disable Intermediate CA Preloading
   * Bug 30812: Exempt about: pages from Resist Fingerprinting
   * Bug 31918+33533+40024+40037: Rebase Tor Browser esr68 patches for ESR 78 [tor-browser]
   * Bug 32612: Update MAR_CHANNEL_ID for the alpha
   * Bug 32886: Separate treatment of @media interaction features for desktop and android
   * Bug 33534: Review FF release notes from FF69 to latest (FF78)
   * Bug 33697: Use old search config based on list.json
   * Bug 33721: PDF Viewer is not working in the safest security level
   * Bug 33734: Set MOZ_NORMANDY to False
   * Bug 33737: Fix aboutDialog.js error for Firefox nightlies
   * Bug 33848: Disable Enhanced Tracking Protection
   * Bug 33851: Patch out Parental Controls detection and logging
   * Bug 33852: Clean up about:logins to not mention Sync
   * Bug 33856: Set browser.privatebrowsing.forceMediaMemoryCache to True
   * Bug 33862: Fix usages of createTransport API
   * Bug 33867: Disable password manager and password generation
   * Bug 33890: Rename XUL files to XHTML
   * Bug 33892: Add brandProductName to brand.dtd and brand.properties
   * Bug 33962: Uplift patch for bug 5741 (dns leak protection)
   * Bug 34125: API change in protocolProxyService.registerChannelFilter
   * Bug 40001: Generate tor-browser-brand.ftl when importing translations [torbutton]
   * Bug 40002: Fix generateNSGetFactory being moved to ComponentUtils [torbutton]
   * Bug 40003: Adapt code for L10nRegistry API changes [torbutton]
   * Bug 40005: Initialize the identity UI before setting up the circuit display [torbutton]
   * Bug 40016: Update Snowflake to discover NAT type [tor-browser-build]
   * Bug 40017: Audit Firefox 68-78 diff for proxy issues [tor-browser]
   * Bug 40022: Update new icons in Tor Browser branding [tor-browser]
   * Bug 40025: Revert add-on permissions due to Mozilla's 1560059 [tor-browser]
   * Bug 40036: Remove product version/update channel from #13379 patch [tor-browser]
   * Bug 40038: Review RemoteSettings for ESR 78 [tor-browser]
   * Bug 40048: Disable various ESR78 features via prefs [tor-browser]
   * Bug 40059: Verify our external helper patch is still working [tor-browser]
   * Bug 40066: Update existing prefs for ESR 78 [tor-browser]
   * Bug 40073: Disable remote Public Suffix List fetching [tor-browser]
   * Bug 40078: Backport patches for bug 1651680 for now [tor-browser]
   * Bug 40106: Cannot install addons in full screen mode [tor-browser]
   * Translations update
 * Windows
   * Bug 33855: Don't use site's icon as window icon in Windows in private mode
   * Bug 40061: Omit the Windows default browser agent from the build [tor-browser]
 * OS X
   * Bug 32252: Tor Browser does not display correctly in VMWare Fusion on macOS (mojave)
 * Build System
   * Windows + OS X + Linux
     * Bug 31845: Bump GCC version to 9.3.0
     * Bug 34011: Bump clang to 9.0.1
     * Bug 34014: Enable sqlite3 support in Python
     * Bug 34390: Don't copy DBM libraries anymore
     * Bug 34391: Remove unused --enable-signmar option
     * Bug 40004: Adapt Rust project for Firefox 78 ESR [tor-browser-build]
     * Bug 40005: Adapt Node project for Firefox 78 ESR [tor-browser-build]
     * Bug 40006: Adapt cbindgen for Firefox 78 ESR [tor-browser-build]
     * Bug 40037: Move projects over to clang-source [tor-browser-build]
     * Bug 40026: Fix full .mar creation for esr78 [tor-browser-build]
     * Bug 40027: Fix incremental .mar creation for esr78 [tor-browser-build]
     * Bug 40028: Do not reference unset env variables [tor-browser-build]
   * Windows
     * Bug 34230: Update Windows toolchain for Firefox 78 ESR
     * Bug 40015: Use only 64bit fxc2 [tor-browser-build]
     * Bug 40017: Enable stripping again on Windows [tor-browser-build]
     * Bug 40061: Omit the Windows default browser agent from the build [tor-browser]
   * OS X
     * Bug 34229: Update macOS toolchain for Firefox 78 ESR
     * Bug 40003: Update cctools version for Firefox 78 ESR [tor-browser-build]
     * Bug 40018: Add libtapi project for cctools [tor-browser-build]
     * Bug 40019: Ship our own runtime library for macOS [tor-browser-build]
   * Linux
     * Bug 34359: Adapt abicheck.cc to deal with newer GCC version
     * Bug 34386: Fix up clang compilation on Linux

Tor Browser 10.0a4 -- July 28 2020
 * All Platforms
   * Update Firefox to 68.11.0esr
   * Update NoScript to 11.0.34
   * Update Tor to *******-alpha
 * Windows + OS X + Linux
   * Bug 40019: Onion-Location should not be processed on .onion webpages [tor-browser]

Tor Browser 9.5.3 -- July 28 2020
 * All Platforms
   * Update Firefox to 68.11.0esr
   * Update NoScript to 11.0.34
   * Update Tor to *******

Tor Browser 10.0a3 -- July 7 2020
 * Android
   * Update Firefox to 68.10.1esr

Tor Browser 9.5.2 -- July 7 2020
 * Android
   * Update Firefox to 68.10.1esr

Tor Browser 10.0a2 -- June 30 2020
 * All Platforms
   * Update Firefox to 68.10.0esr
   * Update NoScript to 11.0.32
   * Update Tor to *******-alpha
   * Translations update
   * Bug 34209: about:tor and about:tbupdate fail to load in debug build
   * Bug 34250: Only listen to 'started' in noscript-control.js
   * Bug 34383: Accept request if GetHost() in mixed content blocking check fails
 * Windows + OS X + Linux
   * Bug 34361: "Prioritize .onion sites when known" appears under General
   * Bug 34362: Improve Onion Service Authentication prompt
   * Bug 34369: Fix learn more link in Onion Auth prompt
   * Bug 34379: Fix learn more for Onion-Location
 * Android
   * Bug 34372: Disable GeckoNetworkManager

Tor Browser 9.5.1 -- June 30 2020
 * All Platforms
   * Update Firefox to 68.10.0esr
   * Update NoScript to 11.0.32
   * Translations update
   * Bug 40009: Improve tor's client auth stability
 * Windows + OS X + Linux
   * Bug 34361: "Prioritize .onion sites when known" appears under General
   * Bug 34362: Improve Onion Service Authentication prompt
   * Bug 34369: Fix learn more link in Onion Auth prompt
   * Bug 34379: Fix learn more for Onion-Location
   * Bug 34347: The Tor Network part on the onboarding is not new anymore

Tor Browser 10.0a1 -- June 2 2020
 * All Platforms
   * Update Firefox to 68.9.0esr
   * Update HTTPS-Everywhere to 2020.5.20
   * Translations update
 * Windows + OS X + Linux
   * Update Tor Launcher to ********
     * Translations update
   * Bug 34321: Add Learn More onboarding item
   * Bug 34347: The Tor Network part on the onboarding is not new anymore
 * Linux
   * Bug 34315: Avoid reading policies from /etc/firefox on Linux
 * Android
   * Bug 30318: Integrate snowflake into mobile Tor Browser
   * Bug 34219: Enable ZSTD support properly for Android
 * Build System
   * Windows
     * Bug 31128: Move Windows containers to Debian 10
   * Mac OS X
     * Bug 31129: Move macOS containers to Debian 10
   * Android
     * Bug 28672: Android reproducible build of Snowflake

Tor Browser 9.5 -- June 2 2020
 * All Platforms
   * Update Firefox to 68.9.0esr
   * Update HTTPS-Everywhere to 2020.5.20
   * Update NoScript to 11.0.26
   * Update Tor to *******
   * Translations update
   * Bug 21549: Disable wasm for now until it is properly audited
   * Bug 27268: Preferences clean-up in Torbutton code
   * Bug 28745: Remove torbutton.js unused code
   * Bug 28746: Remove torbutton isolation and fp prefs sync
   * Bug 30237: Control port module improvements for v3 client authentication
   * Bug 30786: Add th locale
   * Bug 30787: Add lt locale
   * Bug 30788: Add ms locale
   * Bug 30851: Move default preferences to 000-tor-browser.js
   * Bug 30888: Move torbutton_util.js to modules/utils.js
   * Bug 31134: Govern graphite again by security settings
   * Bug 31395: Remove inline script in aboutTor.xhtml
   * Bug 31499: Update libevent to 2.1.11-stable
     * Bug 33877: Disable Samples and Regression tests For Libevent Build
   * Bug 31573: Catch SessionStore.jsm exception
   * Bug 32318: Backport Mozilla's fix for bug 1534339
   * Bug 32414: Make Services.search.addEngine obey FPI
   * Bug 32493: Disable MOZ_SERVICES_HEALTHREPORT
   * Bug 32618: Backport fixes from Mozilla bugs 1467970 and 1590526
   * Bug 33342: Avoid disconnect search addon error after removal
   * Bug 33726: Fix patch for #23247: Communicating security expectations for .onion
   * Bug 34157: Backport fix for Mozilla Bug 1511941
 * Windows + OS X + Linux
   * Update Tor Launcher to ********
     * Translations update
     * Bug 19757: Support on-disk storage of v3 client auth keys
     * Bug 30237: Add v3 onion services client authentication prompt
     * Bug 30786: Add th locale
     * Bug 30787: Add lt locale
     * Bug 30788: Add ms locale
     * Bug 33514: non-en-US Tor Browser 9.5a6 won't start up
   * Bug 19251: Show improved error pages for onion service errors
   * Bug 19757: Support on-disk storage of v3 client auth keys
   * Bug 21952: Implement Onion-Location
   * Bug 27604: Fix broken Tor Browser after moving it to a different directory
   * Bug 28005: Implement .onion alias urlbar rewrites
   * Bug 30237: Improve TBB UI of hidden service client authorization
   * Bug 32076: Upgrade to goptlib v1.1.0
   * Bug 32220: Improve the letterboxing experience
   * Bug 32418: Allow updates to be disabled via an enterprise policy.
   * Bug 32470: Backport fix for bug 1590538
   * Bug 32645: Update URL bar onion indicators
   * Bug 32658: Create a new MAR signing key
   * Bug 32674: Point the about:tor "Get involved" link to the community portal
   * Bug 32767: Remove Disconnect search
   * Bug 33698: Update "About Tor Browser" links in Tor Browser
   * Bug 33707: Swap out onion icon in circuit display with new one
   * Bug 34032: Use Securedrop's Official https-everywhere ruleset
   * Bug 34196: Update site info URL with the onion name
   * Bug 34321: Add Learn More onboarding item
 * Windows
   * Bug 22919: Improve the random number generator for the boundaries in multipart/form-data
   * Bug 29614: Use SHA-256 algorithm for Windows timestamping
   * Bug 33113: Bump NSIS version to 3.05
 * OS X
   * Bug 32505: Tighten our rules in our entitlements file for macOS
 * Linux
   * Bug 27903: Tor Browser 8 does not respect gtk3 settings
   * Bug 34315: Avoid reading policies from /etc/firefox on Linux
 * Android
   * Bug 26529: Notify user about possible proxy-bypass before opening external app
   * Bug 30767: Custom obfs4 bridge does not work on Tor Browser for Android
   * Bug 32303: Obfs4 is broken on Android Q
   * Bug 33359: Use latest Version of TOPL and Remove Patches
   * Bug 33931: obfs4 bridges are used instead of meek if meek is selected in Tor Browser for Android alpha
 * Build System
   * All Platforms
     * Bump Go to 1.13.11
     * Bug 33380: Add *.json to sha256sums-unsigned-build.txt
   * Windows
     * Bug 33802: --enable-secure-api is not supported anymore in mingw-w64
   * Linux
     * Bug 32976: Build and bundle geckodriver
     * Bug 34242: Fix creation of Linux containers
   * Android
     * Bug 28765: LibEvent Build for Android
     * Bug 28766: Tor Build for Android
     * Bug 28803: Integrate building Pluggable Transports for Android
     * Bug 30461: Clean up tor-android-service project
     * Bug 32993: Package Tor With Tor Android Service Project
     * Bug 33685: Add Support for Building zlib for Android

Tor Browser 9.5a13 -- May 20 2020
 * All Platforms
   * Bump NoScript to 11.0.26
   * Bump Tor to *******
   * Translations update
   * Bug 34157: Backport fix for Mozilla Bug 1511941
 * Windows + OS X + Linux
   * Update Tor Launcher to ********
     * Translations update
   * Bug 34196: Update site info URL with the onion name
   * Bug 34043: Update snowflake to persist sessions across proxies
 * Windows
   * Bug 33113: Bump NSIS version to 3.05
 * Build System
   * All Platforms
     * Bump Go to 1.13.11
   * Linux
     * Bug 34242: Fix creation of Linux containers

Tor Browser 9.5a12 -- May 7 2020
 * All Platforms
   * Update Firefox to 68.8.0esr
   * Bump NoScript to 11.0.25
   * Bump Tor to *******-rc
   * Translations update
   * Bug 31499: Update libevent to 2.1.11-stable
     * Bug 33877: Disable Samples and Regression tests For Libevent Build
   * Bug 33630: Remove noisebridge01 default bridge
   * Bug 33726: Fix patch for #23247: Communicating security expectations for .onion
   * Bug 34017: Bump openssl version to 1.1.1g
 * Windows + OS X + Linux
   * Update Tor Launcher to ********
     * Translations update
   * Bug 33576: Update pion-webrtc version to 2.2.3
   * Bug 32418: Allow updates to be disabled via an enterprise policy.
   * Bug 34032: Use Securedrop's Official https-everywhere ruleset
   * Bug 33698: Update "About Tor Browser" links in Tor Browser
 * Windows
   * Bug 29614: Use SHA-256 algorithm for Windows timestamping
 * Android
   * Bug 33359: Use latest Version of TOPL and Remove Patches
   * Bug 33931: obfs4 bridges are used instead of meek if meek is selected in Tor Browser for Android alpha
 * Build System
   * All Platforms
     * Bug 32027: Bump Go to 1.13.10
   * Android
     * Bug 28765: LibEvent Build for Android
     * Bug 28766: Tor Build for Android
     * Bug 32993: Package Tor With Tor Android Service Project
     * Bug 33685: Add Support for Building zlib for Android
   * Windows
     * Bug 33802: --enable-secure-api is not supported anymore in mingw-w64

Tor Browser 9.0.10 -- May 5 2020
 * All Platforms
   * Update Firefox to 68.8.0esr
   * Bump NoScript to 11.0.25
 * Windows + OS X + Linux
   * Bug 34017: Bump openssl version to 1.1.1g

Tor Browser 9.5a11 -- April 8 2020
 * All Platforms
   * Update Firefox to 68.7.0esr
   * Bump Https-Everywhere to 2020.3.16
   * Bump NoScript to 11.0.23
   * Translations update
   * Bug 33342: Avoid disconnect search addon error after removal
   * Bug 33482: Update about:tor donate string
 * Windows + OS X + Linux
   * Bug 19251: Show improved error pages for onion service errors
   * Bug 21952: Implement Onion-Location
   * Bug 28005: Implement .onion alias urlbar rewrites
   * Bug 33693: Bump Snowflake to ea01bf41c3
   * Bug 33707: Swap out onion icon in circuit display with new one
   * Bug 33723: Bump openssl version to 1.1.1f
   * Bug 33761: Remove unnecessary snowflake dependencies
   * Bug 33771: Update some existing licenses and add Libevent license
 * Android
   * Translations update
 * Build System
   * Windows
     * Bug 33805: Remove escape-openssldir.patch

Tor Browser 9.0.9 -- April 7 2020
 * All Platforms
   * Update Firefox to 68.7.0esr
   * Bump NoScript to 11.0.23
   * Bug 33630: Remove noisebridge01 default bridge
 * Windows + OS X + Linux
   * Bug 33771: Update some existing licenses and add Libevent license
   * Bug 33723: Bump openssl version to 1.1.1f
 * Windows
   * Bug 33805: Remove escape-openssldir.patch

Tor Browser 9.5a10 -- April 5 2020
 * All Platforms
   * Mozilla Bug 1620818 - Release nsDocShell::mContentViewer properly
   * Mozilla Bug 1626728 - Normalize shutdown

Tor Browser 9.0.8 -- April 5 2020
 * All Platforms
   * Mozilla Bug 1620818 - Release nsDocShell::mContentViewer properly
   * Mozilla Bug 1626728 - Normalize shutdown

Tor Browser 9.5a9 -- March 25 2020
 * All Platforms
   * Translations update
   * Bump NoScript to 11.0.21
   * Bug 33613: Disable Javascript on Safest security level
   * Bug 33342: Avoid disconnect search addon error after removal
 * Windows + OS X + Linux
   * Bump Tor to *******-alpha
   * Update Tor Launcher to ********
     * Translations update

Tor Browser 9.0.7 -- March 20 2020
 * All Platforms
   * Bump NoScript to 11.0.19
   * Bump Https-Everywhere to 2020.3.16
   * Bug 33613: Disable Javascript on Safest security level
 * Windows + OS X + Linux
   * Bump Tor to *******

Tor Browser 9.5a8 -- March 13 2020
 * All Platforms
   * Update Firefox to 68.6.0esr
   * Bump NoScript to 11.0.15
     * Bug 33430: Disable downloadable fonts on Safest security level
   * Translations update
 * Build System
   * Windows
     * Bug 33535: Patch openssl to use SOURCE_DATE_EPOCH for copyright year

Tor Browser 9.0.6 -- March 11 2020
 * All Platforms
   * Update Firefox to 68.6.0esr
   * Bump NoScript to 11.0.15
     * Bug 33430: Disable downloadable fonts on Safest security level
 * Build System
   * Windows
     * Bug 33535: Patch openssl to use SOURCE_DATE_EPOCH for copyright year

Tor Browser 9.5a7 -- March 6 2020
 * All Platforms
   * Translations update
 * Windows + OS X + Linux
   * Update Tor Launcher to ********
     * Translations update
     * Bug 33514: non-en-US Tor Browser 9.5a6 won't start up
   * Bug 32645: Update URL bar onion indicators

Tor Browser 9.5a6 -- February 27 2020
 * All Platforms
   * Translations update
 * Windows + OS X + Linux
   * Update Tor Launcher to ********
     * Translations update
     * Bug 19757: Support on-disk storage of v3 client auth keys
   * Bug 19757: Support on-disk storage of v3 client auth keys
   * Bug 32493: Disable MOZ_SERVICES_HEALTHREPORT
   * Bug 32658: Create a new MAR signing key
 * Build System
   * All Platforms
     * Bug 33380: Add *.json to sha256sums-unsigned-build.txt

Tor Browser 9.5a5 -- February 12 2020
 * All Platforms
   * Update Firefox to 68.5.0esr
   * Bump NoScript to 11.0.13
   * Translations update
   * Bug 30237: Control port module improvements for v3 client authentication
   * Bug 32891: Add new default bridges
   * Bug 31395: Remove inline script in aboutTor.xhtml
   * Bug 27268: Preferences clean-up in Torbutton code
   * Bug 32470: Backport fix for bug 1590538
   * Bug 32414: Make Services.search.addEngine obey FPI
   * Bug 32948: Make referer behavior consistent regardless of private browing mode status
   * Bug 22919: Improve the random number generator for the boundaries in multipart/form-data
 * Windows + OS X + Linux
   * Update Tor to *******-alpha
   * Update Tor Launcher to ********
     * Translations update
     * Bug 30237: Add v3 onion services client authentication prompt
   * Bug 32870: Update version of pion-webrtc
   * Bug 32767: Remove Disconnect search
   * Bug 30237: Add v3 onion services client authentication prompt
 * Linux
   * Bug 27903: Tor Browser 8 does not respect gtk3 settings
 * Android
   * Bug 30767: Custom obfs4 bridge does not work on Tor Browser for Android
 * Build System
   * Linux
     * Bug 32976: Build and bundle geckodriver
   * OS X
     * Bug 33200: Fix permissions on bookmarks.html

Tor Browser 9.0.5 -- February 11 2020
 * All Platforms
   * Update Firefox to 68.5.0esr
   * Bump NoScript to 11.0.13
   * Bug 32053: Fix LLVM reproducibility issues
   * Bug 32255: Missing ORIGIN header breaks CORS
   * Bug 32891: Add new default bridges
 * Windows + OS X + Linux
   * Bump Tor to *******
 * Windows
   * Bug 32132: Re-enable jemalloc for Windows users
 * Build System
   * All Platforms
     * Bug 32739: Bump clang to 8.0.1
   * OS X
     * Bug 33200: Fix permissions on bookmarks.html

Tor Browser 9.5a4 -- January 10 2020
 * All Platforms
   * Update Firefox to 68.4.1esr
   * Bump NoScript to 11.0.11
   * Translations update
   * Bug 31134: Govern graphite again by security settings
   * Bug 31855: Remove End of Year Fundraising Campaign from about:tor
   * Bug 32053: Fix LLVM reproducibility issues
   * Bug 32547: Add new default bridge at UMN
   * Bug 32659: Remove IPv6 address of default bridge
 * Windows + OS X + Linux
   * Update Tor to *******
   * Update Tor Launcher to 0.2.21
     * Bug 32636: Clean up locales shipped with Tor Launcher
     * Translations update
   * Bug 32674: Point the about:tor "Get involved" link to the community portal
 * Build System
   * All Platforms
     * Update OpenPGP keyring
     * Bug 32739: Bump clang to 8.0.1
   * Linux
     * Bug 32676: Create a tarball with all Linux x86_64 language packs

Tor Browser 9.0.4 -- January 9 2020
 * All Platforms
   * Update Firefox to 68.4.1esr

Tor Browser 9.0.3 -- January 7 2020
 * All Platforms
   * Update Firefox to 68.4.0esr
   * Bump NoScript to 11.0.11
   * Translations update
   * Bug 32606: Set up default bridge at Georgetown University
   * Bug 32659: Remove IPv6 address of default bridge
   * Bug 32547: Add new default bridge at UMN
   * Bug 31855: Remove End of Year Fundraising Campaign from about:tor
 * Windows + OS X + Linux
   * Bump Tor to *******
   * Update Tor Launcher to ********
     * Bug 32636: Clean up locales shipped with Tor Launcher
     * Revert bug 30786, 30787, and 30788
 * Android
   * Bug 32405: Crash immediately after bootstrap on Android
 * Build System
   * All Platforms
     * Update OpenPGP keyring
   * Linux
     * Bug 32676: Create a tarball with all Linux x86_64 language packs

Tor Browser 9.0.2 -- December 3 2019
 * All Platforms
   * Update Firefox to 68.3.0esr
   * Bump NoScript to 11.0.9
     * Bug 32362: NoScript TRUSTED setting doesn't work
     * Bug 32429: Issues with about:blank and NoScript on .onion sites
   * Bump HTTPS Everywhere to 2019.11.7
   * Bug 27268: Preferences clean-up in Torbutton code
   * Translations update
 * Windows + OS X + Linux
   * Bug 32125: Fix circuit display for bridge without a fingerprint
   * Bug 32250: Backport enhanced letterboxing support (bug 1546832 and 1556017)
 * Windows
   * Bug 31989: Backport backout of old mingw-gcc patch
   * Bug 32616: Disable GetSecureOutputDirectoryPath() functionality
 * Android
   * Bug 32365: Localization is broken in Tor Browser 9 on Android
 * Build System
   * All Platforms
     * Bug 32413: Bump Go version to 1.12.13

Tor Browser 9.5a3 -- December 3 2019
 * All Platforms
   * Update Firefox to 68.3.0esr
   * Bump NoScript to 11.0.9
     * Bug 32362: NoScript TRUSTED setting doesn't work
     * Bug 32429: Issues with about:blank and NoScript on .onion sites
     * Bug 32549: NoScript makes requests to sync-messages.invalid
   * Update HTTPS Everywhere to 2019.11.7
   * Bug 32618: Backport fixes from Mozilla bugs 1467970 and 1590526
   * Bug 32606: Set up default bridge at Georgetown University
   * Bug 30787: Add lt locale
   * Bug 30788: Add ms locale
   * Bug 30786: Add th locale
   * Translations update
   * Bug 28746: Remove torbutton isolation and fp prefs sync
   * Bug 28745: Assume always running in Tor Browser
   * Bug 30888: move torbutton_util.js to modules/utils.js
   * Bug 30851: Move default preferences to 000-tor-browser.js
   * Bug 28745: Remove torbutton.js unused code
   * Bug 32255: Missing ORIGIN header breaks CORS
 * Windows + OS X + Linux
   * Update Tor to *******-rc
   * Update Tor Launcher to ********
     * Bug 30787: Add lt locale
     * Bug 30788: Add ms locale
     * Bug 30786: Add th locale
   * Bug 30237: Improve TBB UI of hidden service client authorization
 * Android
   * Bug 32365: Localization is broken in Tor Browser 9 on Android
   * Bug 32405: Crash immediately after bootstrap on Android
 * OS X
   * Bug 32505: Tighten our rules in our entitlements file for macOS
 * Windows
   * Bug 32616: Disable GetSecureOutputDirectoryPath() functionality

Tor Browser 9.0.2 -- December 3 2019
 * All Platforms
   * Update Firefox to 68.3.0esr
   * Bump NoScript to 11.0.9
     * Bug 32362: NoScript TRUSTED setting doesn't work
     * Bug 32429: Issues with about:blank and NoScript on .onion sites
     * Bug 32549: NoScript makes requests to sync-messages.invalid
   * Bump HTTPS Everywhere to 2019.11.7
   * Bug 27268: Preferences clean-up in Torbutton code
   * Translations update
 * Windows + OS X + Linux
   * Bug 32125: Fix circuit display for bridge without a fingerprint
   * Bug 32250: Backport enhanced letterboxing support (bug 1546832 and 1556017)
 * Windows
   * Bug 31989: Backport backout of old mingw-gcc patch
   * Bug 32616: Disable GetSecureOutputDirectoryPath() functionality
 * Android
   * Bug 32365: Localization is broken in Tor Browser 9 on Android
 * Build System
   * All Platforms
     * Bug 32413: Bump Go version to 1.12.13

Tor Browser 9.5a2 -- November 11 2019
 * All Platforms
   * Update NoScript to 11.0.7
     * Bug 21004: Don't block JavaScript on onion services on medium security
     * Bug 27307: NoScript marks HTTP onions as not secure
   * Bug 30783: Fundraising banner for EOY 2019 campain
   * Bug 32321: Don't ping Mozilla for Man-in-the-Middle-detection
   * Bug 32318: Backport Mozilla's fix for bug 1534339
   * Bug 31573: Catch SessionStore.jsm exception
   * Bug 27268: Preferences clean-up
 * Windows + OS X + Linux
   * Update Tor to *******-alpha
   * Update Tor Launcher to ********
     * Bug 32164: Trim each received log line from tor
     * Translations update
   * Bug 32250: Backport enhanced letterboxing support (bug 1546832 and 1556017)
   * Bug 31803: Replaced about:debugging logo with flat version
   * Bug 31764: Fix for error when navigating via 'Paste and go'
   * Bug 32169: Fix TB9 Wikipedia address bar search
   * Bug 32210: Hide the tor pane when using a system tor
   * Bug 31658: Use builtin --panel-disabled-color for security level text
   * Bug 32188: Fix localization on about:preferences#tor
   * Bug 32184: Red dot is shown while downloading an update
   * Bug 27604: Fix broken Tor Browser after moving it to a different directory
   * Bug 32220: Improve the letterboxing experience
   * Bug 30683: Backport upstreamed fix from Mozilla (bug 1581537)
 * Android
   * Bug 32342: Crash when changing the browser locale
   * Bug 32303: Obfs4 is broken on Android Q
 * Build System
   * All Platforms
     * Bug 32413: Bump Go version to 1.12.13
   * Android
     * Bug 28803: Integrate building Pluggable Transports for Android

Tor Browser 9.0.1 -- November 4 2019
 * All Platforms
   * Update NoScript to 11.0.4
     * Bug 21004: Don't block JavaScript on onion services on medium security
     * Bug 27307: NoScript marks HTTP onions as not secure
   * Bug 30783: Fundraising banner for EOY 2019 campain
   * Bug 32321: Don't ping Mozilla for Man-in-the-Middle-detection
   * Bug 27268: Preferences clean-up
 * Windows + OS X + Linux
   * Update Tor Launcher to ********
     * Bug 32164: Trim each received log line from tor
     * Translations update
   * Bug 31803: Replaced about:debugging logo with flat version
   * Bug 31764: Fix for error when navigating via 'Paste and go'
   * Bug 32169: Fix TB9 Wikipedia address bar search
   * Bug 32210: Hide the tor pane when using a system tor
   * Bug 31658: Use builtin --panel-disabled-color for security level text
   * Bug 32188: Fix localization on about:preferences#tor
   * Bug 32184: Red dot is shown while downloading an update
 * Android
   * Bug 32342: Crash when changing the browser locale

Tor Browser 9.5a1 -- October 23 2019
 * All Platforms
   * Update Firefox to 68.2.0esr
   * Bug 31740: Remove some unnecessary RemoteSettings instances
   * Bug 30681: Set security.enterprise_roots.enabled to false
   * Bug 31144: Review network code changes for Firefox 68 ESR
   * Bug 21549: Enable WASM on standard security level
 * Windows + OS X + Linux
   * Update Tor Launcher to ********
     * Bug 32154: Custom bridge field only allows one line of input
     * Bug 32112: Fix bad & escaping in translations
     * Bug 31286: Update to tor settings related strings
     * Translations update
   * Bug 32125: Fix circuit display for bridge without a fingerprint
   * Bug 32076: Upgrade to goptlib v1.1.0
   * Bug 32061: Bump snowflake version to b4f4b29a03
   * Bug 32092: Fix Tor Browser Support link in preferences
   * Bug 32111: Fixed issue parsing user-provided bridge strings
   * Bug 31749: Fix security level panel spawning events
   * Bug 31920: Fix Security Level panel when its toolbar button moves to overflow
   * Bug 31748+31961: Fix 'Learn More' links in Security Level preferences and panel
   * Translations update
 * Windows
   * Bug 32132: Re-enable jemalloc for Windows users
   * Bug 31989: Backport backout of old mingw-gcc patch
 * Android
   * Bug 32097: Fix conflicts in mobile onboarding while rebasing to 68.2.0esr
   * Bug 26529: Notify user about possible proxy-bypass before opening external app
 * Build System
   * Android
     * Bug 30461: Clean up tor-android-service project

Tor Browser 9.0 -- October 22 2019
 * All Platforms
   * Update Firefox to 68.2.0esr
   * Bug 31740: Remove some unnecessary RemoteSettings instances
   * Bug 13543: Spoof smooth and powerEfficient for Media Capabilities
   * Bug 28196: about:preferences is not properly translated anymore
   * Bug 19417: Disable asmjs on safer and safest security levels
   * Bug 30463: Explicitly disable MOZ_TELEMETRY_REPORTING
   * Bug 31935: Disable profile downgrade protection
   * Bug 16285: Disable DRM/EME on Android and drop Adobe CDM
   * Bug 31602: Remove Pocket indicators in UI and disable it
   * Bug 31914: Fix eslint linter error
   * Bug 30429: Rebase patches for Firefox 68 ESR
   * Bug 31144: Review network code changes for Firefox 68 ESR
   * Bug 10760: Integrate Torbutton into Tor Browser directly
   * Bug 25856: Remove XUL overlays from Torbutton
   * Bug 31322: Fix about:tor assertion failure debug builds
   * Bug 29430: Add support for meek_lite bridges to bridgeParser
   * Bug 28561: Migrate "About Tor Browser" dialog to tor-browser
   * Bug 30683: Prevent detection of locale via some *.properties
   * Bug 31298: Backport patch for #24056
   * Bug 9336: Odd wyswig schemes without isolation for browserspy.dk
   * Bug 27601: Browser notifications are not working anymore
   * Bug 30845: Make sure internal extensions are enabled
   * Bug 28896: Enable extensions in private browsing by default
   * Bug 31563: Reload search extensions if extensions.enabledScopes has changed
   * Bug 31396: Fix communication with NoScript for security settings
   * Bug 31142: Fix crash of tab and messing with about:newtab
   * Bug 29049: Backport JS Poison Patch
   * Bug 25214: Canvas data extraction on local pdf file should be allowed
   * Bug 30657: Locale is leaked via title of link tag on non-html page
   * Bug 31015: Disabling SVG hides UI icons in extensions
   * Bug 30681: Set security.enterprise_roots.enabled to false
   * Bug 30538: Unable to comment on The Independent Newspaper
   * Bug 31209: View PDF in Tor Browser is fuzzy
   * Translations update
 * Windows + OS X + Linux
   * Update Tor to *******
   * Update OpenSSL to 1.1.1d
     * Bug 31844: OpenSSL 1.1.1d fails to compile for some platforms/architectures
   * Update Tor Launcher to ********
     * Bug 28044: Integrate Tor Launcher into tor-browser
     * Bug 32154: Custom bridge field only allows one line of input
     * Bug 31286: New strings for about:preferences#tor
     * Bug 31303: Do not launch tor in browser toolbox
     * Bug 32112: Fix bad & escaping in translations
     * Bug 31491: Clean up the old meek http helper browser profiles
     * Bug 29197: Remove use of overlays
     * Bug 31300: Modify Tor Launcher so it is compatible with ESR68
     * Bug 31487: Modify moat client code so it is compatible with ESR68
     * Bug 31488: Moat: support a comma-separated list of transports
     * Bug 30468: Add mk locale
     * Bug 30469: Add ro locale
     * Bug 30319: Remove FTE bits
     * Translations update
   * Bug 32092: Fix Tor Browser Support link in preferences
   * Bug 32111: Fixed issue parsing user-provided bridge strings
   * Bug 31749: Fix security level panel spawning events
   * Bug 31920: Fix Security Level panel when its toolbar button moves to overflow
   * Bug 31748+31961: Fix 'Learn More' links in Security Level preferences and panel
   * Bug 28044: Integrate Tor Launcher into tor-browser
   * Bug 31059: Enable Letterboxing
   * Bug 30468: Add mk locale
   * Bug 30469: Add ro locale
   * Bug 29430: Use obfs4proxy's meek_lite with utls instead of meek
   * Bug 31251: Security Level button UI polish
   * Bug 31344: Register SecurityLevelPreference's 'unload' callback
   * Bug 31286: Provide network settings on about:preferences#tor
   * Bug 31886: Fix ko bundle bustage
   * Bug 31768: Update onboarding for Tor Browser 9
   * Bug 27511: Add new identity button to toolbar
   * Bug 31778: Support dark-theme for the Circuit Display UI
   * Bug 31910: Replace meek_lite with meek in circuit display
   * Bug 30504: Deal with New Identity related browser console errors
   * Bug 31929: Don't escape DTD entity in ar
   * Bug 31747: Some onboarding UI is always shown in English
   * Bug 32041: Replace = with real hamburguer icon ≡
   * Bug 30304: Browser locale can be obtained via DTD strings
   * Bug 31065: Set network.proxy.allow_hijacking_localhost to true
   * Bug 24653: Merge securityLevel.properties into torbutton.dtd
   * Bug 31164: Set up default bridge at Karlstad University
   * Bug 15563: Disable ServiceWorkers on all platforms
   * Bug 31598: Disable warning on window resize if letterboxing is enabled
   * Bug 31562: Fix circuit display for error pages
   * Bug 31575: Firefox is phoning home during start-up
   * Bug 31491: Clean up the old meek http helper browser profiles
   * Bug 26345: Hide tracking protection UI
   * Bug 31601: Disable recommended extensions again
   * Bug 30662: Don't show Firefox Home when opening new tabs
   * Bug 31457: Disable per-installation profiles
   * Bug 28822: Re-implement desktop onboarding for ESR 68
 * Windows
   * Bug 31942: Re-enable signature check for language packs
   * Bug 29013: Enable stack protection for Firefox on Windows
   * Bug 30800: ftp:// on Windows can be used to leak the system time zone
   * Bug 31547: Back out patch for Mozilla's bug 1574980
   * Bug 31141: Fix typo in font.system.whitelist
   * Bug 30319: Remove FTE bits
 * OS X
   * Bug 30126: Make Tor Browser compatible with macOS 10.15
   * Bug 31607: App menu items stop working on macOS
   * Bug 31955: On macOS avoid throwing inside nonBrowserWindowStartup()
   * Bug 29818: Adapt #13379 patch for 68esr
   * Bug 31464: Meek and moat are broken on macOS 10.9 with Go 1.12
 * Linux
   * Bug 31942: Re-enable signature check for language packs
   * Bug 31646: Update abicheck to require newer libstdc++.so.6
   * Bug 31968: Don't fail if /proc/cpuinfo is not readable
   * Bug 24755: Stop using a heredoc in start-tor-browser
   * Bug 31550: Put curly quotes inside single quotes
   * Bug 31394: Replace "-1" with "−1" in start-tor-browser.desktop
   * Bug 30319: Remove FTE bits
 * Android
   * Update Tor to *******
   * Bug 31010: Rebase mobile patches for Fennec 68
   * Bug 31010: Don't use addTrustedTab() on mobile
   * Bug 30607: Support Tor Browser running on Android Q
   * Bug 31192: Support x86_64 target on Android
   * Bug 30380: Cancel dormant by startup
   * Bug 30943: Show version number on mobile
   * Bug 31720: Enable website suggestions in address bar
   * Bug 31822: Security slider is not really visible on Android anymore
   * Bug 24920: Only create Private tabs in permanent Private Browsing Mode
   * Bug 31730: Revert aarch64-workaround against JIT-related crashes
   * Bug 32097: Fix conflicts in mobile onboarding while rebasing to 68.2.0esr
 * Build System
   * All Platforms
     * Bug 30585: Provide standalone clang 8 project across all platforms
     * Bug 30376: Use Rust 1.34 for Tor Browser 9
     * Bug 30490: Add cbindgen project for building Firefox 68 ESR/Fennec 68
     * Bug 30701: Add nodejs project for building Firefox 68 ESR/Fennec 68
       * Bug 31621: Fix node bug that makes large writes to stdout fail
     * Bug 30734: Add nasm project for building Firefox 68 ESR/Fennec 68
     * Bug 31293: Make sure the lo interface inside the containers is up
     * Bug 27493: Clean up mozconfig options
     * Bug 31308: Sync mozconfig files used in tor-browser over to tor-browser-build for esr68
   * Windows
     * Bug 29307: Use Stretch for cross-compiling for Windows
     * Bug 29731: Remove faketime for Windows builds
     * Bug 30322: Windows toolchain update for Firefox 68 ESR
       * Bug 28716: Create mingw-w64-clang toolchain
       * Bug 28238: Adapt firefox and fxc2 projects for Windows builds
       * Bug 28716: Optionally omit timestamp in PE header
       * Bug 31567: NS_tsnprintf() does not handle %s correctly on Windows
       * Bug 31458: Revert patch for #27503 and bump mingw-w64 revision used
     * Bug 9898: Provide clean fix for strcmpi issue in NSPR
     * Bug 29013: Enable stack protection support for Firefox on Windows
     * Bug 30384: Use 64bit containers to build 32bit Windows Tor Browser
     * Bug 31538: Windows bundles based on ESR 68 are not built reproducibly
     * Bug 31584: Clean up mingw-w64 project
     * Bug 31596: Bump mingw-w64 version to pick up fix for #31567
     * Bug 29187: Bump NSIS version to 3.04
     * Bug 31732: Windows nightly builds are busted due to mingw-w64 commit bump
     * Bug 29319: Remove FTE support for Windows
   * OS X
     * Bug 30323: MacOS toolchain update for Firefox 68 ESR
     * Bug 31467: Switch to clang for cctools project
     * Bug 31465: Adapt tor-browser-build projects for macOS notarization
   * Linux
     * Bug 31448: gold and lld break linking 32bit Linux bundles
     * Bug 31618: Linux32 builds of Tor Browser 9.0a6 are not matching
     * Bug 31450: Still use GCC for our ASan builds
     * Bug 30321: Linux toolchain update for Firefox ESR 68
       * Bug 30736: Install yasm from wheezy-backports
       * Bug 31447: Don't install Python just for Mach
     * Bug 30448: Strip Browser/gtk2/libmozgtk.so
   * Android
     * Bug 30324: Android toolchain update for Fennec 68
       * Bug 31173: Update android-toolchain project to match Firefox
       * Bug 31389: Update Android Firefox to build with Clang
       * Bug 31388: Update Rust project for Android
       * Bug 30665: Get Firefox 68 ESR working with latest android toolchain
       * Bug 30460: Update TOPL project to use Firefox 68 toolchain
       * Bug 30461: Update tor-android-service project to use Firefox 68 toolchain
     * Bug 28753: Use Gradle with --offline when building the browser part
     * Bug 31564: Make Android bundles based on ESR 68 reproducible
     * Bug 31981: Remove require-api.patch
     * Bug 31979: TOPL: Sort dependency list
     * Bug 30665: Remove unnecessary build patches for Firefox

Tor Browser 9.0a8 -- October 14 2019
 * All Platforms
   * Bug 13543: Spoof smooth and powerEfficient for Media Capabilities
   * Bug 28196: about:preferences is not properly translated anymore
   * Bug 19417: Disable asmjs on safer and safest security levels
   * Bug 30463: Explicitly disable MOZ_TELEMETRY_REPORTING
   * Bug 31935: Disable profile downgrade protection
   * Bug 31811: Backport fix for bug 1554805
   * Bug 16285: Disable DRM/EME on Android and drop Adobe CDM
   * Bug 31602: Remove Pocket indicators in UI and disable it
   * Bug 31914: Fix eslint linter error
   * Translations update
 * Windows + OS X + Linux
   * Update Tor to 0.4.2.2-alpha
   * Update Tor Launcher to 0.2.19.5
     * Bug 31286: New strings for about:preferences#tor
     * Translations update
   * Bug 31286: Provide network settings on about:preferences#tor
   * Bug 31886: Fix ko bundle bustage
   * Bug 31768: Update onboarding for Tor Browser 9
   * Bug 27511: Add new identity button to toolbar
   * Bug 31778: Support dark-theme for the Circuit Display UI
   * Bug 31910: Replace meek_lite with meek in circuit display
   * Bug 30504: Deal with New Identity related browser console errors
   * Bug 31929: Don't escape DTD entity in ar
   * Bug 31747: Some onboarding UI is always shown in English
   * Bug 32041: Replace = with real hamburguer icon ≡
 * Windows
   * Bug 31942: Re-enable signature check for language packs
   * Bug 29013: Enable stack protection for Firefox on Windows
 * OS X
   * Bug 31607: App menu items stop working on macOS
   * Bug 31955: On macOS avoid throwing inside nonBrowserWindowStartup()
 * Linux
   * Bug 31942: Re-enable signature check for language packs
   * Bug 31968: Don't fail if /proc/cpuinfo is not readable
   * Bug 24755: Stop using a heredoc in start-tor-browser
   * Bug 31550: Put curly quotes inside single quotes
 * Android
   * Bug 31822: Security slider is not really visible on Android anymore
 * Build System
   * All Platforms
     * Bug 31293: Make sure the lo interface inside the containers is up
   * Windows
     * Bug 29013: Enable stack protection support for Firefox on Windows
   * Android
     * Bug 31564: Make Android bundles based on ESR 68 reproducible
     * Bug 31981: Remove require-api.patch
     * Bug 31979: TOPL: Sort dependency list
     * Bug 30665: Remove unnecessary build patches for Firefox

Tor Browser 9.0a7 -- October 1 2019
 * All platforms
   * Bug 30304: Browser locale can be obtained via DTD strings
   * Bug 31065: Set network.proxy.allow_hijacking_localhost to true
   * Bug 24653: Merge securityLevel.properties into torbutton.dtd
   * Bug 31725: Pick up mk in Torbutton properly
   * Bug 31164: Set up default bridge at Karlstad University
   * Bug 15563: Disable ServiceWorkers on all platforms
   * Translations update
 * Windows + OS X + Linux
   * Update Tor to *******-alpha
   * Update OpenSSL to 1.1.1d
     * Bug 31844: OpenSSL 1.1.1d fails to compile for some platforms/architectures
   * Update Tor Launcher to ********
     * Bug 31303: Do not launch tor in browser toolbox
     * Bug 31491: Clean up the old meek http helper browser profiles
     * Translations update
   * Bug 31598: Disable warning on window resize if letterboxing is enabled
   * Bug 31562: Fix circuit display for error pages
   * Bug 31575: Firefox is phoning home during start-up
   * Bug 31491: Clean up the old meek http helper browser profiles
   * Bug 26345: Hide tracking protection UI
   * Bug 31601: Disable recommended extensions again
   * Bug 30662: Don't show Firefox Home when opening new tabs
   * Bug 31457: disable per-installation profiles
   * Bug 28822: Re-implement desktop onboarding for ESR 68
   * Bug 25483: Provide Snowflake based on Pion for Windows, macOS, and Linux
 * Windows
   * Bug 30800: ftp:// on Windows can be used to leak the system time zone
 * OS X
   * Bug 30126: Make Tor Browser on macOS compatible with Apple's notarization
   * Bug 31702: Backport Mozilla's bug 1578075
 * Linux
   * Bug 31646: Update abicheck to require newer libstdc++.so.6
   * Bug 31380: Snowflake does not start on older Linux systems
 * Android
   * Update Tor to *******
   * Bug 31192: Support x86_64 target on Android
   * Bug 30380: Cancel dormant by startup
   * Bug 30943: Show version number on mobile
   * Bug 31720: Enable website suggestions in address bar
 * Build System
   * All platforms
     * Bug 31621: Fix node bug that makes large writes to stdout fail
     * Bug 27493: Clean up mozconfig options
     * Bug 31308: Sync mozconfig files used in tor-browser over to tor-browser-build for esr68
   * Windows
     * Bug 30384: Use 64bit containers to build 32bit Windows Tor Browser
     * Bug 31538: Windows bundles based on ESR 68 are not built reproducibly
     * Bug 31584: Clean up mingw-w64 project
     * Bug 31596: Bump mingw-w64 version to pick up fix for #31567
     * Bug 29187: Bump NSIS version to 3.04
     * Bug 31732: Windows nightly builds are busted due to mingw-w64 commit bump
   * Linux
     * Bug 31448: gold and lld break linking 32bit Linux bundles
     * Bug 31618: linux32 builds of Tor Browser 9.0a6 are not matching
     * Bug 31450: Still use GCC for our ASan builds

Tor Browser 8.5.6 -- September 9 2019
 * Android
   * Update Torbutton to 2.1.14
     * Bug 31616: Fix JIT related crashes on aarch64

Tor Browser 9.0a6 -- September 4 2019
 * All platforms
   * Update Firefox to 68.1.0esr
   * Update NoScript to 11.0.3
     * Bug 26847: NoScript pops up a full-site window for XSS warning
     * Bug 31287: NoScript leaks browser locale
   * Bug 30429: Rebase patches for Firefox 68 ESR
   * Bug 10760: Integrate Torbutton into Tor Browser directly
   * Bug 25856: Remove XUL overlays from Torbutton
   * Bug 31322: Fix about:tor assertion failure debug builds
   * Bug 31520: Remove monthly giving banner from Tor Browser
   * Bug 29430: Add support for meek_lite bridges to bridgeParser
   * Bug 28561: Migrate "About Tor Browser" dialog to tor-browser
   * Bug 30683: Prevent detection of locale via some *.properties
   * Bug 31298: Backport patch for #24056
   * Bug 9336: Odd wyswig schemes without isolation for browserspy.dk
   * Bug 27601: Browser notifications are not working anymore
   * Bug 30845: Make sure internal extensions are enabled
   * Bug 28896: Enable extensions in private browsing by default
   * Bug 31563: Reload search extensions if extensions.enabledScopes has changed
   * Bug 31396: Fix communication with NoScript for security settings
   * Bug 31142: Fix crash of tab and messing with about:newtab
   * Bug 29049: Backport JS Poison Patch
   * Bug 25214: Canvas data extraction on local pdf file should be allowed
   * Bug 30657: Locale is leaked via title of link tag on non-html page
   * Bug 31015: Disabling SVG hides UI icons in extensions
   * Bug 30538: Unable to comment on The Independent Newspaper
   * Bug 31357: Retire Tom's default obfs4 bridge
 * Windows + OS X + Linux
   * Update Tor to *******
   * Update Tor Launcher to ********
     * Bug 29197: Remove use of overlays
     * Bug 31300: Modify Tor Launcher so it is compatible with ESR68
     * Bug 31487: Modify moat client code so it is compatible with ESR68
     * Bug 31488: Moat: support a comma-separated list of transports
     * Translations update
   * Bug 29430: Use obfs4proxy's meek_lite with utls instead of meek
   * Bug 31251: Security Level button UI polish
   * Bug 31344: Register SecurityLevelPreference's 'unload' callback
   * Bug 12774: Selecting meek in the browser UI is broken
   * Build System:
     * Bug 31465: Bump Go to 1.12.9
 * Windows
   * Bug 31547: Back out patch for Mozilla's bug 1574980
   * Bug 31141: Fix typo in font.system.whitelist
   * Backport fix for bug 1572844 to fix broken build
 * OS X
   * Bug 29818: Adapt #13379 patch for 68esr
   * Bug 31464: meek and moat are broken on macOS 10.9 with Go 1.12
   * Bug 31403: Bump snowflake commit to cd650fa009
 * Linux
   * Bug 31403: Bump snowflake commit to cd650fa009
 * Android
   * Bug 31010: Rebase mobile patches for Fennec 68
   * Bug 31010: Don't use addTrustedTab() on mobile
   * Bug 30607: Support Tor Browser running on Android Q
 * Build System:
   * All Platforms:
     * Bug 30585: Provide standalone clang 8 project across all platforms
     * Bug 30376: Use Rust 1.34 for Tor Browser 9
     * Bug 30490: Add cbindgen project for building Firefox 68 ESR/Fennec 68
     * Bug 30701: Add nodejs project for building Firefox 68 ESR/Fennec 68
     * Bug 30734: Add nasm project for building Firefox 68 ESR/Fennec 68
   * Windows
     * Bug 30322: Windows toolchain update for Firefox 68 ESR
       * Bug 28716: Create mingw-w64-clang toolchain
       * Bug 28238: Adapt firefox and fxc2 projects for Windows builds
       * Bug 28716: Optionally omit timestamp in PE header
       * Bug 31567: NS_tsnprintf() does not handle %s correctly on Windows
       * Bug 31458: Revert patch for #27503 and bump mingw-w64 revision used
     * Bug 9898: Provide clean fix for strcmpi issue in NSPR
   * OS X
     * Bug 30323: MacOS toolchain update for Firefox 68 ESR
     * Bug 31467: Switch to clang for cctools project
     * Bug 31465: Adapt tor-browser-build projects for macOS notarization
   * Linux
     * Bug 30321: Linux toolchain update for Firefox ESR 68
       * Bug 30736: Install yasm from wheezy-backports
       * Bug 31447: Don't install Python just for Mach
     * Bug 31394: Replace "-1" with "−1" in start-tor-browser.desktop.
   * Android
     * Bug 30324: Android toolchain update for Fennec 68
       * Bug 31173: Update android-toolchain project to match Firefox
       * Bug 31389: Update Android Firefox to build with Clang
       * Bug 31388: Update Rust project for Android
       * Bug 30665: Get Firefox 68 ESR working with latest android toolchain
       * Bug 30460: Update TOPL project to use Firefox 68 toolchain
       * Bug 30461: Update tor-android-service project to use Firefox 68 toolchain
     * Bug 28753: Use Gradle with --offline when building the browser part

Tor Browser 8.5.5 -- September 3 2019
 * All platforms
   * Update Firefox to 60.9.0esr
   * Update Torbutton to 2.1.13
     * Bug 31520: Remove monthly giving banner from Tor Browser
     * Bug 31140: Do not enable IonMonkey on AARCH64
     * Translations update
   * Update NoScript to 11.0.3
     * Bug 26847: NoScript pops up a full-site window for XSS warning
     * Bug 31287: NoScript leaks browser locale
   * Bug 31357: Retire Tom's default obfs4 bridge
 * Windows + OS X + Linux
   * Update Tor to *******
 * Windows
   * Bug 31547: Back out patch for Mozilla's bug 1574980
   * Bug 27503: Provide full support for accessibility tools
   * Bug 30575: Don't allow enterprise policies in Tor Browser
   * Bug 31141: Fix typo in font.system.whitelist
 * Android
   * Bug 28119: Tor Browser for aarch64
 * Build System
   * All platforms
     * Bug 31465: Bump Go to 1.12.9

Tor Browser 9.0a5 -- July 31 2019
 * Android
   * Bug 31260: Backport bug 1477259 for aarch64 support on Google Play

Tor Browser 9.0a4 -- July 9 2019
 * All platforms
   * Update Firefox to 60.8.0esr
   * Update Torbutton to 2.2.1
     * Bug 30577: Add Fundraising Banner
     * Bug 31041: Stop syncing network.cookie.lifetimePolicy
     * Bug 30468: Add mk locale
     * Translations update
   * Update Tor Launcher to ********
     * Bug 30468: Add mk locale
     * Translations update
   * Update HTTPS Everywhere to 2019.6.27
   * Bug 31055+31058: Remove four default bridges
   * Bug 30849: Backport fixes for Mozilla's bug 1552627 and 1549833
 * Windows + OS X + Linux
   * Update Tor to 0.4.1.3-alpha
   * Bug 30468: Add mk locale
   * Bug 31059: Enable Letterboxing
 * Windows
   * Bug 27503: Provide full support for accessibility tools
   * Bug 30575: Don't allow enterprise policies in Tor Browser
 * OS X
   * Bug 30631: Blurry Tor Browser icon on macOS app switcher
 * Android
   * Bug 28119: Tor Browser for aarch64

Tor Browser 8.5.4 -- July 9 2019
 * All platforms
   * Update Firefox to 60.8.0esr
   * Update Torbutton to 2.1.12
     * Bug 30577: Add Fundraising Banner
     * Bug 31041: Stop syncing network.cookie.lifetimePolicy
     * Translations update
   * Update HTTPS Everywhere to 2019.6.27
   * Bug 31055+31058: Remove four default bridges
   * Bug 30712: Backport fix for Mozilla's bug 1552993
   * Bug 30849: Backport fixes for Mozilla's bug 1552627 and 1549833
 * Windows + OS X + Linux
   * Update Tor to *******
   * Update OpenSSL to 1.0.2s
   * Bug 29045: Ensure that tor does not start up in dormant mode
 * OS X
   * Bug 30631: Blurry Tor Browser icon on macOS app switcher

Tor Browser 9.0a3 -- June 24 2019
 * All platforms
   * Pick up fixes for Mozilla's bug 1544386 and 1560192
   * Update NoScript to 10.6.3
     * Bug 29904: NoScript blocks MP4 on higher security levels
     * Bug 30624+29043+29647: Prevent XSS protection from freezing the browser

Tor Browser 8.5.3 -- June 21 2019
 * All platforms
   * Pick up fix for Mozilla's bug 1560192

Tor Browser 8.5.2 -- June 19 2019
 * All platforms
   * Pick up fix for Mozilla's bug 1544386
   * Update NoScript to 10.6.3
     * Bug 29904: NoScript blocks MP4 on higher security levels
     * Bug 30624+29043+29647: Prevent XSS protection from freezing the browser

Tor Browser 9.0a2 -- June 11 2019
 * All platforms
   * Update Torbutton to 2.2
     * Bug 30565: Sync nocertdb with privatebrowsing.autostart at startup
     * Bug 30469: Add ro translation
     * Translations update
   * Update NoScript to 10.6.2
     * Bug 29969: Remove workaround for Mozilla's bug 1532530
   * Update HTTPS Everywhere to 2019.5.13
   * Bug 30541: Disable WebGL readPixel() for web content
   * Bug 30712: Backport fix for Mozilla's bug 1552993
   * Bug 30469: Add locale ro
 * Windows + OS X + Linux
   * Update Tor to *******-alpha
   * Update OpenSSL to 1.1.1c
   * Update Tor Launcher to ********
     * Bug 30469: Add locale ro
     * Translations update
   * Bug 30639: Revert IPv6 support test
   * Bug 30560: Better match actual toolbar in onboarding toolbar graphic
   * Bug 30571: Correct more information URL for security settings
 * Linux
   * Bug 30451: Compile go-webrtc with a non executable stack
 * Android
   * Bug 24920: Only create Private tabs in permanent Private Browsing Mode
   * Bug 30635: Sync mobile default bridges list with desktop one
 * Build System
   * All platforms
     * Bug 30480: Check that signed tag contains expected tag name
     * Bug 30536: Update Go to 1.12.5
   * OS X
     * Bug 30491: Move our macOS builds to Debian Stretch
   * Linux
     * Bug 25930: Update GCC to 8.3.0 for our Linux builds

Tor Browser 8.5.1 -- June 4 2019
 * All platforms
   * Update Torbutton to 2.1.10
     * Bug 30565: Sync nocertdb with privatebrowsing.autostart at startup
     * Bug 30464: Add WebGL to safer descriptions
     * Translations update
   * Update NoScript to 10.6.2
     * Bug 29969: Remove workaround for Mozilla's bug 1532530
   * Update HTTPS Everywhere to 2019.5.13
   * Bug 30541: Disable WebGL readPixel() for web content
 * Windows + OS X + Linux
   * Bug 30560: Better match actual toolbar in onboarding toolbar graphic
   * Bug 30571: Correct more information URL for security settings
 * Android
   * Bug 30635: Sync mobile default bridges list with desktop one
 * Build System
   * All platforms
     * Bug 30480: Check that signed tag contains expected tag name

Tor Browser 9.0a1 -- May 21 2019
 * All platforms
   * Update Firefox to 60.7.0esr
   * Update Torbutton to 2.1.9
     * Bug 30069: Use slider and about:tor localizations
     * Bug 30115+27449+25145: Map browser + domain -> credentials to fix UI issues
     * Bug 30171: Don't sync cookie.cookieBehavior and firstparty.isolate
     * Bug 30425: Revert armagadd-on-2.0 changes
     * Bug 30497: Add Donate link to about:tor
     * Bug 30464: Add WebGL to safer descriptions
     * Translations update
   * Update HTTPS Everywhere to 2019.5.6.1
   * Bug 24622: Proper first-party isolation of s3.amazonaws.com
   * Bug 30425: Revert armagadd-on-2.0 changes
 * Windows + OS X + Linux
   * Update Tor Launcher to 0.2.19
     * Bug 28044: Integrate Tor Launcher into tor-browser
     * Bug 29627: Moat: add support for obfsproxy's meek_lite
     * Bug 30319: Remove FTE bits
     * Translations update
   * Bug 28044: Integrate Tor Launcher into tor-browser
   * Bug 30372: Backport letterboxing (bug 1538130)
   * Bug 28369: Stop shipping pingsender executable
   * Bug 30457: Remove defunct default bridges
   * Bug 29045: Ensure that tor does not start up in dormant mode
   * Bug 29641: Try to connect over IPv6 if needed
 * Windows
   * Bug 30319: Drop FTE releated bits
   * Bug 29319: Remove FTE support for Windows
 * OS X
   * Bug 30241: Bump snowflake version to d11e55aabe
 * Linux
   * Bug 30319: Drop FTE releated bits
   * Bug 30241: Bump snowflake version to d11e55aabe
 * Android
   * Bug 29982: Force single-pane UI on Tor Preferences
   * Bug 30086: Prevent Sync-related crashes on Android
   * Bug 30214: Kill background thread when Activity is null
   * Bug 30239: Render Fragments after crash
   * Bug 30136: Use 'Tor Browser' as brand name on mobile, too
   * Bug 30069: Use slider and about:tor localizations
   * Bug 30371: Stop hard-coding the content provider name in tor-android-service
   * Bug 30162: Tor Browser bootstrap process got stuck after interrupting it
   * Bug 30166: If specified, only use custom bridges for connecting
   * Bug 30518: Add SocksPort flags for consistency across platforms
   * Bug 30284: Fix broken start-up on KitKat devices
   * Bug 30489: Remove Unused Resources from tor-android-service
 * Build System
   * Windows
     * Bug 29307: Use Stretch for cross-compiling for Windows
     * Bug 29731: Remove faketime for Windows builds
   * Linux
     * Bug 30377: Remove selfrando from our build system
     * Bug 30448: Strip Browser/gtk2/libmozgtk.so
   * Android
     * Bug 29981: Add option to build without using containers
     * Bug 30169: Switch to our tor-android-service repo
     * Bug 30404: Remove Orbot Project
     * Bug 30280: Wrong SHA-256 sum for j2objc-annotations-1.1.jar

Tor Browser 8.5 -- May 21 2019
 * All platforms
   * Update Firefox to 60.7.0esr
   * Update Torbutton to 2.1.8
     * Bug 25013: Integrate Torbutton into tor-browser for Android
     * Bug 27111: Update about:tor desktop version to work on mobile
     * Bug 22538+22513: Fix new circuit button for error pages
     * Bug 25145: Update circuit display when back button is pressed
     * Bug 27749: Opening about:config shows circuit from previous website
     * Bug 30115+27449+25145: Map browser+domain to credentials to fix circuit display
     * Bug 25702: Update Tor Browser icon to follow design guidelines
     * Bug 21805: Add click-to-play button for WebGL
     * Bug 28836: Links on about:tor are not clickable
     * Bug 30171: Don't sync cookie.cookieBehavior and firstparty.isolate
     * Bug 29825: Intelligently add new Security Level button to taskbar
     * Bug 29903: No WebGL click-to-play on the standard security level
     * Bug 27290: Remove WebGL pref for min capability mode
     * Bug 25658: Replace security slider with security level UI
     * Bug 28628: Change onboarding Security panel to open new Security Level panel
     * Bug 29440: Update about:tor when Tor Browser is updated
     * Bug 27478: Improved Torbutton icons for dark theme
     * Bug 29239: Don't ship the Torbutton .xpi on mobile
     * Bug 27484: Improve navigation within onboarding (strings)
     * Bug 29768: Introduce new features to users (strings)
     * Bug 28093: Update donation banner style to make it fit in small screens
     * Bug 28543: about:tor has scroll bar between widths 900px and 1000px
     * Bug 28039: Enable dump() if log method is 0
     * Bug 27701: Don't show App Blocker dialog on Android
     * Bug 28187: Change tor circuit icon to torbutton.svg
     * Bug 29943: Use locales in AB-CD scheme to match Mozilla
     * Bug 26498: Add locale: es-AR
     * Bug 28082: Add locales cs, el, hu, ka
     * Bug 29973: Remove remaining stopOpenSecuritySettingsObserver() pieces
     * Bug 28075: Tone down missing SOCKS credential warning
     * Bug 30425: Revert armagadd-on-2.0 changes
     * Bug 30497: Add Donate link to about:tor
     * Bug 30069: Use slider and about:tor localizations on mobile
     * Bug 21263: Remove outdated information from the README
     * Bug 28747: Remove NoScript (XPCOM) related unused code
     * Translations update
     * Code clean-up
   * Update HTTPS Everywhere to 2019.5.6.1
   * Bug 27290: Remove WebGL pref for min capability mode
   * Bug 29120: Enable media cache in memory
   * Bug 24622: Proper first-party isolation of s3.amazonaws.com
   * Bug 29082: Backport patches for bug 1469916
   * Bug 28711: Backport patches for bug 1474659
   * Bug 27828: "Check for Tor Browser update" doesn't seem to do anything
   * Bug 29028: Auto-decline most canvas warning prompts again
   * Bug 27919: Backport SSL status API
   * Bug 27597: Fix our debug builds
   * Bug 28082: Add locales cs, el, hu, ka
   * Bug 26498: Add locale: es-AR
   * Bug 29916: Make sure enterprise policies are disabled
   * Bug 29349: Remove network.http.spdy.* overrides from meek helper user.js
   * Bug 29327: TypeError: hostName is null on about:tor page
   * Bug 30425: Revert armagadd-on-2.0 changes
 * Windows + OS X + Linux
   * Update OpenSSL to 1.0.2r
   * Update Tor Launcher to ********
     * Bug 27994+25151: Use the new Tor Browser logo
     * Bug 29328: Account for Tor 0.4.0.x's revised bootstrap status reporting
     * Bug 22402: Improve "For assistance" link
     * Bug 27994: Use the new Tor Browser logo
     * Bug 25405: Cannot use Moat if a meek bridge is configured
     * Bug 27392: Update Moat URLs
     * Bug 28082: Add locales cs, el, hu, ka
     * Bug 26498: Add locale es-AR
     * Bug 28039: Enable dump() if log method is 0
     * Translations update
   * Bug 25702: Activity 1.1 Update Tor Browser icon to follow design guidelines
   * Bug 28111: Use Tor Browser icon in identity box
   * Bug 22343: Make 'Save Page As' obey first-party isolation
   * Bug 29768: Introduce new features to users
   * Bug 27484: Improve navigation within onboarding
   * Bug 25658+29554: Replace security slider with security level UI
   * Bug 25405: Cannot use Moat if a meek bridge is configured
   * Bug 28885: Notify users that update is downloading
   * Bug 29180: MAR download stalls when about dialog is opened
   * Bug 27485: Users are not taught how to open security-slider dialog
   * Bug 27486: Avoid about:blank tabs when opening onboarding pages
   * Bug 29440: Update about:tor when Tor Browser is updated
   * Bug 23359: WebExtensions icons are not shown on first start
   * Bug 28628: Change onboarding Security panel to open new Security Level panel
   * Bug 27905: Fix many occurrences of "Firefox" in about:preferences
   * Bug 28369: Stop shipping pingsender executable
   * Bug 30457: Remove defunct default bridges
 * Windows
   * Bug 27503: Improve screen reader accessibility
   * Bug 27865: Tor Browser 8.5a2 is crashing on Windows
   * Bug 22654: Firefox icon is shown for Tor Browser on Windows 10 start menu
   * Bug 28874: Bump mingw-w64 commit to fix WebGL crash
   * Bug 12885: Windows Jump Lists fail for Tor Browser
   * Bug 28618: Set MOZILLA_OFFICIAL for Windows build
   * Bug 21704: Abort install if CPU is missing SSE2 support
 * OS X
   * Bug 27623: Use MOZILLA_OFFICIAL for our builds
 * Linux
   * Bug 28022: Use `/usr/bin/env bash` for bash invocation
   * Bug 27623: Use MOZILLA_OFFICIAL for our builds
 * Android
   * Bug 5709: Ship Tor Browser for Android
 * Build System
   * All platforms
     * Bug 25623: Disable network during build
     * Bug 25876: Generate source tarballs during build
     * Bug 28685: Set Build ID based on Tor Browser version
     * Bug 29194: Set DEBIAN_FRONTEND=noninteractive
     * Bug 29167: Upgrade go to 1.11.5
     * Bug 29158: Install updated apt packages (CVE-2019-3462)
     * Bug 29097: Don't try to install python3.6-lxml for HTTPS Everywhere
     * Bug 27061: Enable verification of langpacks checksums
   * Windows
     * Bug 26148: Update binutils to 2.31.1
     * Bug 27320: Build certutil for Windows
   * OS X
     * Bug 27320: Build certutil for macOS
   * Linux
     * Bug 26323+29812: Build 32bit Linux bundles on 64bit Debian Wheezy
     * Bug 26148: Update binutils to 2.31.1
     * Bug 29758: Build firefox debug symbols for linux-i686
     * Bug 29966: Use archive.debian.org for Wheezy images
     * Bug 29183: Use linux-x86_64 langpacks on linux-x86_64
   * Android
     * Bug 29981: Add option to build without using containers

Tor Browser 8.5a12 -- May 7 2019
 * All platforms
   * Update Torbutton to 2.1.7
     * Bug 30388: Make sure the updated intermediate certificate keeps working
   * Backport fixes for bug 1549010 and bug 1549061
     * Bug 30388: Make sure the updated intermediate certificate keeps working

Tor Browser 8.0.9 -- May 7 2019
* All platforms
  * Update Torbutton to 2.0.13
    * Bug 30388: Make sure the updated intermediate certificate keeps working
  * Backport fixes for bug 1549010 and bug 1549061
    * Bug 30388: Make sure the updated intermediate certificate keeps working
  * Update NoScript to 10.6.1
     * Bug 29872: XSS popup with DuckDuckGo search on about:tor

Tor Browser 8.5a11 -- April 16 2019
 * All platforms
   * Update Torbutton to 2.1.6
     * Bug 22538+22513: Fix new circuit button for error pages
     * Bug 29825: Intelligently add new Security Level button to taskbar
     * Bug 29903: No WebGL click-to-play on the standard security level
     * Bug 27484: Improve navigation within onboarding (strings)
     * Bug 29768: Introduce new features to users (strings)
     * Bug 29943: Use locales in AB-CD scheme to match Mozilla
     * Bug 26498: Add locale: es-AR
     * Bug 29973: Remove remaining stopOpenSecuritySettingsObserver() pieces
     * Translations update
   * Update NoScript to 10.6.1
     * Bug 29872: XSS popup with DuckDuckGo search on about:tor
   * Bug 29916: Make sure enterprise policies are disabled
   * Bug 26498: Add locale: es-AR
 * Windows + OS X + Linux
   * Update Tor to *******-rc
   * Update Tor Launcher to ********
     * Bug 26498: Add locale es-AR
     * Translations update
   * Bug 29768: Introduce new features to users
   * Bug 27484: Improve navigation within onboarding
   * Bug 25658: Improve toolbar layout for new security settings
 * Windows
   * Bug 27503: Improve screen reader accessibility
 * Android
   * Bug 27609 (and child bugs): Use Tor Onion Proxy Library
   * Bug 29312: Bump Tor to *******
   * Bug 29859: Disable HLS support for now
   * Bug 28622: Update Tor Browser icon for mobile
   * Bug 29238: Prevent crash on Android after update
   * Bug 29982: Add additional safe guards against crashes during bootstrap
   * Bug 29906: Fix crash on older devices due to missing API
   * Bug 29858: Load onboarding panels after bootstrapping is done
   * Bug 28329: Improve bootstrapping experience
   * Bug 30016: Localize bootstrap-/bridge-related strings for mobile
 * Build System
   * All platforms
     * Bug 29868: Fix installation of python-future package
     * Bug 25623: Disable network during build
   * Linux
     * Bug 29966: Use archive.debian.org for Wheezy images
   * Android
     * Bug 30089: Use apksigner instead of jarsigner

Tor Browser 8.5a10 -- March 24 2019
 * All platforms
   * Update Firefox to 60.6.1esr
   * Update NoScript to 10.2.4
     * Bug 29733: Work around Mozilla's bug 1532530

Tor Browser 8.0.8 -- March 22 2019
 * All platforms
   * Update Firefox to 60.6.1esr
   * Update NoScript to 10.2.4
     * Bug 29733: Work around Mozilla's bug 1532530

Tor Browser 8.5a9 -- March 20 2019
 * All platforms
   * Update Firefox to 60.6.0esr
   * Update Torbutton to 2.1.5
     * Bug 25658: Replace security slider with security level UI
     * Bug 28628: Change onboarding Security panel to open new Security Level panel
     * Bug 29440: Update about:tor when Tor Browser is updated
     * Bug 27478: Improved Torbutton icons for dark theme
     * Bug 29021: Tell NoScript it is running within Tor Browser
     * Bug 29239: Don't ship the Torbutton .xpi on mobile
     * Translations update
   * Bug 29120: Enable media cache in memory
   * Bug 29445: Enable support for enterprise policies
 * Windows + OS X + Linux
   * Update Tor to *******-alpha
     * Bug 29660: XMPP can not connect to SOCKS5 anymore
   * Update OpenSSL to 1.0.2r
   * Update Tor Launcher to ********
     * Bug 29328: Account for Tor 0.4.0.x's revised bootstrap status reporting
     * Bug 22402: Improve "For assistance" link
     * Translations update
   * Bug 25658+29554: Replace security slider with security level UI
   * Bug 28885: notify users that update is downloading
   * Bug 29180: MAR download stalls when about dialog is opened
   * Bug 27485: Users are not taught how to open security-slider dialog
   * Bug 27486: Avoid about:blank tabs when opening onboarding pages
   * Bug 29440: Update about:tor when Tor Browser is updated
   * Bug 23359: WebExtensions icons are not shown on first start
   * Bug 28628: Change onboarding Security panel to open new Security Level panel
 * Android
   * Bug 28329: Design Tor Browser for Android configuration UI
   * Bug 28802: Support PTs in Tor Browser for Android
   * Bug 29794: Update TBA built-in bridges
   * Bug 27210: Add support for x86 on Android
   * Bug 29809: Only ship tor binary for .apk architecture
   * Bug 29633: Don't ship pdnsd anymore
   * Bug 28708: about:tor is not the default homepage after upgrade
   * Bug 29626: Application name is now "Always-On Notifications"
   * Bug 29467: Backport fix for arc4random_buf bustage
 * Build System
   * All platforms
     * Bug 25876: Generate source tarballs during build
     * Bug 28685: Set Build ID based on Tor Browser version
     * Bug 29194: Set DEBIAN_FRONTEND=noninteractive
   * Linux
     * Bug 26323+29812: Build 32bit Linux bundles on 64bit Debian Wheezy
     * Bug 29758: Build firefox debug symbols for linux-i686
   * Android
     * Bug 29632: Use HTTPS for downloading Gradle

Tor Browser 8.0.7 -- March 19 2019
 * All platforms
   * Update Firefox to 60.6.0esr
   * Update Tor to *******
     * Bug 29660: XMPP can not connect to SOCKS5 anymore
   * Update Torbutton to 2.0.11
     * Bug 29021: Tell NoScript it is running within Tor Browser
 * Windows
   * Bug 29081: Harden libwinpthread
 * Linux
   * Bug 27531: Add separate LD_LIBRARY_PATH for fteproxy

Tor Browser 8.5a8 -- February 13 2019
 * All platforms
   * Update Firefox to 60.5.1esr
   * Update HTTPS Everywhere to 2019.1.31
   * Bug 29378: Remove ************ from default bridges
   * Bug 29349: Remove network.http.spdy.* overrides from meek helper user.js
   * Bug 29327: TypeError: hostName is null on about:tor page
 * Build System
   * All Platforms
     * Bug 29235: Build our own version of python3.6 for HTTPS Everywhere
     * Bug 29167: Upgrade go to 1.11.5
   * Linux
     * Bug 29183: Use linux-x86_64 langpacks on linux-x86_64

Tor Browser 8.0.6 -- February 12 2019
 * All platforms
   * Update Firefox to 60.5.1esr
   * Update HTTPS Everywhere to 2019.1.31
   * Bug 29378: Remove ************ from default bridges
 * Build System
   * All Platforms
     * Bug 29235: Build our own version of python3.6 for HTTPS Everywhere

Tor Browser 8.5a7 -- January 29 2019
 * All Platforms
   * Update Firefox to 60.5.0esr
   * Update Torbutton to 2.1.4
     * Bug 25702: Update Tor Browser icon to follow design guidelines
     * Bug 21805: Add click-to-play button for WebGL
     * Bug 28836: Links on about:tor are not clickable
     * Bug 29035: Clean up our donation campaign and add newsletter sign-up link
     * Translations update
     * Code clean-up
   * Update HTTPS Everywhere to 2019.1.7
   * Update NoScript to 10.2.1
     * Bug 28873: Cascading of permissions is broken
     * Bug 28720: Some videos are blocked outright on higher security levels
   * Bug 29082: Backport patches for bug 1469916
   * Bug 28711: Backport patches for bug 1474659
   * Bug 27828: "Check for Tor Browser update" doesn't seem to do anything
   * Bug 29028: Auto-decline most canvas warning prompts again
   * Bug 27597: Fix our debug builds
 * Windows
   * Update Tor to *******-alpha
   * Bug 25702: Activity 1.1 Update Tor Browser icon to follow design guidelines
   * Bug 28111: Use Tor Browser icon in identity box
   * Bug 22654: Firefox icon is shown for Tor Browser on Windows 10 start menu
   * Bug 27503: Compile with accessibility support
   * Bug 28874: Bump mingw-w64 commit to fix WebGL crash
   * Bug 12885: Windows Jump Lists fail for Tor Browser
   * Bug 28618: Set MOZILLA_OFFICIAL for Windows build
 * OS X
   * Update Tor to *******-alpha
   * Bug 25702: Activity 1.1 Update Tor Browser icon to follow design guidelines
   * Bug 28111: Use Tor Browser icon in identity box
 * Linux
   * Update Tor to *******-alpha
   * Bug 25702: Activity 1.1 Update Tor Browser icon to follow design guidelines
   * Bug 28111: Use Tor Browser icon in identity box
   * Bug 27531: Fix crashing print dialog
 * Android
   * Bug 28705: Fix download crash on newer Android devices
   * Bug 28814: Backport 1480079 to allow installing downloaded apps
 * Build System
   * All Platforms
     * Bug 29158: Install updated apt packages (CVE-2019-3462)
     * Bug 29097: Don't try to install python3.6-lxml for HTTPS Everywhere
   * Windows
     * Bug 26148: Update binutils to 2.31.1
     * Bug 29081: Harden libwinpthread
   * Linux
     * Bug 26148: Update binutils to 2.31.1
   * Android
     * Bug 28752: Don't download tor-android-binary resources during build

Tor Browser 8.0.5 -- January 29 2019
 * All platforms
   * Update Firefox to 60.5.0esr
   * Update Tor to *******
   * Update Torbutton to 2.0.10
     * Bug 29035: Clean up our donation campaign and add newsletter sign-up link
     * Bug 27175: Add pref to allow users to persist custom noscript settings
   * Update HTTPS Everywhere to 2019.1.7
   * Update NoScript to 10.2.1
     * Bug 28873: Cascading of permissions is broken
     * Bug 28720: Some videos are blocked outright on higher security levels
   * Bug 26540: Enabling pdfjs disableRange option prevents pdfs from loading
   * Bug 28740: Adapt Windows navigator.platform value on 64-bit systems
   * Bug 28695: Set default security.pki.name_matching_mode to enforce (3)

Tor Browser 8.5a6 -- December 11 2018
 * All Platforms
   * Update Firefox to 60.4.0esr
   * Update Torbutton to 2.1.3
     * Bug 28540: Use new text for 2018 donation banner
     * Bug 27290: Remove WebGL pref for min capability mode
     * Bug 28075: Tone down missing SOCKS credential warning
     * Bug 28747: Remove NoScript (XPCOM) related unused code
     * Translations update
   * Bug 28608: Disable background HTTP response throttling
   * Bug 28695: Set default security.pki.name_matching_mode to enforce (3)
   * Bug 27290: Remove WebGL pref for min capability mode
   * Bug 27919: Backport SSL status API
   * Bug 25794: Disable pointer events
 * Windows
   * Update OpenSSL to 1.0.2q
   * Bug 28740: Adapt Windows navigator.platform value on 64-bit systems
 * OS X
   * Update OpenSSL to 1.0.2q
 * Linux
   * Update OpenSSL to 1.0.2q
 * Android
   * Bug 26843: Multi-locale support for Tor Browser on Android
 * Build System
   * Android
     * Bug 25164: Add .apk to our sha256sums unsigned build file
     * Bug 28696: Make path to Gradle dependencies reproducible
     * Bug 28697: Use pregenerated keystore and fix timestamp issues

Tor Browser 8.0.4 -- December 11 2018
 * All platforms
   * Update Firefox to 60.4.0esr
   * Update Tor to *******
   * Update OpenSSL to 1.0.2q
   * Update Torbutton to 2.0.9
     * Bug 28540: Use new text for 2018 donation banner
     * Bug 28515: Use en-US for english Torbutton strings
     * Translations update
   * Update HTTPS Everywhere to 2018.10.31
   * Update NoScript to 10.2.0
 * Bug 1623: Block protocol handler enumeration (backport of fix for #680300)
 * Bug 25794: Disable pointer events
 * Bug 28608: Disable background HTTP response throttling
 * Bug 28185: Add smallerRichard to Tor Browser
 * Windows
   * Bug 26381: about:tor page does not load on first start on Windows
   * Bug 28657: Remove broken FTE bridge from Tor Browser
 * OS X
   * Bug 26263: App icon positioned incorrectly in macOS DMG installer window
   * Bug 26475: Fix Stylo related reproducibility issue
 * Linux
   * Bug 26475: Fix Stylo related reproducibility issue
   * Bug 28657: Remove broken FTE bridge from Tor Browser
 * Build System
   * All Platforms
     * Bug 27218: Generate multiple Tor Browser bundles in parallel

Tor Browser 8.5a5 -- December 3 2018
 * All Platforms
   * Update Torbutton to 2.1.2
     * Bug 25013: Integrate Torbutton into tor-browser for Android
     * Bug 27111: Update about:tor desktop version to work on mobile
     * Bug 28093: Update donation banner style to make it fit in small screens
     * Bug 28543: about:tor has scroll bar between widths 900px and 1000px
     * Bug 28039: Enable dump() if log method is 0
     * Bug 27701: Don't show App Blocker dialog on Android
     * Bug 28187: Change tor circuit icon to torbutton.svg
     * Bug 28515: Use en-US for english Torbutton strings
     * Translations update
   * Update Tor Launcher to 0.2.18
     * Bug 28039: Enable dump() if log method is 0
     * Translations update
   * Update HTTPS Everywhere to 2018.10.31
   * Update NoScript to 10.2.0
   * Bug 22343: Make 'Save Page As' obey first-party isolation
   * Bug 26540: Enabling pdfjs disableRange option prevents pdfs from loading
 * Windows
   * Update Tor to *******-alpha
   * Bug 28310: Don't build obfs4 with module versioning support
   * Bug 27827: Update Go to 1.11.1
   * Bug 28185: Add smallerRichard to Tor Browser
   * Bug 28657: Remove broken FTE bridge from Tor Browser
 * OS X
   * Update Tor to *******-alpha
   * Bug 28310: Don't build obfs4 with module versioning support
   * Bug 27827: Update Go to 1.11.1
   * Bug 27827: Build snowflake reproducibly
   * Bug 28258: Don't look for webrtc headers under talk/
   * Bug 28185: Add smallerRichard to Tor Browser
 * Linux
   * Update Tor to *******-alpha
   * Bug 28310: Don't build obfs4 with module versioning support
   * Bug 27827: Update Go to 1.11.1
   * Bug 27827: Build snowflake reproducibly
   * Bug 28258: Don't look for webrtc headers under talk/
   * Bug 28185: Add smallerRichard to Tor Browser
   * Bug 28657: Remove broken FTE bridge from Tor Browser
 * Android
   * Bug 28051: Fix up Orbot for inclusion into Tor Browser
   * Bug 26690+25765: Port padlock states for .onion services to mobile
   * Bug 28507: Delete private data in the browser startup
   * Bug 27111+25013: Configure Tor Browser for mobile to load about:tor
   * Bug 27256: Enable TouchEvents on Android
   * Bug 28640: Use system add-on and distributed preferences
 * Build System
   * Bug 27977: Build Orbot inside tor-browser-build
   * Bug 27443: Update Firefox RBM config and build for Android
   * Bug 27439: Add android target for rust compiler
   * Bug 28469: Fix unsupported libbacktrace in Rust 1.26
   * Bug 28468: Modify Android toolchain to support Orbot
   * Bug 28483: Modify Android Toolchain API Version
   * Bug 28472: Add Android Makefile Rules
   * Bug 28470: Add fetch gradle dependency script to common project
   * Bug 28144: Update projects/tor-browser for Android

Tor Browser 8.5a4 -- October 23 2018
 * All Platforms
   * Update Firefox to 60.3.0esr
   * Update Tor to *******-alpha
   * Update Torbutton to 2.1.1
     * Bug 23925+27959: Donation banner for year end 2018 campaign
     * Bug 24172: Donation banner clobbers Tor Browser version string
     * Bug 28082: Add locales cs, el, hu, ka
     * Translations update
   * Update Tor Launcher to 0.2.17
     * Bug 27994+25151: Use the new Tor Browser logo
     * Bug 28082: Add locales cs, el, hu, ka
     * Translations update
   * Update HTTPS Everywhere to 2018.9.19
   * Update NoScript to ********
   * Bug 1623: Block protocol handler enumeration (backport of fix for #680300)
   * Bug 27905: Fix many occurrences of "Firefox" in about:preferences
   * Bug 28082: Add locales cs, el, hu, ka
 * Windows
   * Bug 21704: Abort install if CPU is missing SSE2 support
   * Bug 28002: Fix the precomplete file in the en-US installer
 * OS X
   * Bug 26263: App icon positioned incorrectly in macOS DMG installer window
   * Bug 26475: Fix Stylo related reproducibility issue
 * Linux
   * Bug 26475: Fix Stylo related reproducibility issue
   * Bug 28022: Use `/usr/bin/env bash` for bash invocation
 * Android
   * Backport of fixes for bug 1448014, 1458905, 1441345, and 1448305
 * Build System
   * All Platforms
     * Bug 27218: Generate multiple Tor Browser bundles in parallel
   * Windows
     * Bug 27320: Build certutil for Windows
   * OS X
     * Bug 27320: Build certutil for macOS

Tor Browser 8.0.3 -- October 23 2018
 * All platforms
   * Update Firefox to 60.3.0esr
   * Update Torbutton to 2.0.8
     * Bug 23925+27959: Donation banner for year end 2018 campaign
     * Bug 24172: Donation banner clobbers Tor Browser version string
     * Bug 27760: Use new NoScript API for IPC and fix about:blank issue
     * Translations update
   * Update HTTPS Everywhere to 2018.9.19
   * Update NoScript to ********
 * Linux
   * Bug 27546: Fix vertical scrollbar behavior in Tor Browser 8 with Gtk3
   * Bug 27552: Use bundled dir on CentOS/RHEL 6

Tor Browser 8.5a3 -- October 4 2018
 * All platforms
   * Update Firefox to 60.2.1esr
   * Backport fix for Mozilla bug 1493900 and 1493903
 * Windows
   * Bug 27865: Tor Browser 8.5a2 is crashing on Windows
 * OS X
   * Backport fix for Mozilla bug 1489785 for macOS 10.14 compatibility

Tor Browser 8.0.2 -- October 2 2018
 * All platforms
   * Update Firefox to 60.2.1esr
   * Backport fix for Mozilla bug 1493900 and 1493903
 * OS X
   * Backport fix for Mozilla bug 1489785 for macOS 10.14 compatibility

Tor Browser 8.5a2 -- September 24 2018
 * All platforms
   * Update Tor to *******-alpha
   * Update Torbutton to 2.1
     * Bug 27097: Tor News signup banner
     * Bug 27663: Add New Identity menuitem again
     * Bug 27175: Add pref to allow users to persist custom noscript settings
     * Bug 27760: Use new NoScript API for IPC and fix about:blank issue
     * Bug 26624: Only block OBJECT on highest slider level
     * Bug 26555: Don't show IP address for meek or snowflake
     * Bug 27478: Torbutton icons for dark theme
     * Bug 27506+14520: Move status version to upper left corner for RTL locales
     * Bug 27558: Update the link to "Your Guard note may not change" text
     * Bug 21263: Remove outdated information from the README
     * Translations update
   * Update Tor Launcher to ********
     * Bug 27469: Adapt Moat URLs
     * Translations update
     * Clean-up
   * Update NoScript to ********
   * Bug 27763: Restrict Torbutton signing exemption to mobile
   * Bug 26146: Spoof HTTP User-Agent header for desktop platforms
   * Bug 27543: QR code is broken on web.whatsapp.com
   * Bug 27264: Bookmark items are not visible on the boomark toolbar
   * Bug 27535: Enable TLS 1.3 draft version
   * Bug 27623: Use MOZILLA_OFFICIAL for our builds
   * Backport of Mozilla bug 1490585, 1475775, and 1489744
 * Windows:
   * Bug 26381: about:tor page does not load on first start on Windows
 * Linux:
   * Bug 27546: Fix vertical scrollbar behavior in Tor Browser 8 with Gtk3
   * Bug 27552: Use bundled dir on CentOS/RHEL 6
   * Bug 26556: Fix broken Tor Browser icon path on Linux

Tor Browser 8.0.1 -- September 24 2018
 * All platforms
   * Update Tor to *******
   * Update Torbutton to 2.0.7
     * Bug 27097: Tor News signup banner
     * Bug 27663: Add New Identity menuitem again
     * Bug 26624: Only block OBJECT on highest slider level
     * Bug 26555: Don't show IP address for meek or snowflake
     * Bug 27478: Torbutton icons for dark theme
     * Bug 27506+14520: Move status version to upper left corner for RTL locales
     * Bug 27427: Fix NoScript IPC for about:blank by whitelisting messages
     * Bug 27558: Update the link to "Your Guard note may not change" text
     * Translations update
   * Update Tor Launcher to ********
     * Bug 27469: Adapt Moat URLs
     * Translations update
     * Clean-up
   * Update NoScript to ********
   * Bug 27763: Restrict Torbutton signing exemption to mobile
   * Bug 26146: Spoof HTTP User-Agent header for desktop platforms
   * Bug 27543: QR code is broken on web.whatsapp.com
   * Bug 27264: Bookmark items are not visible on the boomark toolbar
   * Bug 27535: Enable TLS 1.3 draft version
   * Backport of Mozilla bug 1490585, 1475775, and 1489744
 * OS X
   * Bug 27482: Fix crash during start-up on macOS 10.9.x systems
 * Linux
   * Bug 26556: Fix broken Tor Browser icon path on Linux

Tor Browser 8.5a1 -- September 5 2018
 * All platforms
   * Update Firefox to 60.2.0esr
   * Update Tor to *******-rc
   * Update OpenSSL to 1.0.2p
   * Update Torbutton to 2.0.6
     * Bug 27401: Start listening for NoScript before it loads
     * Bug 27276: Adapt to new NoScript messaging protocol
     * Bug 26884: Use Torbutton to provide security slider on mobile
     * Bug 26962: Circuit display onboarding
     * Bug 26520: Fix sec slider/NoScript for TOR_SKIP_LAUNCH=1
     * Bug 26490: Remove the security slider notification
     * Bug 27301: Improve about:tor behavior and appearance
     * Bug 27097: Add text for Tor News signup widget
     * Bug 27214: Improve the onboarding text
     * Translations update
   * Update Tor Launcher to ********
     * Bug 25405: Cannot use Moat if a meek bridge is configured
     * Bug 27392: Update Moat URLs
     * Translations update
   * Update HTTPS Everywhere to 2018.8.22
   * Update NoScript to ********
   * Bug 26962: New feature onboarding
   * Bug 27403: The onboarding bubble is not always displayed
   * Bug 27283: Fix first-party isolation for UI tour
   * Bug 27213: Update about:tbupdate to new (about:tor) layout
   * Bug 26670: Make canvas permission prompt respect first-party isolation
   * Bug 26561: .onion images are not displayed
   * Bug 21787: Spoof en-US for date picker
   * Bug 21607: Disable WebVR for now until it is properly audited
   * Bug 21549: Disable wasm for now until it is properly audited
   * Bug 26614: Disable Web Authentication API until it is properly audited
   * Bug 27281: Enable Reader View mode again
   * Bug 26114: Don't expose navigator.mozAddonManager to websites
   * Bug 26048: Fix potentially confusing "restart to update" message
   * Bug 27221: Purge startup cache if Tor Browser version changed
   * Bug 26049: Reduce delay for showing update prompt to 1 hour
   * Bug 25405: Cannot use Moat if a meek bridge is configured
   * Bug 27268+27257+27262+26603: Preferences clean-up
 * Windows
   * Bug 26381: Work around endless loop during page load and about:tor not loading
   * Bug 27411: Fix broken security slider and NoScript interaction on Windows
 * Build System
   * All Platforms
     * Bug 27061: Enable verification of langpacks checksums
     * Bug 27178+27179: Add support for xz compression in mar files

Tor Browser 8.0 -- September 5 2018
 * All platforms
   * Update Firefox to 60.2.0esr
   * Update Tor to *******
   * Update OpenSSL to 1.0.2p
   * Update Libevent to 2.1.8
   * Update Torbutton to 2.0.6
     * Bug 26960: Implement new about:tor start page
     * Bug 26961: Implement new user onboarding
     * Bug 26962: Circuit display onboarding
     * Bug 27301: Improve about:tor behavior and appearance
     * Bug 27214: Improve the onboarding text
     * Bug 26321: Move 'New Identity', 'New Circuit' to File, hamburger menus
     * Bug 26100: Adapt Torbutton to Firefox 60 ESR
     * Bug 26520: Fix sec slider/NoScript for TOR_SKIP_LAUNCH=1
     * Bug 27401: Start listening for NoScript before it loads
     * Bug 26430: New Torbutton icon
     * Bug 24309: Move circuit display to the identity popup
     * Bug 26884: Use Torbutton to provide security slider on mobile
     * Bug 26128: Adapt security slider to the WebExtensions version of NoScript
     * Bug 27276: Adapt to new NoScript messaging protocol
     * Bug 23247: Show security state of .onions
     * Bug 26129: Show our about:tor page on startup
     * Bug 26235: Hide new unusable items from help menu
     * Bug 26058: Remove workaround for hiding 'sign in to sync' button
     * Bug 26590: Use new svg.disabled pref in security slider
     * Bug 26655: Adjust color and size of onion button
     * Bug 26500: Reposition circuit display relay icon for RTL locales
     * Bug 26409: Remove spoofed locale implementation
     * Bug 26189: Remove content-policy.js
       * Bug 26544: Images are not centered anymore
     * Bug 26490: Remove the security slider notification
     * Bug 25126: Make about:tor layout responsive
     * Bug 27097: Add text for Tor News signup widget
     * Bug 21245: Add da translation to Torbutton and keep track of it
     * Bug 27129+20628: Add locales ca, ga, id, is, nb, da, he, sv, and zh-TW
     * Translations update
  * Update Tor Launcher to ********
     * Bug 23136: Moat integration (fetch bridges for the user)
     * Bug 25750: Update Tor Launcher to make it compatible with Firefox 60 ESR
     * Bug 26985: Help button icons missing
     * Bug 25509: Improve the proxy help text
     * Bug 26466: Remove sv-SE from tracking for releases
     * Bug 27129+20628: Add locales ca, ga, id, is, nb, da, he, sv, and zh-TW
     * Translations update
   * Update HTTPS Everywhere to 2018.8.22
   * Update NoScript to ********
   * Update meek to 0.31
     * Bug 26477: Make meek extension compatible with ESR 60
   * Update obfs4proxy to v0.0.7 (bug 25356)
   * Bug 27082: Enable a limited UITour for user onboarding
   * Bug 26961: New user onboarding
   * Bug 26962: New feature onboarding
   * Bug 27403: The onboarding bubble is not always displayed
   * Bug 27283: Fix first-party isolation for UI tour
   * Bug 27213: Update about:tbupdate to new (about:tor) layout
   * Bug 14952+24553: Enable HTTP2 and AltSvc
     * Bug 25735: Tor Browser stalls while loading Facebook login page
   * Bug 17252: Enable TLS session identifiers with first-party isolation
   * Bug 26353: Prevent speculative connects that violate first-party isolation
   * Bug 26670: Make canvas permission prompt respect first-party isolation
   * Bug 24056: Use en-US strings in HTML forms if locale is spoofed to english
   * Bug 26456: HTTP .onion sites inherit previous page's certificate information
   * Bug 26561: .onion images are not displayed
   * Bug 26321: Move 'New Identity', 'New Circuit' to File, hamburger menus
   * Bug 26833: Backport Mozilla's bug 1473247
   * Bug 26628: Backport Mozilla's bug 1470156
   * Bug 26237: Clean up toolbar for ESR60-based Tor Browser
   * Bug 26519: Avoid Firefox icons in ESR60
   * Bug 26039: Load our preferences that modify extensions (fixup)
   * Bug 26515: Update Tor Browser blog post URLs
   * Bug 26216: Fix broken MAR file generation
   * Bug 26409: Remove spoofed locale implementation
   * Bug 25543: Rebase Tor Browser patches for ESR60
   * Bug 23247: Show security state of .onions
   * Bug 26039: Load our preferences that modify extensions
   * Bug 17965: Isolate HPKP and HSTS to URL bar domain
   * Bug 21787: Spoof en-US for date picker
   * Bug 21607: Disable WebVR for now until it is properly audited
   * Bug 21549: Disable wasm for now until it is properly audited
   * Bug 26614: Disable Web Authentication API until it is properly audited
   * Bug 18598: Disable WebSpeech API
   * Bug 27281: Enable Reader View mode again
   * Bug 26114: Don't expose navigator.mozAddonManager to websites
   * Bug 21850: Update about:tbupdate handling for e10s
   * Bug 26048: Fix potentially confusing "restart to update" message
   * Bug 27221: Purge startup cache if Tor Browser version changed
   * Bug 26049: Reduce delay for showing update prompt to 1 hour
   * Bug 26365: Add potential AltSvc support
   * Bug 9145: Fix broken hardware acceleration on Windows and enable it
   * Bug 22756: Show Canvas prompt only after user interaction
   * Bug 26045: Add new MAR signing keys
   * Bug 25215: Revert bug 18619 (we are not disabling IndexedDB any longer)
   * Bug 19910: Rip out optimistic data socks handshake variant (#3875)
   * Bug 22564: Hide Firefox Sync
   * Bug 21484: Hide "What's New" link from About dialog
   * Bug 25090: Disable updater telemetry
   * Bug 26127: Make sure Torbutton and Tor Launcher are not treated as legacy extensions
   * Bug 13575: Disable randomised Firefox HTTP cache decay user tests
   * Bug 22548: Firefox downgrades VP9 videos to VP8 for some users
   * Bug 24995: Include git hash in tor --version
   * Bug 27268+27257+27262+26603 : Preferences clean-up
   * Bug 26073: Migrate general.useragent.locale to intl.locale.requested
   * Bug 27129+20628: Make Tor Browser available in ca, ga, id, is, nb, da, he, sv, and zh-TW
     * Bug 12927: Include Hebrew translation into Tor Browser
     * Bug 21245: Add danish (da) translation
 * Windows
   * Bug 20636+10026: Create 64bit Tor Browser for Windows
     * Bug 26239+24197: Enable content sandboxing for 64bit Windows builds
     * Bug 26514: Fix intermittent updater failures on Win64 (Error 19)
     * Bug 26874: Fix UNC path restrictions failure in Tor Browser 8.0a9
     * Bug 12968: Enable HEASLR in Windows x86_64 builds
   * Bug 26381: Work around endless loop during page load and about:tor not loading
   * Bug 27411: Fix broken security slider and NoScript interaction on Windows
   * Bug 22581: Fix shutdown crash
   * Bug 25266: PT config should include full names of executable files
   * Bug 26304: Update zlib to version 1.2.11
   * Update tbb-windows-installer to 0.4
     * Bug 26355: Update tbb-windows-installer to check for Windows7+
   * Bug 26355: Require Windows7+ for updates to Tor Browser 8
 * OS X
   * Bug 24136: After loading file:// URLs clicking on links is broken on OS X
   * Bug 24243: Tor Browser only renders HTML for local pages via file://
   * Bug 24263: Tor Browser does not run extension scripts if loaded via about:debugging
   * Bug 22794: Don't open AF_INET/AF_INET6 sockets when AF_LOCAL is configured
 * Linux
   * Bug 22794: Don't open AF_INET/AF_INET6 sockets when AF_LOCAL is configured
   * Bug 25485: Unbreak Tor Browser on systems with newer libstdc++
   * Bug 20866: Fix OpenGL software rendering on systems with newer libstdc++
   * Bug 26951+18022: Fix execdesktop argument passing
   * Bug 24136: After loading file:// URLs clicking on links is broken on Linux
   * Bug 24243: Tor Browser only renders HTML for local pages via file://
   * Bug 24263: Tor Browser does not run extension scripts if loaded via about:debugging
   * Bug 20283: Tor Browser should run without a `/proc` filesystem.
   * Bug 26354: Set SSE2 support as minimal requirement for Tor Browser 8
 * Build System
   * All Platforms
     * Bug 26362+26410: Use old MAR format for first ESR60-based stable
     * Bug 27020: RBM build fails with runc version 1.0.1
     * Bug 26949: Use GitHub repository for STIX
     * Bug 26773: Add --verbose to the ./mach build flag for firefox
     * Bug 26319: Don't package up Tor Browser in the `mach package` step
     * Bug 27178: Add support for xz compression in mar files
     * Clean up
   * Windows
     * Bug 26203: Adapt tor-browser-build/tor-browser for Windows
     * Bug 26204: Bundle d3dcompiler_47.dll for Tor Browser 8
     * Bug 26205: Don't build the uninstaller for Windows during Firefox compilation
     * Bug 26206: Ship pthread related dll where needed
     * Bug 26396: Build libwinpthread reproducible
     * Bug 25837: Integrate fxc2 into our build setup for Windows builds
     * Bug 27152: Use mozilla/fxc2.git for the fxc2 repository
     * Bug 25894: Get a rust cross-compiler for Windows
     * Bug 25554: Bump mingw-w64 version for ESR 60
     * Bug 23561: Fix nsis builds for Windows 64
       * Bug 13469: Windows installer is missing many languages from NSIS file
     * Bug 23231: Remove our STL Wrappers workaround for Windows 64bit
     * Bug 26370: Don't copy msvcr100.dll and libssp-0.dll twice
     * Bug 26476: Work around Tor Browser crashes due to fix for bug 1467041
     * Bug 18287: Use SHA-2 signature for Tor Browser setup executables
     * Bug 25420: Update GCC to 6.4.0
     * Bug 16472: Update Binutils to 2.26.1
     * Bug 20302: Fix FTE compilation for Windows with GCC 6.4.0
     * Bug 25111: Don't compile Yasm on our own anymore for Windows Tor Browser
     * Bug 18691: Switch Windows builds from precise to jessie
   * OS X
     * Bug 24632: Update macOS toolchain for ESR 60
     * Bug 9711: Build our own cctools for macOS cross-compilation
     * Bug 25548: Update macOS SDK for Tor Browser builds to 10.11
     * Bug 26003: Clean up our mozconfig-osx-x86_64 file
     * Bug 26195: Use new cctools in our macosx-toolchain project
     * Bug 25975: Get a rust cross-compiler for macOS
     * Bug 26475: Disable Stylo to make macOS build reproducible
     * Bug 26489: Fix .app directory name in tools/dmg2mar
   * Linux
     * Bug 26073: Patch tor-browser-build for transition to ESR 60
     * Bug 25481: Rust support for tor-browser and tor
     * Bug 25304: Update GCC to 6.4.0
     * Bug 16472: Update Binutils to 2.26.1

Tor Browser 8.0a10 -- August 20 2018
 * All platforms
   * Update Tor to 0.3.4.6-rc
   * Update Torbutton to 2.0.2
     * Bug 26960: Implement new about:tor start page
     * Bug 26961: Implement new user onboarding
     * Bug 26321: Move 'New Identity', 'New Circuit' to File, hamburger menus
     * Bug 26590: Use new svg.disabled pref in security slider
     * Bug 26655: Adjust color and size of onion button
     * Bug 26500: Reposition circuit display relay icon for RTL locales
     * Bug 26409: Remove spoofed locale implementation
     * Bug 26189: Remove content-policy.js
       * Bug 26544: Images are not centered anymore
     * Bug 27129: Add locales ca, ga, id, is, nb
     * Translations update
   * Update Tor Launcher to ********
     * Bug 26985: Help button icons missing
     * Bug 25509: Improve the proxy help text
     * Bug 27129: Add locales ca, ga, id, is, nb
     * Translations update
   * Update NoScript to *********
   * Update meek to 0.31
     * Bug 26477: Make meek extension compatible with ESR 60
   * Bug 27082: Enable a limited UITour for user onboarding
   * Bug 26961: New user onboarding
   * Bug 14952+24553: Enable HTTP2 and AltSvc
     * Bug 25735: Tor Browser stalls while loading Facebook login page
   * Bug 17252: Enable TLS session identifiers with first-party isolation
   * Bug 26353: Prevent speculative connects that violate first-party isolation
   * Bug 24056: Use en-US strings in HTML forms if locale is spoofed to english
   * Bug 26456: HTTP .onion sites inherit previous page's certificate information
   * Bug 26321: Move 'New Identity', 'New Circuit' to File, hamburger menus
   * Bug 26833: Backport Mozilla's bug 1473247
   * Bug 26628: Backport Mozilla's bug 1470156
   * Bug 26237: Clean up toolbar for ESR60-based Tor Browser
   * Bug 26519: Avoid Firefox icons in ESR60
   * Bug 26039: Load our preferences that modify extensions (fixup)
   * Bug 26515: Update Tor Browser blog post URLs
   * Bug 27129: Add locales ca, ga, id, is, nb
   * Bug 26216: Fix broken MAR file generation
   * Bug 26409: Remove spoofed locale implementation
   * Bug 26603: Remove obsolete HTTP pipelining preferences
 * Windows
   * Bug 26514: Fix intermittent updater failures on Win64 (Error 19)
   * Bug 26874: Fix UNC path restrictions failure in Tor Browser 8.0a9
   * Bug 12968: Enable HEASLR in Windows x86_64 builds
   * Update tbb-windows-installer to 0.4
     * Bug 26355: Update tbb-windows-installer to check for Windows7+
   * Bug 26355: Require Windows7+ for updates to Tor Browser 8
 * OS X
   * Bug 26795: Bump snowflake to 6077141f4a for bug 25600
 * Linux
   * Bug 25485: Unbreak Tor Browser on systems with newer libstdc++
   * Bug 20866: Fix OpenGL software rendering on systems with newer libstdc++
   * Bug 26951+18022: Fix execdesktop argument passing
   * Bug 26795: Bump snowflake to 6077141f4a for bug 25600
 * Build System
   * All Platforms
     * Bug 26410: Stop using old MAR format in the alpha series
     * Bug 27020: RBM build fails with runc version 1.0.1
     * Bug 26949: Use GitHub repository for STIX
     * Bug 26773: Add --verbose to the ./mach build flag for firefox
     * Bug 26569: Redirect pre-8.0a9 alpha users to a separate update directory
     * Bug 26319: Don't package up Tor Browser in the `mach package` step
   * OS X
     * Bug 26489: Fix .app directory name in tools/dmg2mar
   * Windows
     * Bug 27152: Use mozilla/fxc2.git for the fxc2 repository

Tor Browser 8.0a9 -- June 27 2018
 * All platforms
   * Update Firefox to 60.1.0esr
   * Update Tor to *******-alpha
   * Update Libevent to 2.1.8
   * Update Torbutton to 2.0.1
     * Bug 26100: Adapt Torbutton to Firefox 60 ESR
     * Bug 26430: New Torbutton icon
     * Bug 24309: Move circuit display to the identity popup
     * Bug 26128: Adapt security slider to the WebExtensions version of NoScript
     * Bug 23247: Show security state of .onions
     * Bug 26129: Show our about:tor page on startup
     * Bug 26235: Hide new unusable items from help menu
     * Bug 26058: Remove workaround for hiding 'sign in to sync' button
     * Bug 20628: Add locales da, he, sv, and zh-TW
     * Translations update
   * Update Tor Launcher to ********
     * Bug 25750: Update Tor Launcher to make it compatible with Firefox 60 ESR
     * Bug 20890: Increase control port connection timeout
     * Bug 26466: Remove sv-SE from tracking for releases
     * Bug 20628: Add more locales to Tor Browser
     * Translations update
   * Update HTTPS Everywhere to 2018.6.21
   * Update NoScript to ********
   * Bug 25543: Rebase Tor Browser patches for ESR60
   * Bug 23247: Show security state of .onions
   * Bug 26039: Load our preferences that modify extensions
   * Bug 17965: Isolate HPKP and HSTS to URL bar domain
   * Bug 26365: Add potential AltSvc support
   * Bug 9145: Fix broken hardware acceleration on Windows and enable it
   * Bug 22756: Show Canvas prompt only after user interaction
   * Bug 26045: Add new MAR signing keys
   * Bug 22564: Hide Firefox Sync
   * Bug 21484: Hide "What's New" link from About dialog
   * Bug 25090: Disable updater telemetry
   * Bug 18598: Disable WebSpeech API
   * Bug 26127: Make sure Torbutton and Tor Launcher are not treated as legacy extensions
   * Bug 26073: Migrate general.useragent.locale to intl.locale.requested
   * Bug 20628: Make Tor Browser available in da, he, sv-SE, and zh-TW
     * Bug 12927: Include Hebrew translation into Tor Browser
     * Bug 21245: Add danish (da) translation
 * Windows
   * Bug 26239+24197: Enable content sandboxing for 64bit Windows builds
   * Bug 22581: Fix shutdown crash
   * Bug 26424: Disable UNC paths to prevent possible proxy bypasses
   * Bug 26304: Update zlib to version 1.2.11
 * OS X
   * Bug 24052: Backport fix for bug 1412081 for better file:// handling
   * Bug 24136: After loading file:// URLs clicking on links is broken on OS X
   * Bug 24243: Tor Browser only renders HTML for local pages via file://
   * Bug 24263: Tor Browser does not run extension scripts if loaded via about:debugging
   * Bug 24632: Disable snowflake for now until its build is fixed
   * Bug 26438: Remove broken seatbelt profiles
 * Linux
   * Bug 24052: Backport fix for bug 1412081 for better file:// handling
   * Bug 24136: After loading file:// URLs clicking on links is broken on Linux
   * Bug 24243: Tor Browser only renders HTML for local pages via file://
   * Bug 24263: Tor Browser does not run extension scripts if loaded via about:debugging
   * Bug 26153: Update selfrando to be compatible with Firefox 60 ESR
   * Bug 22242: Remove RUNPATH in Linux binaries embedded by selfrando
   * Bug 26354: Set SSE2 support as minimal requirement for Tor Browser 8
 * Build System
  * All Platforms
    * Bug 26362: Use old MAR format for first ESR60-based alpha
    * Clean up
  * Windows
    * Bug 26203: Adapt tor-browser-build/tor-browser for Windows
    * Bug 26204: Bundle d3dcompiler_47.dll for Tor Browser 8
    * Bug 26205: Don't build the uninstaller for Windows during Firefox compilation
    * Bug 26206: Ship pthread related dll where needed
    * Bug 26396: Build libwinpthread reproducible
    * Bug 25837: Integrate fxc2 into our build setup for Windows builds
    * Bug 25894: Get a rust cross-compiler for Windows
    * Bug 25554: Bump mingw-w64 version for ESR 60
    * Bug 23561: Fix nsis builds for Windows 64
      * Bug 13469: Windows installer is missing many languages from NSIS file
    * Bug 23231: Remove our STL Wrappers workaround for Windows 64bit
    * Bug 26370: Don't copy msvcr100.dll and libssp-0.dll twice
    * Bug 26476: Work around Tor Browser crashes due to fix for bug 1467041
    * Bug 18287: Use SHA-2 signature for Tor Browser setup executables
    * Bug 16472: Update Binutils to 2.26.1
  * OS X
    * Bug 24632: Update macOS toolchain for ESR 60
    * Bug 9711: Build our own cctools for macOS cross-compilation
    * Bug 25548: Update macOS SDK for Tor Browser builds to 10.11
    * Bug 26003: Clean up our mozconfig-osx-x86_64 file
    * Bug 26195: Use new cctools in our macosx-toolchain project
    * Bug 25975: Get a rust cross-compiler for macOS
    * Bug 26475: Disable Stylo to make macOS build reproducible
  * Linux
    * Bug 26073: Patch tor-browser-build for transition to ESR 60
    * Bug 25540: Stop building and distributing sandboxed tor browser
    * Bug 25481: Rust support for tor-browser and tor
    * Bug 16472: Update Binutils to 2.26.1

Tor Browser 7.5.6 -- June 26 2018
 * All platforms
   * Update Firefox to 52.9.0esr
   * Update Tor to *******
   * Update Tor Launcher to ********
     * Bug 20890: Increase control port connection timeout
   * Update HTTPS Everywhere to 2018.6.21
     * Bug 26451: Prevent HTTPS Everywhere from freezing the browser
   * Update NoScript to *******
   * Bug 21537: Mark .onion cookies as secure
   * Bug 25938: Backport fix for cross-origin header leak (bug 1334776)
   * Bug 25721: Backport patches from Mozilla's bug 1448771
   * Bug 25147+25458: Sanitize HTML fragments for chrome documents
   * Bug 26221: Backport fix for leak in SHA256 in nsHttpConnectionInfo.cpp
 * Windows
   * Bug 26424: Disable UNC paths to prevent possible proxy bypasses

Tor Browser 8.0a8 -- June 10 2018
 * All platforms
   * Update Firefox to 52.8.1esr
   * Bug 26098: Remove amazon-meek

Tor Browser 7.5.5 -- June 10 2018
 * All platforms
   * Update Firefox to 52.8.1esr
   * Bug 26098: Remove amazon-meek

Tor Browser 8.0a7 -- May 9 2018
 * All platforms
   * Update Firefox to 52.8.0esr
   * Update Tor Launcher to ********
     * Bug 25807: Change front domain to unbreak Moat
     * Translations update
   * Bug 25973: Backport off-by-one fix (bug 1352073)
   * Bug 25938: Backport fix for cross-origin header leak (bug 1334776)
   * Bug 25458: Fix broken UI customization
   * Bug 25898: Make Youtube videos play automatically again
   * Bug 25980: Improve backport of bug 1448771 (fixes broken Orfox build)
 * OS X
   * Bug 26010: Change Snowflake rendezvous to use the Azure domain front
 * Linux
   * Bug 26010: Change Snowflake rendezvous to use the Azure domain front

Tor Browser 7.5.4 -- May 9 2018
 * All platforms
   * Update Firefox to 52.8.0esr
   * Update HTTPS Everywhere to 2018.4.11
   * Update NoScript to *******
   * Bug 23439: Exempt .onion domains from mixed content warnings
   * Bug 22614: Make e10s/non-e10s Tor Browsers indistinguishable
   * Bug 22659: Changes to `intl.accept.languages` get overwritten after restart
   * Bug 25973: Backport off-by-one fix (bug 1352073)
   * Bug 25020: Add a tbb_version.json file

Tor Browser 8.0a6 -- April 19 2018
 * All platforms
   * Update Tor to *******-rc
   * Update OpenSSL to 1.0.2o
   * Update Torbutton to *******
     * Bug 25126: Make about:tor layout responsive
     * Translations update
   * Update HTTPS Everywhere to 2018.4.11
   * Update NoScript to *******
   * Bug 21537: Mark .onion cookies as secure
   * Bug 21850: Update about:tbupdate handling for e10s
   * Bug 25721: Backport patches from Mozilla's bug 1448771
 * Linux
   * Bug 20283: Tor Browser should run without a `/proc` filesystem.
 * Windows
   * Bug 13893: Make EMET compatible with Tor Browser
 * Build System
   * Windows
     * Bug 25420: Update GCC to 6.4.0
     * Bug 20302: Fix FTE compilation for Windows with GCC 6.4.0
   * Linux
     * Bug 25304: Update GCC to 6.4.0

Tor Browser 8.0a5 -- March 27 2018
 * All platforms
   * Update Firefox to 52.7.3esr
   * Update HTTPS Everywhere to 2018.3.13
   * Bug 23439: Exempt .onion domains from mixed content warnings
 * OS X
   * Update Snowflake
     * Bug 21312+25579+25449: Fix crashes and memory/file descriptor leaks in go-webrtc
 * Linux
   * Update Snowflake
     * Bug 21312+25579+25449: Fix crashes and memory/file descriptor leaks in go-webrtc

Tor Browser 7.5.3 -- March 26 2018
 * All platforms
   * Update Firefox to 52.7.3esr
   * Update HTTPS Everywhere to 2018.3.13
     * Bug 25339: Adapt build system for Python 3.6 based build procedure

Tor Browser 8.0a4 -- March 17 2018
 * All platforms
   * Update Firefox to 52.7.2esr

Tor Browser 7.5.2 -- March 17 2018
 * All platforms
   * Update Firefox to 52.7.2esr

Tor Browser 8.0a3 -- March 13 2018
 * All platforms
   * Update Firefox to 52.7.0esr
   * Update Tor to *******-alpha
   * Update Tor Launcher to ********
     * Bug 23136: Moat integration (fetch bridges for the user)
     * Translations update
   * Update HTTPS Everywhere to 2018.2.26
     * Bug 25339: Adapt build system for Python 3.6 based build procedure
   * Bug 25356: Update obfs4proxy to v0.0.7
   * Bug 25147: Sanitize HTML fragments created for chrome-privileged documents
 * Windows
   * Bug 25112: No sandboxing on 64-bit Windows <= Vista

Tor Browser 7.5.1 -- March 13 2018
 * All platforms
   * Update Firefox to 52.7.0esr
   * Update Tor to *******0
   * Update Torbutton to *******
     * Bug 24159: Version check does not deal with platform specific checks
     * Bug 25016: Remove 2017 donation banner
     * Translations update
   * Update Tor Launcher to ********
     * Bug 25089: Special characters are not escaped in proxy password
     * Translations update
   * Update NoScript to *******
   * Bug 25356: Update obfs4proxy to v0.0.7
   * Bug 25000: Add [System+Principal] to the NoScript whitelist
 * Windows
   * Bug 25112: Disable sandboxing on 64-bit Windows <= Vista

Tor Browser 8.0a2 -- February 23 2018
 * All Platforms
   * Update Tor to *******-alpha
   * Update Torbutton to 1.9.9
     * Bug 24159: Version check does not deal with platform specific checks
     * Bug 25016: Remove 2017 donation banner
     * Translations update
   * Update Tor Launcher to 0.2.15
     * Bug 25089: Special characters are not escaped in proxy password
     * Translations update
   * Update HTTPS Everywhere to 2018.1.29
   * Update NoScript to *******
   * Update meek to 0.29
   * Bug 25215: Revert bug 18619 (we are not disabling IndexedDB any longer)
   * Bug 19910: Rip out optimistic data socks handshake variant (#3875)
   * Bug 22659: Changes to `intl.accept.languages` get overwritten after restart
   * Bug 25000: Add [System+Principal] to the NoScript whitelist
   * Bug 15599: Disable Range requests used by pdfjs as they are not isolated
   * Bug 22614: Make e10s/non-e10s Tor Browsers indistinguishable
   * Bug 13575: Disable randomised Firefox HTTP cache decay user tests
   * Bug 25020: Add a tbb_version.json file
   * Bug 24995: Include git hash in tor --version
 * OS X
   * Bug 22794: Don't open AF_INET/AF_INET6 sockets when AF_LOCAL is configured
 * Linux
   * Bug 22794: Don't open AF_INET/AF_INET6 sockets when AF_LOCAL is configured
 * Windows:
   * Bug 25266: PT config should include full names of executable files
 * Build System
   * Windows
     * Bug 25111: Don't compile Yasm on our own anymore for Windows Tor Browser

Tor Browser 8.0a1 -- January 23 2018
 * All Platforms
   * Update Firefox to 52.6.0esr
   * Update Tor to *******
   * Update Torbutton to *******
     * Bug 21245: Add da translation to Torbutton and keep track of it
     * Bug 24702: Remove Mozilla text from banner
     * Translations update
   * Update Tor Launcher to 0.2.14.3
     * Translations update
   * Update HTTPS Everywhere to 2018.1.11
   * Bug 24756: Add noisebridge01 obfs4 bridge configuration
   * Bug 23916: Add new MAR signing key
   * Bug 22548: Firefox downgrades VP9 videos to VP8 for some users
 * Windows
   * Bug 24197: Fix win64 sandbox compile issues
 * Build System
   * Windows
     * Bug 18691: switch Windows builds from precise to jessie
   * Linux
     * Bug 23892: Include Firefox and Tor debug files in final build directory
     * Bug 24842: include libasan.so.2 and libubsan.so.0 in debug builds

Tor Browser 7.5 -- January 23 2018
 * All Platforms
   * Update Firefox to 52.6.0esr
   * Update Tor to *******
   * Update OpenSSL to 1.0.2n
   * Update Torbutton to *******
     * Bug 21847: Update copy for security slider
     * Bug 21245: Add da translation to Torbutton and keep track of it
     * Bug 24702: Remove Mozilla text from banner
     * Bug 10573: Replace deprecated nsILocalFile with nsIFile (code clean-up)
     * Translations update
   * Update Tor Launcher to 0.2.14.3
     * Bug 23262: Implement integrated progress bar
     * Bug 23261: implement configuration portion of new Tor Launcher UI
     * Bug 24623: Revise "country that censors Tor" text
     * Bug 24624: tbb-logo.svg may cause network access
     * Bug 23240: Retrieve current bootstrap progress before showing progress bar
     * Bug 24428: Bootstrap error message sometimes lost
     * Bug 22232: Add README on use of bootstrap status messages
     * Bug 10573: Replace deprecated nsILocalFile with nsIFile (code clean-up)
     * Translations update
   * Update HTTPS Everywhere to 2018.1.11
   * Update NoScript to *******
   * Bug 23104: CSS line-height reveals the platform Tor Browser is running on
   * Bug 24398: Plugin-container process exhausts memory
   * Bug 22501: Requests via javascript: violate FPI
   * Bug 24756: Add noisebridge01 obfs4 bridge configuration
 * Windows
   * Bug 16010: Enable content sandboxing on Windows
   * Bug 23230: Fix build error on Windows 64
 * OS X
   * Bug 24566: Avoid white flashes when opening dialogs in Tor Browser
   * Bug 23025: Add some hardening flags to macOS build
 * Linux
   * Bug 23970: Make "Print to File" work with sandboxing enabled
   * Bug 23016: "Print to File" is broken on some non-english Linux systems
   * Bug 10089: Set middlemouse.contentLoadURL to false by default
   * Bug 18101: Suppress upload file dialog proxy bypass (linux part)
 * Android
   * Bug 22084: Spoof network information API
 * Build System
   * All Platforms
     * Switch from gitian/tor-browser-bundle to rbm/tor-browser-build
   * Windows
     * Bug 22563: Update mingw-w64 to fix W^X violations
     * Bug 20929: Bump GCC version to 5.4.0
   * Linux
     * Bug 20929: Bump GCC version to 5.4.0
     * Bug 23892: Include Firefox and Tor debug files in final build directory
     * Bug 24842: include libasan.so.2 and libubsan.so.0 in debug builds

Tor Browser 7.5a10 -- December 19 2017
 * All Platforms
   * Update Tor to 0.3.2.7-rc
   * Update OpenSSL to 1.0.2n
   * Update Torbutton to *******
     * Bug 21847: Update copy for security slider
     * Bug 10573: Replace deprecated nsILocalFile with nsIFile (code clean-up)
     * Translations update
   * Update Tor Launcher to ********
     * Bug 24623: Revise "country that censors Tor" text
     * Bug 24428: Bootstrap error message sometimes lost
     * Bug 24624: tbb-logo.svg may cause network access
     * Bug 10573: Replace deprecated nsILocalFile with nsIFile (code clean-up)
     * Translations update
   * Update NoScript to *******
   * Bug 23104: CSS line-height reveals the platform Tor Browser is running on
   * Bug 24398: Plugin-container process exhausts memory
 * OS X
   * Bug 24566: Avoid white flashes when opening dialogs in Tor Browser
 * Linux
   * Bug 23970: Make "Print to File" work with sandboxing enabled
   * Bug 23016: "Print to File" is broken on some non-english Linux systems
 * Android
   * Bug 22084: Spoof network information API

Tor Browser 7.5a9 -- December 09 2017
 * All Platforms
   * Update Firefox to 52.5.2esr
   * Update Tor to *******-alpha
   * Update HTTPS-Everywhere to 2017.12.6
   * Update NoScript to *******
   * Update sandboxed-tor-browser to 0.0.16

Tor Browser 7.0.11 -- December 09 2017
 * All Platforms
   * Update Firefox to 52.5.2esr
   * Update Tor to *******
   * Update HTTPS-Everywhere to 2017.12.6
   * Update NoScript to *******

Tor Browser 7.5a8 -- November 15 2017
 * All Platforms
   * Update Firefox to 52.5.0esr
   * Update Tor to *******-alpha
   * Update Torbutton to *******
     * Bug 23997: Add link to Tor Browser manual for de, nl, tr, vi
     * Bug 23949: Fix donation banner display
     * Update locales with translated banner
     * Translations update
   * Update Tor Launcher to ********
     * Bug 23262: Implement integrated progress bar
     * Bug 23261: implement configuration portion of new Tor Launcher UI
     * Translations update
   * Update HTTPS-Everywhere to 2017.10.30
   * Update NoScript to 5.1.5
     * Bug 23968: NoScript icon jumps to the right after update
   * Update sandboxed-tor-browser to 0.0.15
 * Windows
   * Bug 20636+10026: Create 64bit Tor Browser for Windows
   * Bug 24052: Block file:// redirects early

Tor Browser 7.0.10 -- November 14 2017
 * All Platforms
   * Update Firefox to 52.5.0esr
   * Update Tor to *******
   * Update Torbutton to ********
     * Bug 23997: Add link to Tor Browser manual for de, nl, tr, vi
     * Translations update
   * Update HTTPS-Everywhere to 2017.10.30
     * Bug 24178: Use make.sh for building HTTPS-Everywhere
   * Update NoScript to 5.1.5
     * Bug 23968: NoScript icon jumps to the right after update
 * Windows
   * Bug 23582: Enable the Windows DLL blocklist for mingw-w64 builds
   * Bug 23396: Update the msvcr100.dll we ship
   * Bug 24052: Block file:// redirects early

Tor Browser 7.5a7 -- November 4 2017
 * OS X
   * Bug 24052: Streamline handling of file:// resources
 * Linux
   * Bug 24052: Streamline handling of file:// resources

Tor Browser 7.0.9 -- November 3 2017
 * OS X
   * Bug 24052: Streamline handling of file:// resources
 * Linux
   * Bug 24052: Streamline handling of file:// resources

Tor Browser 7.0.8 -- October 25 2017
 * All Platforms
   * Update Torbutton to *******
     * Bug 23949: Fix donation banner display
     * Update locales with translated banner
     * Translations update

Tor Browser 7.5a6 -- October 19 2017
 * All Platforms
   * Update Firefox to 52.4.1esr
   * Update Tor to *******-alpha
   * Update Torbutton to *******
     * Bug 23887: Update banner locales and Mozilla text
     * Translations update
   * Update HTTPS-Everywhere to 2017.10.4
   * Update NoScript to 5.1.2
     * Bug 23723: Loading entities from NoScript .dtd files is blocked
     * Bug 23724: NoScript update breaks Security Slider and its icon disappears
   * Update sandboxed-tor-browser to 0.0.14
   * Bug 23745: Tab crashes when using Tor Browser to access Google Drive
   * Bug 23694: Update the detailsURL in update responses
   * Bug 22501: Requests via javascript: violate FPI
 * OS X
   * Bug 23807: Tab crashes when playing video on High Sierra
   * Bug 23025: Add some hardening flags to macOS build

Tor Browser 7.0.7 -- October 19 2017
 * All Platforms
   * Update Firefox to 52.4.1esr
   * Update Torbutton to *******
     * Bug 23887: Update banner locales and Mozilla text
     * Bug 23526: Add 2017 Donation banner text
     * Bug 23483: Donation banner on about:tor for 2017 (testing mode)
     * Bug 22610: Avoid crashes when canceling external helper app related downloads
     * Bug 22472: Fix FTP downloads when external helper app dialog is shown
     * Bug 22471: Downloading pdf files via the PDF viewer download button is broken
     * Bug 22618: Downloading pdf file via file:/// is stalling
     * Translations update
   * Update HTTPS-Everywhere to 2017.10.4
   * Update NoScript to 5.1.2
     * Bug 23723: Loading entities from NoScript .dtd files is blocked
     * Bug 23724: NoScript update breaks Security Slider and its icon disappears
   * Bug 23745: Tab crashes when using Tor Browser to access Google Drive
   * Bug 22610: Avoid crashes when canceling external helper app related downloads
   * Bug 22472: Fix FTP downloads when external helper app dialog is shown
   * Bug 22471: Downloading pdf files via the PDF viewer download button is broken
   * Bug 22618: Downloading pdf file via file:/// is stalling
   * Bug 23694: Update the detailsURL in update responses
 * OS X
   * Bug 23807: Tab crashes when playing video on High Sierra
 * Linux
   * Bug 22692: Enable content sandboxing on Linux

Tor Browser 7.5a5 -- September 28 2017
 * All Platforms
   * Update Firefox to 52.4.0esr
   * Update Tor to *******-alpha
   * Update Torbutton to *******
     * Bug 20375: Warn users after entering fullscreen mode
     * Bug 22989: Fix dimensions of new windows on macOS
     * Bug 23526: Add 2017 Donation banner text
     * Bug 23483: Donation banner on about:tor for 2017 (testing mode)
     * Translations update
   * Update Tor Launcher to 0.2.13
     * Bug 23240: Retrieve current bootstrap progress before showing progress bar
     * Bug 22232: Add README on use of bootstrap status messages
     * Translations update
   * Update HTTPS-Everywhere to 2017.9.12
   * Update NoScript to 5.0.10
   * Update sandboxed-tor-browser to 0.0.13
   * Bug 23393: Don't crash all tabs when closing one tab
   * Bug 23166: Add new obfs4 bridge to the built-in ones
   * Bug 23258: Fix broken HTTPS-Everywhere on higher security levels
   * Bug 21270: NoScript settings break WebExtensions add-ons
   * Bug 23104: CSS line-height reveals the platform Tor Browser is running on
 * Windows
   * Bug 16010: Enable content sandboxing on Windows
   * Bug 23582: Enable the Windows DLL blocklist for mingw-w64 builds
   * Bug 23396: Update the msvcr100.dll we ship
   * Bug 23230: Fix build error on Windows 64
 * OS X
   * Bug 23404: Add missing Noto Sans Buginese font to the macOS whitelist
 * Linux
   * Bug 10089: Set middlemouse.contentLoadURL to false by default
   * Bug 22692: Enable content sandboxing on Linux
   * Bug 18101: Suppress upload file dialog proxy bypass (linux part)
 * Build System
   * All Platforms
     * Switch from gitian/tor-browser-bundle to rbm/tor-browser-build

Tor Browser 7.0.6 -- September 28 2017
 * All Platforms
   * Update Firefox to 52.4.0esr
   * Update Tor to *******
   * Update Torbutton to *******
     * Bug 22542: Security Settings window too small on macOS 10.12 (fixup)
     * Bug 20375: Warn users after entering fullscreen mode
   * Update HTTPS-Everywhere to 2017.9.12
   * Update NoScript to 5.0.10
   * Bug 21830: Copying large text from web console leaks to /tmp
   * Bug 23393: Don't crash all tabs when closing one tab
 * OS X
   * Bug 23404: Add missing Noto Sans Buginese font to the macOS whitelist

Tor Browser 7.0.5 -- September 4 2017
 * All Platforms
   * Update Torbutton to *******
     * Bug 22989: Fix dimensions of new windows on macOS
     * Translations update
   * Update HTTPS-Everywhere to 2017.8.31
   * Update NoScript to 5.0.9
   * Bug 23166: Add new obfs4 bridge to the built-in ones
   * Bug 23258: Fix broken HTTPS-Everywhere on higher security levels
   * Bug 21270: NoScript settings break WebExtensions add-ons

Tor Browser 7.5a4 -- August 9 2017
 * All Platforms
   * Update Firefox to 52.3.0esr
   * Update Tor to *******-alpha
   * Update OpenSSL to 1.0.2l
   * Update Torbutton to 1.9.8
     * Bug 22610: Avoid crashes when canceling external helper app related downloads
     * Bug 22472: Fix FTP downloads when external helper app dialog is shown
     * Bug 22471: Downloading pdf files via the PDF viewer download button is broken
     * Bug 22618: Downloading pdf file via file:/// is stalling
     * Bug 22542: Resize slider window to work without scrollbars
     * Bug 21999: Fix display of language prompt in non-en-US locales
     * Bug 18913: Don't let about:tor have chrome privileges
     * Bug 22535: Search on about:tor discards search query
     * Bug 21948: Going back to about:tor page gives "Address isn't valid" error
     * Code clean-up
     * Translations update
   * Update Tor Launcher to ********
     * Bug 22592: Default bridge settings are not removed
     * Translations update
   * Update HTTPS-Everywhere to 5.2.21
   * Update NoScript to *******
     * Bug 22362: Remove workaround for XSS related browser freezing
     * Bug 22067: NoScript Click-to-Play bypass with embedded videos and audio
   * Update sandboxed-tor-browser to 0.0.12
   * Bug 22610: Avoid crashes when canceling external helper app related downloads
   * Bug 22472: Fix FTP downloads when external helper app dialog is shown
   * Bug 22471: Downloading pdf files via the PDF viewer download button is broken
   * Bug 22618: Downloading pdf file via file:/// is stalling
   * Bug 21321: Exempt .onions from HTTP related security warnings
   * Bug 21830: Copying large text from web console leaks to /tmp
   * Bug 22073: Disable GetAddons option on addons page
   * Bug 22884: Fix broken about:tor page on higher security levels
   * Bug 22829: Remove default obfs4 bridge riemann.
 * Windows
   * Bug 21617: Fix single RWX page on Windows (included in 52.3.0esr)
 * OS X
   * Bug 22831: Enable Snowflake for mac
 * Linux
   * Bug 22832: Don't include monthly timestamp in libwebrtc build output
   * Bug 20848: Deploy Selfrando in 32bit Linux builds
 * Build system
   * Windows
     * Bug 22563: Update mingw-w64 to fix W^X violations
     * Bug 20929: Bump GCC version to 5.4.0
   * Linux
     * Bug 20929: Bump GCC version to 5.4.0

Tor Browser 7.0.4 -- August 8 2017
 * All Platforms
   * Update Firefox to 52.3.0esr
   * Update Tor to ********
   * Update Torbutton to *******
     * Bug 21999: Fix display of language prompt in non-en-US locales
     * Bug 18913: Don't let about:tor have chrome privileges
     * Bug 22535: Search on about:tor discards search query
     * Bug 21948: Going back to about:tor page gives "Address isn't valid" error
     * Code clean-up
     * Translations update
   * Update Tor Launcher to ********
     * Bug 22592: Default bridge settings are not removed
     * Translations update
   * Update HTTPS-Everywhere to 5.2.21
   * Update NoScript to *******
     * Bug 22362: Remove workaround for XSS related browser freezing
     * Bug 22067: NoScript Click-to-Play bypass with embedded videos and audio
   * Bug 21321: Exempt .onions from HTTP related security warnings
   * Bug 22073: Disable GetAddons option on addons page
   * Bug 22884: Fix broken about:tor page on higher security levels
 * Windows
   * Bug 22829: Remove default obfs4 bridge riemann.
   * Bug 21617: Fix single RWX page on Windows (included in 52.3.0esr)
 * OS X
   * Bug 22829: Remove default obfs4 bridge riemann.

Tor Browser 7.5a3 -- July 28 2017
 * Linux
   * Bug 23044: Don't allow GIO supported protocols by default

Tor Browser 7.0.3 -- July 27 2017
 * Linux
   * Bug 23044: Don't allow GIO supported protocols by default
   * Bug 22829: Remove default obfs4 bridge riemann.

Tor Browser 7.5a2 -- July 6 2017
 * All Platforms
   * Update Tor to *******-alpha
   * Update HTTPS-Everywhere to 5.2.19
 * Linux
   * Update sandboxed-tor-browser to 0.0.9

Tor Browser 7.0.2 -- July 3 2017
 * All Platforms
   * Update Tor to *******, fixing bug #22753
   * Update HTTPS-Everywhere to 5.2.19

Tor Browser 7.5a1 -- June 14 2017
 * All Platforms
   * Update Firefox to 52.2.0esr
   * Update Tor to 0.3.1.3-alpha
   * Update Torbutton to *******
     * Bug 22542: Security Settings window too small on macOS 10.12
     * Bug 22104: Adjust our content policy whitelist for ff52-esr
     * Bug 22457: Allow resources loaded by view-source://
     * Bug 21627: Ignore HTTP 304 responses when checking redirects
     * Bug 22459: Adapt our use of the nsIContentPolicy to e10s mode
     * Translations update
   * Update Tor Launcher to ********
     * Bug 22283: Linux 7.0a4 is broken after update due to unix: lines in torrc
     * Translations update
   * Update HTTPS-Everywhere to 5.2.18
   * Update NoScript to 5.0.5
   * Update sandboxed-tor-browser to 0.0.7
   * Bug 22362: NoScript's XSS filter freezes the browser
   * Bug 21766: Fix crash when the external application helper dialog is invoked
   * Bug 21886: Download is stalled in non-e10s mode
   * Bug 22333: Disable WebGL2 API for now
   * Bug 21861: Disable additional mDNS code to avoid proxy bypasses
   * Bug 21684: Don't expose navigator.AddonManager to content
   * Bug 21431: Clean-up system extensions shipped in Firefox 52
   * Bug 22320: Use preference name 'referer.hideOnionSource' everywhere
   * Bug 16285: Don't ship ClearKey EME system and update EME preferences
   * Bug 21972: about:support is partially broken
   * Bug 21323: Enable Mixed Content Blocking
   * Bug 22415: Fix format error in our pipeline patch
   * Bug 21862: Rip out potentially unsafe Rust code
   * Bug 16485: Improve about:cache page
   * Bug 22462: Backport of patch for bug 1329521 to fix assertion failure
   * Bug 22458: Fix broken `about:cache` page on higher security levels
   * Bug 18531: Uncaught exception when opening ip-check.info
   * Bug 18574: Uncaught exception when clicking items in Library
   * Bug 22327: Isolate Page Info media previews to first party domain
   * Bug 22452: Isolate tab list menuitem favicons to first party domain
   * Bug 15555: View-source requests are not isolated by first party domain
   * Bug 5293: Neuter fingerprinting with Battery API
   * Bug 22429: Add IPv6 address for Lisbeth:443 obfs4 bridge
   * Bug 22468: Add default obfs4 bridges frosty and dragon
 * Windows
   * Bug 22419: Prevent access to file://
   * Bug 21617: Fix single RWX page on Windows
 * OS X
   * Bug 22558: Don't update OS X 10.7.x and 10.8.x users to Tor Browser 7.0

 * Linux
   * Bug 16285: Remove ClearKey related library stripping
   * Bug 21852: Don't use jemalloc4 anymore
 * Android
   * Bug 19078: Disable RtspMediaResource stuff in Orfox

Tor Browser 7.0.1 -- June 13 2017
 * All Platforms
   * Update Firefox to 52.2.0esr
   * Update Tor to *******
   * Update Torbutton to *******
     * Bug 22542: Security Settings window too small on macOS 10.12
   * Update HTTPS-Everywhere to 5.2.18
   * Bug 22362: NoScript's XSS filter freezes the browser
 * OS X
   * Bug 22558: Don't update OS X 10.7.x and 10.8.x users to Tor Browser 7.0

Tor Browser 7.0 -- June 7 2017
 * All Platforms
   * Update Firefox to 52.1.2esr
   * Update Tor to *******
   * Update Torbutton to *******
     * Bug 22104: Adjust our content policy whitelist for ff52-esr
     * Bug 22457: Allow resources loaded by view-source://
     * Bug 21627: Ignore HTTP 304 responses when checking redirects
     * Bug 22459: Adapt our use of the nsIContentPolicy to e10s mode
     * Bug 21865: Update our JIT preferences in the security slider
     * Bug 21747: Make 'New Tor Circuit for this Site' work in ESR52
     * Bug 21745: Fix handling of catch-all circuit
     * Bug 21547: Fix circuit display under e10s
     * Bug 21268: e10s compatibility for New Identity
     * Bug 21267: Remove window resize implementation for now
     * Bug 21201: Make Torbutton multiprocess compatible
     * Translations update
   * Update Tor Launcher to ********
     * Bug 22283: Linux 7.0a4 broken after update due to unix: lines in torrc
     * Bug 20761: Don't ignore additional SocksPorts
     * Bug 21920: Don't show locale selection dialog
     * Bug 21546: Mark Tor Launcher as multiprocess compatible
     * Bug 21264: Add a README file
     * Translations update
   * Update HTTPS-Everywhere to 5.2.17
   * Update NoScript to 5.0.5
   * Update Go to 1.8.3 (bug 22398)
   * Bug 21962: Fix crash on about:addons page
   * Bug 21766: Fix crash when the external application helper dialog is invoked
   * Bug 21886: Download is stalled in non-e10s mode
   * Bug 21778: Canvas prompt is not shown in Tor Browser based on ESR52
   * Bug 21569: Add first-party domain to Permissions key
   * Bug 22165: Don't allow collection of local IP addresses
   * Bug 13017: Work around audio fingerprinting by disabling the Web Audio API
   * Bug 10286: Disable Touch API and add fingerprinting resistance as fallback
   * Bug 13612: Disable Social API
   * Bug 10283: Disable SpeechSynthesis API
   * Bug 22333: Disable WebGL2 API for now
   * Bug 21861: Disable additional mDNS code to avoid proxy bypasses
   * Bug 21684: Don't expose navigator.AddonManager to content
   * Bug 21431: Clean-up system extensions shipped in Firefox 52
   * Bug 22320: Use preference name 'referer.hideOnionSource' everywhere
   * Bug 16285: Don't ship ClearKey EME system and update EME preferences
   * Bug 21675: Spoof window.navigator.hardwareConcurrency
   * Bug 21792: Suppress MediaError.message
   * Bug 16337: Round times exposed by Animation API to nearest 100ms
   * Bug 21972: about:support is partially broken
   * Bug 21726: Keep Graphite support disabled
   * Bug 21323: Enable Mixed Content Blocking
   * Bug 21685: Disable remote new tab pages
   * Bug 21790: Disable captive portal detection
   * Bug 21686: Disable Microsoft Family Safety support
   * Bug 22073: Make sure Mozilla's experiments are disabled
   * Bug 21683: Disable newly added Safebrowsing capabilities
   * Bug 22071: Disable Kinto-based blocklist update mechanism
   * Bug 22415: Fix format error in our pipeline patch
   * Bug 22072: Hide TLS error reporting checkbox
   * Bug 20761: Don't ignore additional SocksPorts
   * Bug 21862: Rip out potentially unsafe Rust code
   * Bug 16485: Improve about:cache page
   * Bug 22462: Backport of patch for bug 1329521 to fix assertion failure
   * Bug 21340: Identify and backport new patches from Firefox
   * Bug 22153: Fix broken feeds on higher security levels
   * Bug 22025: Fix broken certificate error pages on higher security levels
   * Bug 21887: Fix broken error pages on higher security levels
   * Bug 22458: Fix broken `about:cache` page on higher security levels
   * Bug 21876: Enable e10s by default on all supported platforms
   * Bug 21876: Always use esr policies for e10s
   * Bug 20905: Fix resizing issues after moving to a direct Firefox patch
   * Bug 21875: Modal dialogs are maximized in ESR52 nightly builds
   * Bug 21885: SVG is not disabled in Tor Browser based on ESR52
   * Bug 17334: Hide Referer when leaving a .onion domain (improved patch)
   * Bug 18531: Uncaught exception when opening ip-check.info
   * Bug 18574: Uncaught exception when clicking items in Library
   * Bug 22327: Isolate Page Info media previews to first party domain
   * Bug 22452: Isolate tab list menuitem favicons to first party domain
   * Bug 15555: View-source requests are not isolated by first party domain
   * Bug 3246: Double-key cookies
   * Bug 8842: Fix XML parsing error
   * Bug 5293: Neuter fingerprinting with Battery API
   * Bug 16886: 16886: "Add-on compatibility check dialog" contains Firefox logo
   * Bug 19645: TBB zooms text when resizing browser window
   * Bug 19192: Untrust Blue Coat CA
   * Bug 19955: Avoid confusing warning that favicon load request got cancelled
   * Bug 20005: Backport fixes for memory leaks investigation
   * Bug 20755: ltn.com.tw is broken in Tor Browser
   * Bug 21896: Commenting on website is broken due to CAPTCHA not being displayed
   * Bug 20680: Rebase Tor Browser patches to 52 ESR
   * Bug 22429: Add IPv6 address for Lisbeth:443 obfs4 bridge
   * Bug 22468: Add default obfs4 bridges frosty and dragon
 * Windows
   * Bug 22419: Prevent access to file://
   * Bug 12426: Make use of HeapEnableTerminationOnCorruption
   * Bug 19316: Make sure our Windows updates can deal with the SSE2 requirement
   * Bug 21868: Fix build bustage with FIREFOX_52_0_2esr_RELEASE for Windows
 * OS X
   * Bug 21940: Don't allow privilege escalation during update
   * Bug 22044: Fix broken default search engine on macOS
   * Bug 21879: Use our default bookmarks on OSX
   * Bug 21779: Non-admin users can't access Tor Browser on macOS
   * Bug 21723: Fix inconsistent generation of MOZ_MACBUNDLE_ID
   * Bug 21724: Make Firefox and Tor Browser distinct macOS apps
   * Bug 21931: Backport OSX SetupMacCommandLine updater fixes
   * Bug 15910: Don't download GMPs via the local fallback
 * Linux
   * Bug 16285: Remove ClearKey related library stripping
   * Bug 22041: Fix update error during update to 7.0a3
   * Bug 22238: Fix use of hardened wrapper for Firefox build
   * Bug 21907: Fix runtime error on CentOS 6
   * Bug 15910: Don't download GMPs via the local fallback
 * Android
   * Bug 19078: Disable RtspMediaResource stuff in Orfox
 * Build system
   * Windows
     * Bug 21837: Fix reproducibility of accessibility code for Windows
     * Bug 21240: Create patches to fix mingw-w64 compilation of Firefox ESR 52
     * Bug 21904: Bump mingw-w64 commit to help with sandbox compilation
     * Bug 18831: Use own Yasm for Firefox cross-compilation
   * OS X
     * Bug 21328: Updating to clang 3.8.0
     * Bug 21754: Remove old GCC toolchain and macOS SDK
     * Bug 19783: Remove unused macOS helper scripts
     * Bug 10369: Don't use old GCC toolchain anymore for utils
     * Bug 21753: Replace our old GCC toolchain in PT descriptor
     * Bug 18530: ESR52 based Tor Browser only runs on macOS 10.9+
     * Bug 22328: Remove clang PIE wrappers
   * Linux
     * Bug 21930: NSS libraries are missing from mar-tools archive
     * Bug 21239: Adapt Linux Firefox descriptor to ESR52 (use GTK2)
     * Bug 21960: Linux bundles based on ESR 52 are not reproducible anymore
     * Bug 21629: Fix broken ASan builds when switching to ESR 52
     * Bug 22444: Use hardening-wrapper when building GCC
     * Bug 22361: Fix hardening of libraries built in linux/gitian-utils.yml

Tor Browser 7.0a4 -- May 15 2017
 * All Platforms
   * Update Firefox to 52.1.1esr
   * Update Tor to *******
   * Update Tor Launcher to ********
     * Bug 20761: Don't ignore additional SocksPorts
     * Translation update
   * Update HTTPS-Everywhere to 5.2.16
   * Update NoScript to 5.0.4
   * Bug 21962: Fix crash on about:addons page
   * Bug 21778: Canvas prompt is not shown in Tor Browser based on ESR52
   * Bug 21569: Add first-party domain to Permissions key
   * Bug 22165: Don't allow collection of local IP addresses
   * Bug 13017: Work around audio fingerprinting by disabling the Web Audio API
   * Bug 10286: Disable Touch API and add fingerprinting resistance as fallback
   * Bug 13612: Disable Social API
   * Bug 10283: Disable SpeechSynthesis API
   * Bug 21675: Spoof window.navigator.hardwareConcurrency
   * Bug 21792: Suppress MediaError.message
   * Bug 16337: Round times exposed by Animation API to nearest 100ms
   * Bug 21726: Keep Graphite support disabled
   * Bug 21685: Disable remote new tab pages
   * Bug 21790: Disable captive portal detection
   * Bug 21686: Disable Microsoft Family Safety support
   * Bug 22073: Make sure Mozilla's experiments are disabled
   * Bug 21683: Disable newly added Safebrowsing capabilities
   * Bug 22071: Disable Kinto-based blocklist update mechanism
   * Bug 22072: Hide TLS error reporting checkbox
   * Bug 20761: Don't ignore additional SocksPorts
   * Bug 21340: Identify and backport new patches from Firefox
   * Bug 22153: Fix broken feeds on higher security levels
   * Bug 22025: Fix broken certificate error pages on higher security levels
   * Bug 21710: Upgrade Go to 1.8.1
 * Mac
   * Bug 21940: Don't allow privilege escalation during update
   * Bug 22044: Fix broken default search engine on macOS
   * Bug 21879: Use our default bookmarks on OSX
   * Bug 21779: Non-admin users can't access Tor Browser on macOS
 * Linux
   * Bug 22041: Fix update error during update to 7.0a3
   * Bug 22238: Fix use of hardened wrapper for Firefox build
   * Bug 20683: Selfrando support for 64-bit Linux systems

Tor Browser 7.0a3 -- April 20 2017
 * All Platforms
   * Update Firefox to 52.1.0esr
   * Tor to *******-rc
   * Update Torbutton to *******
     * Bug 21865: Update our JIT preferences in the security slider
     * Bug 21747: Make 'New Tor Circuit for this Site' work in ESR52
     * Bug 21745: Fix handling of catch-all circuit
     * Bug 21547: Fix circuit display under e10s
     * Bug 21268: e10s compatibility for New Identity
     * Bug 21267: Remove window resize implementation for now
     * Bug 21201: Make Torbutton multiprocess compatible
     * Translations update
   * Update Tor Launcher to 0.2.12
     * Bug 21920: Don't show locale selection dialog
     * Bug 21546: Mark Tor Launcher as multiprocess compatible
     * Bug 21264: Add a README file
     * Translations update
   * Update HTTPS-Everywhere to 5.2.14
   * Update NoScript to 5.0.2
   * Update sandboxed-tor-browser to 0.0.6
     * Bug 21764: Use bubblewrap's `--die-with-parent` when supported
     * Fix e10s Web Content crash on systems with grsec kernels
     * Bug 21928: Force a reinstall if an existing hardened bundle is present
     * Bug 21929: Remove hardened/ASAN related code
     * Bug 21927: Remove the ability to install/update the hardened bundle
     * Bug 21244: Update the MAR signing key for 7.0
     * Bug 21536: Remove asn's scramblesuit bridge from Tor Browser
     * Add back the old release MAR signing key
     * Add `prlimit64` to the firefox system call whitelist
     * Fix compilation with Go 1.8
     * Use Config.Clone() to clone TLS configs when available
   * Update Go to 1.7.5 (bug 21709)
   * Bug 21555+16450: Don't remove Authorization header on subdomains (e.g. Twitter)
   * Bug 21887: Fix broken error pages on higher security levels
   * Bug 21876: Enable e10s by default on all supported platforms
   * Bug 21876: Always use esr policies for e10s
   * Bug 20905: Fix resizing issues after moving to a direct Firefox patch
   * Bug 21875: Modal dialogs are maximized in ESR52 nightly builds
   * Bug 21885: SVG is not disabled in Tor Browser based on ESR52
   * Bug 17334: Hide Referer when leaving a .onion domain (improved patch)
   * Bug 3246: Double-key cookies
   * Bug 8842: Fix XML parsing error
   * Bug 16886: "Add-on compatibility check dialog" contains Firefox logo
   * Bug 19192: Untrust Blue Coat CA
   * Bug 19955: Avoid confusing warning that favicon load request got cancelled
   * Bug 20005: Backport fixes for memory leaks investigation
   * Bug 20755: ltn.com.tw is broken in Tor Browser
   * Bug 21896: Commenting on website is broken due to CAPTCHA not being displayed
   * Bug 20680: Rebase Tor Browser patches to 52 ESR
   * Bug 21917: Add new obfs4 bridges
   * Bug 21918: Move meek-amazon to d2cly7j4zqgua7.cloudfront.net backend
 * Windows
   * Bug 21795: Fix Tor Browser crashing on github.com
   * Bug 12426: Make use of HeapEnableTerminationOnCorruption
   * Bug 19316: Make sure our Windows updates can deal with the SSE2 requirement
   * Bug 21868: Fix build bustage with FIREFOX_52_0_2esr_RELEASE for Windows
 * OS X
   * Bug 21723: Fix inconsistent generation of MOZ_MACBUNDLE_ID
   * Bug 21724: Make Firefox and Tor Browser distinct macOS apps
   * Bug 21931: Backport OSX SetupMacCommandLine updater fixes
   * Bug 15910: Don't download GMPs via the local fallback
 * Linux
   * Bug 21907: Fix runtime error on CentOS 6
   * Bug 21748: Fix broken Snowflake build and update bridge details
   * Bug 21954: Snowflake breaks the 7.0a3 build
   * Bug 15910: Don't download GMPs via the local fallback
 * Build system
   * Windows
     * Bug 21837: Fix reproducibility of accessibility code for Windows
     * Bug 21240: Create patches to fix mingw-w64 compilation of Firefox ESR 52
     * Bug 21904: Bump mingw-w64 commit to help with sandbox compilation
     * Bug 18831: Use own Yasm for Firefox cross-compilation
   * OS X
     * Bug 21328: Updating to clang 3.8.0
     * Bug 21754: Remove old GCC toolchain and macOS SDK
     * Bug 19783: Remove unused macOS helper scripts
     * Bug 10369: Don't use old GCC toolchain anymore for utils
     * Bug 21753: Replace our old GCC toolchain in PT descriptor
     * Bug 18530: ESR52 based Tor Browser only runs on macOS 10.9+
   * Linux
     * Bug 21930: NSS libraries are missing from mar-tools archive
     * Bug 21239: Adapt Linux Firefox descriptor to ESR52 (use GTK2)
     * Bug 21960: Linux bundles based on ESR 52 are not reproducible anymore
     * Bug 21629: Fix broken ASan builds when switching to ESR 52

Tor Browser 6.5.2 -- April 19 2017
 * All Platforms
   * Update Firefox to 45.9.0esr
   * Update HTTPS-Everywhere to 5.2.14
   * Update NoScript to 5.0.2
   * Bug 21555+16450: Don't remove Authorization header on subdomains (e.g. Twitter)
   * Bug 19316: Make sure our Windows updates can deal with the SSE2 requirement
   * Bug 21917: Add new obfs4 bridges
   * Bug 21918: Move meek-amazon to d2cly7j4zqgua7.cloudfront.net backend
 * Windows
   * Bug 21795: Fix Tor Browser crashing on github.com

Tor Browser 7.0a2-hardened -- March 7 2017
 * All Platforms
   * Update Firefox to 45.8.0esr
   * Tor to *******-rc
   * OpenSSL to 1.0.2k
   * Update Torbutton to *******
     * Bug 21396: Allow leaking of resource/chrome URIs (off by default)
     * Bug 21574: Add link for zh manual and create manual links dynamically
     * Bug 21330: Non-usable scrollbar appears in tor browser security settings
     * Bug 21324: Don't update NoScript button with timer update
     * Translation updates
   * Update HTTPS-Everywhere to 5.2.11
   * Bug 21514: Restore W^X JIT implementation removed from ESR45
   * Bug 21536: Remove scramblesuit bridge
   * Bug 21342: Move meek-azure to the meek.azureedge.net backend and cymrubridge02 bridge
   * Bug 21326: Update the "Using a system-installed Tor" section in start script
 * Build system
   * Bug 17034: Use our built binutils and GCC for building tor
   * Code clean-up

Tor Browser 7.0a2 -- March 7 2017
 * All Platforms
   * Update Firefox to 45.8.0esr
   * Tor to *******-rc
   * OpenSSL to 1.0.2k
   * Update Torbutton to *******
     * Bug 21396: Allow leaking of resource/chrome URIs (off by default)
     * Bug 21574: Add link for zh manual and create manual links dynamically
     * Bug 21330: Non-usable scrollbar appears in tor browser security settings
     * Bug 21324: Don't update NoScript button with timer update
     * Translation updates
   * Update HTTPS-Everywhere to 5.2.11
   * Bug 21514: Restore W^X JIT implementation removed from ESR45
   * Bug 21536: Remove scramblesuit bridge
   * Bug 21342: Move meek-azure to the meek.azureedge.net backend and cymrubridge02 bridge
   * Bug 21348: Make snowflake only available on Linux for now
 * Linux
   * Bug 21326: Update the "Using a system-installed Tor" section in start script
 * Build system
   * OS X
     * Bug 21343: Remove unused FTE related parts for macOS
   * Linux
     * Bug 17034: Use our built binutils and GCC for building tor
     * Clean-up

Tor Browser 6.5.1 -- March 7 2017
 * All Platforms
   * Update Firefox to 45.8.0esr
   * Tor to *******0
   * OpenSSL to 1.0.2k
   * Update Torbutton to *******4
     * Bug 21396: Allow leaking of resource/chrome URIs (off by default)
     * Bug 21574: Add link for zh manual and create manual links dynamically
     * Bug 21330: Non-usable scrollbar appears in tor browser security settings
     * Translation updates
   * Update HTTPS-Everywhere to 5.2.11
   * Bug 21514: Restore W^X JIT implementation removed from ESR45
   * Bug 21536: Remove scramblesuit bridge
   * Bug 21342: Move meek-azure to the meek.azureedge.net backend and cymrubridge02 bridge
 * Linux
   * Bug 21326: Update the "Using a system-installed Tor" section in start script

Tor Browser 7.0a1-hardened -- January 25 2017
 * All Platforms
   * Update Firefox to 45.7.0esr
   * Tor to *******-alpha
   * Update Torbutton to 1.9.7
     * Bug 19898: Use DuckDuckGo on about:tor
     * Bug 21091: Hide the update check menu entry when running under the sandbox
     * Bug 21243: Add links to es, fr, and pt Tor Browser manual
     * Bug 21194: Show snowflake in the circuit display
     * Bug 21131: Remove 2016 donation banner
     * Translation updates
   * Update HTTPS-Everywhere to 5.2.9
   * Update NoScript to *******
   * Bug 20471: Allow javascript: links from HTTPS first party pages
   * Bug 20651: DuckDuckGo does not work with JavaScript disabled
   * Bug 20589: Add new MAR signing key
   * Bug 20735: Add snowflake pluggable transport to alpha Linux builds
 * Build system
   * All platforms
     * Bug 20927: Upgrade Go to 1.7.4

Tor Browser 7.0a1 -- January 25 2017
 * All Platforms
   * Update Firefox to 45.7.0esr
   * Tor to *******-alpha
   * Update Torbutton to 1.9.7
     * Bug 19898: Use DuckDuckGo on about:tor
     * Bug 21091: Hide the update check menu entry when running under the sandbox
     * Bug 21243: Add links to es, fr, and pt Tor Browser manual
     * Bug 21194: Show snowflake in the circuit display
     * Bug 21131: Remove 2016 donation banner
     * Translation updates
   * Update HTTPS-Everywhere to 5.2.9
   * Update NoScript to *******
   * Bug 20471: Allow javascript: links from HTTPS first party pages
   * Bug 20651: DuckDuckGo does not work with JavaScript disabled
   * Bug 20589: Add new MAR signing key
 * Windows
   * Bug 20981: On Windows, check TZ for timezone first
 * OS X
   * Bug 20989: Browser sandbox profile is too restrictive on OSX 10.12.2
 * Linux
   * Update sandboxed-tor-browser to 0.0.3
   * Bug 20735: Add snowflake pluggable transport to alpha Linux builds
 * Build system
   * All platforms
     * Bug 20927: Upgrade Go to 1.7.4
   * Linux
     * Bug 21103: Update descriptors for sandboxed-tor-browser 0.0.3

Tor Browser 6.5 -- January 24 2017
 * All Platforms
   * Update Firefox to 45.7.0esr
   * Tor to *******
   * OpenSSL to 1.0.2j
   * Update Torbutton to *******2
     * Bug 16622: Timezone spoofing moved to tor-browser.git
     * Bug 17334: Move referrer spoofing for .onion domains into tor-browser.git
     * Bug 8725: Block addon resource and url fingerprinting with nsIContentPolicy
     * Bug 20701: Allow the directory listing stylesheet in the content policy
     * Bug 19837: Whitelist internal URLs that Firefox requires for media
     * Bug 19206: Avoid SOCKS auth and NEWNYM collisions when sharing a tor client
     * Bug 19273: Improve external app launch handling and associated warnings
     * Bug 15852: Remove/synchronize Torbutton SOCKS pref logic
     * Bug 19733: GETINFO response parser doesn't handle AF_UNIX entries + IPv6
     * Bug 17767: Make "JavaScript disabled" more visible in Security Slider
     * Bug 20556: Use pt-BR strings from now on
     * Bug 20614: Add links to Tor Browser User Manual
     * Bug 20414: Fix non-rendering arrow on OS X
     * Bug 20728: Fix bad preferences.xul dimensions
     * Bug 19898: Use DuckDuckGo on about:tor
     * Bug 21091: Hide the update check menu entry when running under the sandbox
     * Bug 19459: Move resizing code to tor-browser.git
     * Bug 20264: Change security slider to 3 options
     * Bug 20347: Enhance security slider's custom mode
     * Bug 20123: Disable remote jar on all security levels
     * Bug 20244: Move privacy checkboxes to about:preferences#privacy
     * Bug 17546: Add tooltips to explain our privacy checkboxes
     * Bug 17904: Allow security settings dialog to resize
     * Bug 18093: Remove 'Restore Defaults' button
     * Bug 20373: Prevent redundant dialogs opening
     * Bug 20318: Remove helpdesk link from about:tor
     * Bug 21243: Add links for pt, es, and fr Tor Browser manuals
     * Bug 20753: Remove obsolete StartPage locale strings
     * Bug 21131: Remove 2016 donation banner
     * Bug 18980: Remove obsolete toolbar button code
     * Bug 18238: Remove unused Torbutton code and strings
     * Bug 20388+20399+20394: Code clean-up
     * Translation updates
   * Update Tor Launcher to ********
     * Bug 19568: Set CurProcD for Thunderbird/Instantbird
     * Bug 19432: Remove special handling for Instantbird/Thunderbird
     * Translation updates
   * Update HTTPS-Everywhere to 5.2.9
   * Update NoScript to *******
   * Bug 16622: Spoof timezone with Firefox patch
   * Bug 17334: Spoof referrer when leaving a .onion domain
   * Bug 19273: Write C++ patch for external app launch handling
   * Bug 19459: Size new windows to 1000x1000 or nearest 200x100 (Firefox patch)
   * Bug 12523: Mark JIT pages as non-writable
   * Bug 20123: Always block remote jar files
   * Bug 19193: Reduce timing precision for AudioContext, HTMLMediaElement, and MediaStream
   * Bug 19164: Remove support for SHA-1 HPKP pins
   * Bug 19186: KeyboardEvents are only rounding to 100ms
   * Bug 16998: Isolate preconnect requests to URL bar domain
   * Bug 19478: Prevent millisecond resolution leaks in File API
   * Bug 20471: Allow javascript: links from HTTPS first party pages
   * Bug 20244: Move privacy checkboxes to about:preferences#privacy
   * Bug 20707: Fix broken preferences tab in non-en-US alpha bundles
   * Bug 20709: Fix wrong update URL in alpha bundles
   * Bug 19481: Point the update URL to aus1.torproject.org
   * Bug 20556: Start using pt-BR instead of pt-PT for Portuguese
   * Bug 20442: Backport fix for local path disclosure after drag and drop
   * Bug 20160: Backport fix for broken MP3-playback
   * Bug 20043: Isolate SharedWorker script requests to first party
   * Bug 18923: Add script to run all Tor Browser regression tests
   * Bug 20651: DuckDuckGo does not work with JavaScript disabled
   * Bug 19336+19835: Enhance about:tbupdate page
   * Bug 20399+15852: Code clean-up
 * Windows
   * Bug 20981: On Windows, check TZ for timezone first
   * Bug 18175: Maximizing window and restarting leads to non-rounded window size
   * Bug 13437: Rounded inner window accidentally grows to non-rounded size
 * OS X
   * Bug 20590: Badly resized window due to security slider notification bar on OS X
   * Bug 20439: Make the build PIE on OSX
 * Linux
   * Bug 20691: Updater breaks if unix domain sockets are used
   * Bug 15953: Weird resizing dance on Tor Browser startup
 * Build system
   * All platforms
     * Bug 20927: Upgrade Go to 1.7.4
     * Bug 20583: Make the downloads.json file reproducible
     * Bug 20133: Don't apply OpenSSL patch anymore
     * Bug 19528: Set MOZ_BUILD_DATE based on Firefox version
     * Bug 18291: Remove some uses of libfaketime
     * Bug 18845: Make zip and tar helpers generate reproducible archives
   * OS X
     * Bug 20258: Make OS X Tor archive reproducible again
     * Bug 20184: Make OS X builds reproducible (use clang for compiling tor)
     * Bug 19856: Make OS X builds reproducible (getting libfaketime back)
     * Bug 19410: Fix incremental updates by taking signatures into account
     * Bug 20210: In dmg2mar, extract old mar file to copy permissions to the new one

Tor Browser 6.5a6-hardened -- December 14 2016
 * All Platforms
   * Update Firefox to 45.6.0esr
   * Tor to *******-rc
   * Update Torbutton to *******
     * Bug 16622: Timezone spoofing moved to tor-browser.git
     * Bug 20701: Allow the directory listing stylesheet in the content policy
     * Bug 20556: Use pt-BR strings from now on
     * Bug 20614: Add links to Tor Browser User Manual
     * Bug 20414: Fix non-rendering arrow on OS X
     * Bug 20728: Fix bad preferences.xul dimensions
     * Bug 20318: Remove helpdesk link from about:tor
     * Bug 20753: Remove obsolete StartPage locale strings
     * Bug 20947: Donation banner improvements
     * Translation updates
   * Update HTTPS-Everywhere to 5.2.8
   * Bug 16622: Spoof timezone with Firefox patch
   * Bug 20707: Fix broken preferences tab in non-en-US alpha bundles
   * Bug 20709: Fix wrong update URL in alpha bundles
   * Bug 20556: Start using pt-BR instead of pt-PT for Portuguese
   * Bug 20809: Use non-/html search engine URL for DuckDuckGo search plugins
   * Bug 20837: Activate iat-mode for certain obfs4 bridges
   * Bug 20838: Uncomment NX01 default obfs4 bridge
   * Bug 20840: Rotate ports a third time for default obfs4 bridges

Tor Browser 6.5a6 -- December 14 2016
 * All Platforms
   * Update Firefox to 45.6.0esr
   * Tor to 0.2.9.6-rc
   * Update Torbutton to 1.9.6.8
     * Bug 16622: Timezone spoofing moved to tor-browser.git
     * Bug 20701: Allow the directory listing stylesheet in the content policy
     * Bug 20556: Use pt-BR strings from now on
     * Bug 20614: Add links to Tor Browser User Manual
     * Bug 20414: Fix non-rendering arrow on OS X
     * Bug 20728: Fix bad preferences.xul dimensions
     * Bug 20318: Remove helpdesk link from about:tor
     * Bug 20753: Remove obsolete StartPage locale strings
     * Translation updates
   * Update HTTPS-Everywhere to 5.2.8
   * Bug 16622: Spoof timezone with Firefox patch
   * Bug 20707: Fix broken preferences tab in non-en-US alpha bundles
   * Bug 20709: Fix wrong update URL in alpha bundles
   * Bug 20556: Start using pt-BR instead of pt-PT for Portuguese
   * Bug 20809: Use non-/html search engine URL for DuckDuckGo search plugins
   * Bug 20837: Activate iat-mode for certain obfs4 bridges
   * Bug 20838: Uncomment NX01 default obfs4 bridge
   * Bug 20840: Rotate ports a third time for default obfs4 bridges
 * Linux
   * Bug 20352: Integrate sandboxed-tor-browser into our Gitian build
   * Bug 20758: Make Linux sandbox build deterministic
   * Bug 10281: Use jemalloc4 and abort on redzone corruption
 * OS X
   * Bug 20121: Create Seatbelt profile(s) for Tor Browser

Tor Browser 6.0.8 -- December 13 2016
 * All Platforms
   * Update Firefox to 45.6.0esr
   * Tor to *******1
   * Update Torbutton to *******3
     * Bug 20947: Donation banner improvements
   * Update HTTPS-Everywhere to 5.2.8
   * Bug 20809: Use non-/html search engine URL for DuckDuckGo search plugins
   * Bug 20837: Activate iat-mode for certain obfs4 bridges
   * Bug 20838: Uncomment NX01 default obfs4 bridge
   * Bug 20840: Rotate ports a third time for default obfs4 bridges

Tor Browser 6.5a5-hardened -- December 1 2016
 * All Platforms
   * Update Firefox to 45.5.1esr
   * Update NoScript to *******
 * Linux
   * Bug 20691: Updater breaks if unix domain sockets are used

Tor Browser 6.5a5 -- December 1 2016
 * All Platforms
   * Update Firefox to 45.5.1esr
   * Update NoScript to *******
 * Linux
   * Bug 20691: Updater breaks if unix domain sockets are used

Tor Browser 6.0.7 -- November 30 2016
 * All Platforms
   * Update Firefox to 45.5.1esr
   * Update NoScript to *******

Tor Browser 6.5a4-hardened -- November 16 2016
 * All Platforms
   * Update Firefox to 45.5.0esr
   * Update Tor to *******-alpha
   * Update OpenSSL to 1.0.2j
   * Update Torbutton to *******
     * Bug 20414: Add donation banner on about:tor for 2016 campaign
     * Bug 20111: use Unix domain sockets for SOCKS port by default
     * Bug 19459: Move resizing code to tor-browser.git
     * Bug 20264: Change security slider to 3 options
     * Bug 20347: Enhance security slider's custom mode
     * Bug 20123: Disable remote jar on all security levels
     * Bug 20244: Move privacy checkboxes to about:preferences#privacy
     * Bug 17546: Add tooltips to explain our privacy checkboxes
     * Bug 17904: Allow security settings dialog to resize
     * Bug 18093: Remove 'Restore Defaults' button
     * Bug 20373: Prevent redundant dialogs opening
     * Bug 20388+20399+20394: Code clean-up
     * Translation updates
   * Update Tor Launcher to ********
     * Bug 20111: use Unix domain sockets for SOCKS port by default
     * Bug 20185: Avoid using Unix domain socket paths that are too long
     * Bug 20429: Do not open progress window if tor doesn't get started
     * Bug 19646: Wrong location for meek browser profile on OS X
     * Translation updates
   * Update HTTPS-Everywhere to 5.2.7
   * Update meek to 0.25
     * Bug 19646: Wrong location for meek browser profile on OS X
     * Bug 20030: Shut down meek-http-helper cleanly if built with Go > 1.5.4
   * Bug 20304: Support spaces and other special characters for SOCKS socket
   * Bug 20490: Fix assertion failure due to fix for #20304
   * Bug 19459: Size new windows to 1000x1000 or nearest 200x100 (Firefox patch)
   * Bug 20442: Backport fix for local path disclosure after drag and drop
   * Bug 20160: Backport fix for broken MP3-playback
   * Bug 20043: Isolate SharedWorker script requests to first party
   * Bug 20123: Always block remote jar files
   * Bug 20244: Move privacy checkboxes to about:preferences#privacy
   * Bug 19838: Add dgoulet's bridge and add another one commented out
   * Bug 19481: Point the update URL to aus1.torproject.org
   * Bug 20296: Rotate ports again for default obfs4 bridges
   * Bug 20651: DuckDuckGo does not work with JavaScript disabled
   * Bug 20399+15852: Code clean-up
   * Bug 15953: Weird resizing dance on Tor Browser startup
 * Build system
   * All platforms
     * Bug 20023: Upgrade Go to 1.7.3
     * Bug 20583: Make the downloads.json file reproducible

Tor Browser 6.5a4 -- November 16 2016
 * All Platforms
   * Update Firefox to 45.5.0esr
   * Update Tor to *******-alpha
   * Update OpenSSL to 1.0.2j
   * Update Torbutton to *******
     * Bug 20414: Add donation banner on about:tor for 2016 campaign
     * Bug 20111: use Unix domain sockets for SOCKS port by default
     * Bug 19459: Move resizing code to tor-browser.git
     * Bug 20264: Change security slider to 3 options
     * Bug 20347: Enhance security slider's custom mode
     * Bug 20123: Disable remote jar on all security levels
     * Bug 20244: Move privacy checkboxes to about:preferences#privacy
     * Bug 17546: Add tooltips to explain our privacy checkboxes
     * Bug 17904: Allow security settings dialog to resize
     * Bug 18093: Remove 'Restore Defaults' button
     * Bug 20373: Prevent redundant dialogs opening
     * Bug 20388+20399+20394: Code clean-up
     * Translation updates
   * Update Tor Launcher to ********
     * Bug 20111: use Unix domain sockets for SOCKS port by default
     * Bug 20185: Avoid using Unix domain socket paths that are too long
     * Bug 20429: Do not open progress window if tor doesn't get started
     * Bug 19646: Wrong location for meek browser profile on OS X
     * Translation updates
   * Update HTTPS-Everywhere to 5.2.7
   * Update meek to 0.25
     * Bug 19646: Wrong location for meek browser profile on OS X
     * Bug 20030: Shut down meek-http-helper cleanly if built with Go > 1.5.4
   * Bug 20304: Support spaces and other special characters for SOCKS socket
   * Bug 20490: Fix assertion failure due to fix for #20304
   * Bug 19459: Size new windows to 1000x1000 or nearest 200x100 (Firefox patch)
   * Bug 20442: Backport fix for local path disclosure after drag and drop
   * Bug 20160: Backport fix for broken MP3-playback
   * Bug 20043: Isolate SharedWorker script requests to first party
   * Bug 20123: Always block remote jar files
   * Bug 20244: Move privacy checkboxes to about:preferences#privacy
   * Bug 19838: Add dgoulet's bridge and add another one commented out
   * Bug 19481: Point the update URL to aus1.torproject.org
   * Bug 20296: Rotate ports again for default obfs4 bridges
   * Bug 20651: DuckDuckGo does not work with JavaScript disabled
   * Bug 20399+15852: Code clean-up
 * Windows
   * Bug 20342: Add tor-gencert.exe to expert bundle
   * Bug 18175: Maximizing window and restarting leads to non-rounded window size
   * Bug 13437: Rounded inner window accidentally grows to non-rounded size
 * OS X
   * Bug 20204: Windows don't drag on macOS Sierra anymore
   * Bug 20250: Meek fails on macOS Sierra if built with Go < 1.7
   * Bug 20590: Badly resized window due to security slider notification bar on OS X
   * Bug 20439: Make the build PIE on OSX
 * Linux
   * Bug 15953: Weird resizing dance on Tor Browser startup
 * Build system
   * All platforms
     * Bug 20023: Upgrade Go to 1.7.3
     * Bug 20583: Make the downloads.json file reproducible
   * OS X
     * Bug 20258: Make OS X Tor archive reproducible again
     * Bug 20184: Make OS X builds reproducible again
     * Bug 20210: In dmg2mar, extract old mar file to copy permissions to the new one

Tor Browser 6.0.6 -- November 15
 * All Platforms
   * Update Firefox to 45.5.0esr
   * Update Tor to 0.2.8.9
   * Update OpenSSL to 1.0.1u
   * Update Torbutton to *******2
     * Bug 20414: Add donation banner on about:tor for 2016 campaign
     * Translation updates
   * Update Tor Launcher to 0.2.9.4
     * Bug 20429: Do not open progress window if tor doesn't get started
     * Bug 19646: Wrong location for meek browser profile on OS X
   * Update HTTPS-Everywhere to 5.2.7
   * Update meek to 0.25
     * Bug 19646: Wrong location for meek browser profile on OS X
     * Bug 20030: Shut down meek-http-helper cleanly if built with Go > 1.5.4
   * Bug 19838: Add dgoulet's bridge and add another one commented out
   * Bug 20296: Rotate ports again for default obfs4 bridges
   * Bug 19735: Switch default search engine to DuckDuckGo
   * Bug 20118: Don't unpack HTTPS Everywhere anymore
 * Windows
   * Bug 20342: Add tor-gencert.exe to expert bundle
 * OS X
   * Bug 20204: Windows don't drag on macOS Sierra anymore
   * Bug 20250: Meek fails on macOS Sierra if built with Go < 1.7
 * Build system
   * All platforms
     * Bug 20023: Upgrade Go to 1.7.3

Tor Browser 6.5a3-hardened -- September 20 2016
 * All Platforms
   * Update Firefox to 45.4.0esr
   * Update Tor to *******-alpha
   * Update OpenSSL to 1.0.2h (bug 20095)
   * Update Torbutton to *******
     * Bug 17334: Move referrer spoofing for .onion domains into tor-browser.git
     * Bug 17767: Make "JavaScript disabled" more visible in Security Slider
     * Bug 19995: Clear site security settings during New Identity
     * Bug 19906: "Maximizing Tor Browser" Notification can exist multiple times
     * Bug 19837: Whitelist internal URLs that Firefox requires for media
     * Bug 15852: Remove/synchronize Torbutton SOCKS pref logic
     * Bug 19733: GETINFO response parser doesn't handle AF_UNIX entries + IPv6
     * Bug 14271: Make Torbutton work with Unix Domain Socket option
     * Translation updates
   * Update Tor Launcher to 0.2.11
     * Bug 14272: Make Tor Launcher work with Unix Domain Socket option
     * Bug 19568: Set CurProcD for Thunderbird/Instantbird
     * Bug 19432: Remove special handling for Instantbird/Thunderbird
     * Translation updates
   * Update HTTPS-Everywhere to 5.2.4
   * Update NoScript to ********
   * Bug 19851: Fix ASan error by upgrading GCC to 5.4.0
   * Bug 17858: Fix creation of incremental MARs for hardened builds
   * Bug 14273: Backport patches for Unix Domain Socket support
   * Bug 19890: Disable installation of system addons
   * Bug 17334: Spoof referrer when leaving a .onion domain
   * Bug 20092: Rotate ports for default obfs4 bridges
   * Bug 20040: Add update support for unpacked HTTPS Everywhere
   * Bug 20118: Don't unpack HTTPS Everywhere anymore
   * Bug 19336+19835: Enhance about:tbupdate page
 * Build system
   * All platforms
     * Bug 20133: Don't apply OpenSSL patch anymore
     * Bug 19528: Set MOZ_BUILD_DATE based on Firefox version

Tor Browser 6.5a3 -- September 20 2016
 * All Platforms
   * Update Firefox to 45.4.0esr
   * Update Tor to *******-alpha
   * Update OpenSSL to 1.0.2h (bug 20095)
   * Update Torbutton to *******
     * Bug 17334: Move referrer spoofing for .onion domains into tor-browser.git
     * Bug 17767: Make "JavaScript disabled" more visible in Security Slider
     * Bug 19995: Clear site security settings during New Identity
     * Bug 19906: "Maximizing Tor Browser" Notification can exist multiple times
     * Bug 19837: Whitelist internal URLs that Firefox requires for media
     * Bug 15852: Remove/synchronize Torbutton SOCKS pref logic
     * Bug 19733: GETINFO response parser doesn't handle AF_UNIX entries + IPv6
     * Bug 14271: Make Torbutton work with Unix Domain Socket option
     * Translation updates
   * Update Tor Launcher to ********
     * Bug 14272: Make Tor Launcher work with Unix Domain Socket option
     * Bug 19568: Set CurProcD for Thunderbird/Instantbird
     * Bug 19432: Remove special handling for Instantbird/Thunderbird
     * Translation updates
   * Update HTTPS-Everywhere to 5.2.4
   * Update NoScript to ********
   * Bug 14273: Backport patches for Unix Domain Socket support
   * Bug 19890: Disable installation of system addons
   * Bug 17334: Spoof referrer when leaving a .onion domain
   * Bug 20092: Rotate ports for default obfs4 bridges
   * Bug 20040: Add update support for unpacked HTTPS Everywhere
   * Bug 20118: Don't unpack HTTPS Everywhere anymore
   * Bug 19336+19835: Enhance about:tbupdate page
 * Android
   * Bug 19706: Store browser data in the app home directory
 * Build system
   * All platforms
     * Bug 20133: Don't apply OpenSSL patch anymore
     * Bug 19528: Set MOZ_BUILD_DATE based on Firefox version
   * OS X
     * Bug 19856: Make OS X builds reproducible again
     * Bug 19410: Fix incremental updates by taking signatures into account

Tor Browser 6.0.5 -- September 16
 * All Platforms
   * Update Firefox to 45.4.0esr
   * Update Tor to *******
   * Update Torbutton to *******
     * Bug 19995: Clear site security settings during New Identity
     * Bug 19906: "Maximizing Tor Browser" Notification can exist multiple times
   * Update HTTPS-Everywhere to 5.2.4
   * Bug 20092: Rotate ports for default obfs4 bridges
   * Bug 20040: Add update support for unpacked HTTPS Everywhere
 * Windows
   * Bug 19725: Remove old updater files left on disk after upgrade to 6.x
 * Linux
   * Bug 19725: Remove old updater files left on disk after upgrade to 6.x
 * Android
   * Bug 19706: Store browser data in the app home directory
 * Build system
   * All platforms
     * Upgrade Go to 1.4.3

Tor Browser 6.0.4 -- August 16 2016
 * All Platforms
   * Update Tor to *******
   * Update NoScript to ********
   * Bug 19890: Disable installation of system addons

Tor Browser 6.5a2-hardened -- August 3 2016
 * All Platforms
   * Update Firefox to 45.3.0esr
   * Update Tor to tor-*******-rc
   * Update Torbutton to *******
     * Bug 19689: Use proper parent window for plugin prompt
     * Bug 19206: Avoid SOCKS auth and NEWNYM collisions when sharing a tor client
     * Bug 19417: Disable asm.js (but add code to clear on New Identity if enabled)
     * Bug 19273: Improve external app launch handling and associated warnings
     * Bug 8725: Block addon resource and url fingerprinting with nsIContentPolicy
   * Update HTTPS-Everywhere to 5.2.1
   * Update NoScript to ********
   * Bug 17406: Include Selfrando into our hardened builds
   * Bug 19417: Disable asmjs for now
   * Bug 19715: Disable the meek-google pluggable transport option
   * Bug 19714: Remove mercurius4 obfs4 bridge
   * Bug 19585: Fix regression test for keyboard layout fingerprinting
   * Bug 19515: Tor Browser is crashing in graphics code
   * Bug 18513: Favicon requests can bypass New Identity
   * Bug 19273: Write C++ patch for external app launch handling
   * Bug 16998: Isolate preconnect requests to URL bar domain
   * Bug 18923: Add script to run all Tor Browser regression tests
   * Bug 19478: Prevent millisecond resolution leaks in File API
   * Bug 19401: Fix broken PDF download button
   * Bug 19411: Don't show update icon if a partial update failed
   * Bug 19400: Back out GCC bug workaround to avoid asmjs crash
   * Bug 19735: Switch default search engine to DuckDuckGo
   * Bug 19276: Disable Xrender due to possible performance regressions
   * Bug 19725: Remove old updater files left on disk after upgrade to 6.x
 * Build System
   * All Platforms
     * Bug 19703: Upgrade Go to 1.6.3

Tor Browser 6.5a2 -- August 3 2016
 * All Platforms
   * Update Firefox to 45.3.0esr
   * Update Tor to tor-*******-rc
   * Update Torbutton to *******
     * Bug 19689: Use proper parent window for plugin prompt
     * Bug 19206: Avoid SOCKS auth and NEWNYM collisions when sharing a tor client
     * Bug 19417: Disable asm.js (but add code to clear on New Identity if enabled)
     * Bug 19273: Improve external app launch handling and associated warnings
     * Bug 8725: Block addon resource and url fingerprinting with nsIContentPolicy
   * Update HTTPS-Everywhere to 5.2.1
   * Update NoScript to ********
   * Bug 19417: Disable asmjs for now
   * Bug 19715: Disable the meek-google pluggable transport option
   * Bug 19714: Remove mercurius4 obfs4 bridge
   * Bug 19585: Fix regression test for keyboard layout fingerprinting
   * Bug 19515: Tor Browser is crashing in graphics code
   * Bug 18513: Favicon requests can bypass New Identity
   * Bug 19273: Write C++ patch for external app launch handling
   * Bug 16998: Isolate preconnect requests to URL bar domain
   * Bug 18923: Add script to run all Tor Browser regression tests
   * Bug 19478: Prevent millisecond resolution leaks in File API
   * Bug 19401: Fix broken PDF download button
   * Bug 19411: Don't show update icon if a partial update failed
   * Bug 19400: Back out GCC bug workaround to avoid asmjs crash
   * Bug 19735: Switch default search engine to DuckDuckGo
 * Windows
   * Bug 19348: Adapt to more than one build target on Windows (fixes updates)
   * Bug 19725: Remove old updater files left on disk after upgrade to 6.x
 * Linux
   * Bug 19276: Disable Xrender due to possible performance regressions
   * Bug 19725: Remove old updater files left on disk after upgrade to 6.x
 * OS X
   * Bug 19269: Icon doesn't appear in Applications folder or Dock
 * Android
   * Bug 19484: Avoid compilation error when MOZ_UPDATER is not defined
 * Build System
   * All Platforms
     * Bug 19703: Upgrade Go to 1.6.3

Tor Browser 6.0.3 -- August 2 2016
 * All Platforms
   * Update Firefox to 45.3.0esr
   * Update Torbutton to *******
     * Bug 19417: Disable asmjs for now
     * Bug 19689: Use proper parent window for plugin prompt
   * Update HTTPS-Everywhere to 5.2.1
   * Update NoScript to ********
   * Bug 19417: Disable asmjs for now
   * Bug 19715: Disable the meek-google pluggable transport option
   * Bug 19714: Remove mercurius4 obfs4 bridge
   * Bug 19585: Fix regression test for keyboard layout fingerprinting
   * Bug 19515: Tor Browser is crashing in graphics code
   * Bug 18513: Favicon requests can bypass New Identity
 * OS X
   * Bug 19269: Icon doesn't appear in Applications folder or Dock
 * Android
   * Bug 19484: Avoid compilation error when MOZ_UPDATER is not defined

Tor Browser 6.0.2 -- June 21 2016
 * All Platforms
   * Update Torbutton to *******
     * Bug 19417: Clear asmjscache
   * Bug 19401: Fix broken PDF download button
   * Bug 19411: Don't show update icon if a partial update failed
   * Bug 19400: Back out GCC bug workaround to avoid asmjs crash
 * Windows
   * Bug 19348: Adapt to more than one build target on Windows (fixes updates)
 * Linux
   * Bug 19276: Disable Xrender due to possible performance regressions

Tor Browser 6.5a1-hardened -- June 8 2016
 * All Platforms
   * Update Firefox to 45.2.0esr
   * Update Tor to *******-alpha
   * Update Torbutton to 1.9.6
     * Bug 18743: Pref to hide 'Sign in to Sync' button in hamburger menu
     * Bug 18905: Hide unusable items from help menu
     * Bug 17599: Provide shortcuts for New Identity and New Circuit
     * Bug 18980: Remove obsolete toolbar button code
     * Bug 18238: Remove unused Torbutton code and strings
     * Translation updates
     * Code clean-up
   * Update Tor Launcher to *******
     * Bug 18947: Tor Browser is not starting on OS X if put into /Applications
   * Update HTTPS-Everywhere to 5.1.9
   * Update meek to 0.22 (tag 0.22-18371-3)
   * Bug 19121: The update.xml hash should get checked during update
   * Bug 12523: Mark JIT pages as non-writable
   * Bug 19193: Reduce timing precision for AudioContext, HTMLMediaElement, and MediaStream
   * Bug 19164: Remove support for SHA-1 HPKP pins
   * Bug 19186: KeyboardEvents are only rounding to 100ms
   * Bug 18884: Don't build the loop extension
   * Bug 19187: Backport fix for crash related to popup menus
   * Bug 19212: Fix crash related to network panel in developer tools
   * Bug 18703: Fix circuit isolation issues on Page Info dialog
   * bug 19115: Tor Browser should not fall back to Bing as its search engine
   * Bug 18915+19065: Use our search plugins in localized builds
   * Bug 19176: Zip our language packs deterministically
   * Bug 18811: Fix first-party isolation for blobs URLs in Workers
   * Bug 18950: Disable or audit Reader View
   * Bug 18886: Remove Pocket
   * Bug 18619: Tor Browser reports "InvalidStateError" in browser console
   * Bug 18945: Disable monitoring the connected state of Tor Browser users
   * Bug 18855: Don't show error after add-on directory clean-up
   * Bug 18885: Disable the option of logging TLS/SSL key material
   * Bug 18770: SVGs should not show up on Page Info dialog when disabled
   * Bug 18958: Spoof screen.orientation values
   * Bug 19047: Disable Heartbeat prompts
   * Bug 18914: Use English-only label in <isindex/> tags
   * Bug 18996: Investigate server logging in esr45-based Tor Browser
   * Bug 17790: Add unit tests for keyboard fingerprinting defenses
   * Bug 18995: Regression test to ensure CacheStorage is disabled
   * Bug 18912: Add automated tests for updater cert pinning
   * Bug 16728: Add test cases for favicon isolation
   * Bug 18976: Remove some FTE bridges
 * Linux
   * Bug 19189: Backport for working around a linker (gold) bug
 * Build System
   * All PLatforms
     * Bug 18333: Upgrade Go to 1.6.2
     * Bug 18919: Remove unused keys and unused dependencies
     * Bug 18291: Remove some uses of libfaketime
     * Bug 18845: Make zip and tar helpers generate reproducible archives

Tor Browser 6.5a1 -- June 8 2016
 * All Platforms
   * Update Firefox to 45.2.0esr
   * Update Tor to *******-alpha
   * Update Torbutton to 1.9.6
     * Bug 18743: Pref to hide 'Sign in to Sync' button in hamburger menu
     * Bug 18905: Hide unusable items from help menu
     * Bug 17599: Provide shortcuts for New Identity and New Circuit
     * Bug 18980: Remove obsolete toolbar button code
     * Bug 18238: Remove unused Torbutton code and strings
     * Translation updates
     * Code clean-up
   * Update Tor Launcher to *******
     * Bug 18947: Tor Browser is not starting on OS X if put into /Applications
   * Update HTTPS-Everywhere to 5.1.9
   * Update meek to 0.22 (tag 0.22-18371-3)
     * Bug 18904: Mac OS: meek-http-helper profile not updated
   * Bug 19121: The update.xml hash should get checked during update
   * Bug 12523: Mark JIT pages as non-writable
   * Bug 19193: Reduce timing precision for AudioContext, HTMLMediaElement, and MediaStream
   * Bug 19164: Remove support for SHA-1 HPKP pins
   * Bug 19186: KeyboardEvents are only rounding to 100ms
   * Bug 18884: Don't build the loop extension
   * Bug 19187: Backport fix for crash related to popup menus
   * Bug 19212: Fix crash related to network panel in developer tools
   * Bug 18703: Fix circuit isolation issues on Page Info dialog
   * bug 19115: Tor Browser should not fall back to Bing as its search engine
   * Bug 18915+19065: Use our search plugins in localized builds
   * Bug 19176: Zip our language packs deterministically
   * Bug 18811: Fix first-party isolation for blobs URLs in Workers
   * Bug 18950: Disable or audit Reader View
   * Bug 18886: Remove Pocket
   * Bug 18619: Tor Browser reports "InvalidStateError" in browser console
   * Bug 18945: Disable monitoring the connected state of Tor Browser users
   * Bug 18855: Don't show error after add-on directory clean-up
   * Bug 18885: Disable the option of logging TLS/SSL key material
   * Bug 18770: SVGs should not show up on Page Info dialog when disabled
   * Bug 18958: Spoof screen.orientation values
   * Bug 19047: Disable Heartbeat prompts
   * Bug 18914: Use English-only label in <isindex/> tags
   * Bug 18996: Investigate server logging in esr45-based Tor Browser
   * Bug 17790: Add unit tests for keyboard fingerprinting defenses
   * Bug 18995: Regression test to ensure CacheStorage is disabled
   * Bug 18912: Add automated tests for updater cert pinning
   * Bug 16728: Add test cases for favicon isolation
   * Bug 18976: Remove some FTE bridges
 * OS X
   * Bug 18951: HTTPS-E is missing after update
   * Bug 18904: meek-http-helper profile not updated
   * Bug 18928: Upgrade is not smooth (requires another restart)
 * Linux
   * Bug 19189: Backport for working around a linker (gold) bug
 * Build System
   * All PLatforms
     * Bug 18333: Upgrade Go to 1.6.2
     * Bug 18919: Remove unused keys and unused dependencies
     * Bug 18291: Remove some uses of libfaketime
     * Bug 18845: Make zip and tar helpers generate reproducible archives

Tor Browser 6.0.1 -- June 7 2016
 * All Platforms
   * Update Firefox to 45.2.0esr
   * Bug 18884: Don't build the loop extension
   * Bug 19187: Backport fix for crash related to popup menus
   * Bug 19212: Fix crash related to network panel in developer tools
 * Linux
   * Bug 19189: Backport for working around a linker (gold) bug

Tor Browser 6.0 -- May 30 2016
 * All Platforms
   * Update Firefox to 45.1.1esr
   * Update OpenSSL to 1.0.1t
   * Update Torbutton to *******
     * Bug 18466: Make Torbutton compatible with Firefox ESR 45
     * Bug 18743: Pref to hide 'Sign in to Sync' button in hamburger menu
     * Bug 18905: Hide unusable items from help menu
     * Bug 16017: Allow users to more easily set a non-tor SSH proxy
     * Bug 17599: Provide shortcuts for New Identity and New Circuit
     * Translation updates
     * Code clean-up
   * Update Tor Launcher to *******
     * Bug 13252: Do not store data in the application bundle
     * Bug 18947: Tor Browser is not starting on OS X if put into /Applications
     * Bug 11773: Setup wizard UI flow improvements
     * Translation updates
   * Update HTTPS-Everywhere to 5.1.9
   * Update meek to 0.22 (tag 0.22-18371-3)
     * Bug 18371: Symlinks are incompatible with Gatekeeper signing
     * Bug 18904: Mac OS: meek-http-helper profile not updated
   * Bug 15197 and child tickets: Rebase Tor Browser patches to ESR 45
   * Bug 18900: Fix broken updater on Linux
   * Bug 19121: The update.xml hash should get checked during update
   * Bug 18042: Disable SHA1 certificate support
   * Bug 18821: Disable libmdns support for desktop and mobile
   * Bug 18848: Disable additional welcome URL shown on first start
   * Bug 14970: Exempt our extensions from signing requirement
   * Bug 16328: Disable MediaDevices.enumerateDevices
   * Bug 16673: Disable HTTP Alternative-Services
   * Bug 17167: Disable Mozilla's tracking protection
   * Bug 18603: Disable performance-based WebGL fingerprinting option
   * Bug 18738: Disable Selfsupport and Unified Telemetry
   * Bug 18799: Disable Network Tickler
   * Bug 18800: Remove DNS lookup in lockfile code
   * Bug 18801: Disable dom.push preferences
   * Bug 18802: Remove the JS-based Flash VM (Shumway)
   * Bug 18863: Disable MozTCPSocket explicitly
   * Bug 15640: Place Canvas MediaStream behind site permission
   * Bug 16326: Verify cache isolation for Request and Fetch APIs
   * Bug 18741: Fix OCSP and favicon isolation for ESR 45
   * Bug 16998: Disable <link rel="preconnect"> for now
   * Bug 18898: Exempt the meek extension from the signing requirement as well
   * Bug 18899: Don't copy Torbutton, TorLauncher, etc. into meek profile
   * Bug 18890: Test importScripts() for cache and network isolation
   * Bug 18886: Hide pocket menu items when Pocket is disabled
   * Bug 18703: Fix circuit isolation issues on Page Info dialog
   * bug 19115: Tor Browser should not fall back to Bing as its search engine
   * Bug 18915+19065: Use our search plugins in localized builds
   * Bug 19176: Zip our language packs deterministically
   * Bug 18811: Fix first-party isolation for blobs URLs in Workers
   * Bug 18950: Disable or audit Reader View
   * Bug 18886: Remove Pocket
   * Bug 18619: Tor Browser reports "InvalidStateError" in browser console
   * Bug 18945: Disable monitoring the connected state of Tor Browser users
   * Bug 18855: Don't show error after add-on directory clean-up
   * Bug 18885: Disable the option of logging TLS/SSL key material
   * Bug 18770: SVGs should not show up on Page Info dialog when disabled
   * Bug 18958: Spoof screen.orientation values
   * Bug 19047: Disable Heartbeat prompts
   * Bug 18914: Use English-only label in <isindex/> tags
   * Bug 18996: Investigate server logging in esr45-based Tor Browser
   * Bug 17790: Add unit tests for keyboard fingerprinting defenses
   * Bug 18995: Regression test to ensure CacheStorage is disabled
   * Bug 18912: Add automated tests for updater cert pinning
   * Bug 16728: Add test cases for favicon isolation
   * Bug 18976: Remove some FTE bridges
 * Windows
   * Bug 13419: Support ICU in Windows builds
   * Bug 16874: Fix broken https://sports.yahoo.com/dailyfantasy page
   * Bug 18767: Context menu is broken on Windows in ESR 45 based Tor Browser
 * OS X
   * Bug 6540: Support OS X Gatekeeper
   * Bug 13252: Tor Browser should not store data in the application bundle
   * Bug 18951: HTTPS-E is missing after update
   * Bug 18904: meek-http-helper profile not updated
   * Bug 18928: Upgrade is not smooth (requires another restart)
 * Build System
   * All Platforms
     * Bug 18127: Add LXC support for building with Debian guest VMs
     * Bug 16224: Don't use BUILD_HOSTNAME anymore in Firefox builds
     * Bug 18919: Remove unused keys and unused dependencies
   * Windows
     * Bug 17895: Use NSIS 2.51 for installer to avoid DLL hijacking
     * Bug 18290: Bump mingw-w64 commit we use
   * OS X
     * Bug 18331: Update toolchain for Firefox 45 ESR
     * Bug 18690: Switch to Debian Wheezy guest VMs
   * Linux
     * Bug 18699: Stripping fails due to obsolete Browser/components directory
     * Bug 18698: Include libgconf2-dev for our Linux builds
     * Bug 15578: Switch to Debian Wheezy guest VMs (10.04 LTS is EOL)

Tor Browser 6.0a5-hardened -- April 28 2016
 * All Platforms
   * Update Firefox to 45.1.0esr
   * Update Tor to *******-alpha
   * Update Torbutton to *******
     * Bug 18466: Make Torbutton compatible with Firefox ESR 45
     * Translation updates
   * Update Tor Launcher to *******
     * Bug 13252: Do not store data in the application bundle
     * Bug 10534: Don't advertise the help desk directly anymore
     * Translation updates
   * Update HTTPS-Everywhere to 5.1.6
   * Update NoScript to ********
   * Update meek to 0.22 (tag 0.22-18371-2)
     * Bug 18371: Symlinks are incompatible with Gatekeeper signing
   * Bug 15197 and child tickets: Rebase Tor Browser patches to ESR 45
   * Bug 18900: Fix broken updater on Linux
   * Bug 18042: Disable SHA1 certificate support
   * Bug 18821: Disable libmdns support for desktop and mobile
   * Bug 18848: Disable additional welcome URL shown on first start
   * Bug 14970: Exempt our extensions from signing requirement
   * Bug 16328: Disable MediaDevices.enumerateDevices
   * Bug 16673: Disable HTTP Alternative-Services
   * Bug 17167: Disable Mozilla's tracking protection
   * Bug 18603: Disable performance-based WebGL fingerprinting option
   * Bug 18738: Disable Selfsupport and Unified Telemetry
   * Bug 18799: Disable Network Tickler
   * Bug 18800: Remove DNS lookup in lockfile code
   * Bug 18801: Disable dom.push preferences
   * Bug 18802: Remove the JS-based Flash VM (Shumway)
   * Bug 18863: Disable MozTCPSocket explicitly
   * Bug 15640: Place Canvas MediaStream behind site permission
   * Bug 16326: Verify cache isolation for Request and Fetch APIs
   * Bug 18741: Fix OCSP and favicon isolation for ESR 45
   * Bug 16998: Disable <link rel="preconnect"> for now
   * Bug 17506: Reenable building hardened Tor Browser with startup cache
   * Bug 18898: Exempt the meek extension from the signing requirement as well
   * Bug 18899: Don't copy Torbutton, TorLauncher, etc. into meek profile
   * Bug 18890: Test importScripts() for cache and network isolation
   * Bug 18726: Add new default obfs4 bridge (GreenBelt)
 * Build System
   * Bug 16224: Don't use BUILD_HOSTNAME anymore in Firefox builds
   * Bug 18699: Stripping fails due to obsolete Browser/components directory
   * Bug 18698: Include libgconf2-dev for our Linux builds

Tor Browser 6.0a5 -- April 28 2016
 * All Platforms
   * Update Firefox to 45.1.0esr
   * Update Tor to *******-alpha
   * Update Torbutton to *******
     * Bug 18466: Make Torbutton compatible with Firefox ESR 45
     * Translation updates
   * Update Tor Launcher to *******
     * Bug 13252: Do not store data in the application bundle
     * Bug 10534: Don't advertise the help desk directly anymore
     * Translation updates
   * Update HTTPS-Everywhere to 5.1.6
   * Update NoScript to ********
   * Update meek to 0.22 (tag 0.22-18371-2)
     * Bug 18371: Symlinks are incompatible with Gatekeeper signing
   * Bug 15197 and child tickets: Rebase Tor Browser patches to ESR 45
   * Bug 18900: Fix broken updater on Linux
   * Bug 18042: Disable SHA1 certificate support
   * Bug 18821: Disable libmdns support for desktop and mobile
   * Bug 18848: Disable additional welcome URL shown on first start
   * Bug 14970: Exempt our extensions from signing requirement
   * Bug 16328: Disable MediaDevices.enumerateDevices
   * Bug 16673: Disable HTTP Alternative-Services
   * Bug 17167: Disable Mozilla's tracking protection
   * Bug 18603: Disable performance-based WebGL fingerprinting option
   * Bug 18738: Disable Selfsupport and Unified Telemetry
   * Bug 18799: Disable Network Tickler
   * Bug 18800: Remove DNS lookup in lockfile code
   * Bug 18801: Disable dom.push preferences
   * Bug 18802: Remove the JS-based Flash VM (Shumway)
   * Bug 18863: Disable MozTCPSocket explicitly
   * Bug 15640: Place Canvas MediaStream behind site permission
   * Bug 16326: Verify cache isolation for Request and Fetch APIs
   * Bug 18741: Fix OCSP and favicon isolation for ESR 45
   * Bug 16998: Disable <link rel="preconnect"> for now
   * Bug 18898: Exempt the meek extension from the signing requirement as well
   * Bug 18899: Don't copy Torbutton, TorLauncher, etc. into meek profile
   * Bug 18890: Test importScripts() for cache and network isolation
   * Bug 18726: Add new default obfs4 bridge (GreenBelt)
 * Windows
   * Bug 13419: Support ICU in Windows builds
   * Bug 16874: Fix broken https://sports.yahoo.com/dailyfantasy page
   * Bug 18767: Context menu is broken on Windows in ESR 45 based Tor Browser
 * OS X
   * Bug 6540: Support OS X Gatekeeper
   * Bug 13252: Tor Browser should not store data in the application bundle
 * Build System
   * All Platforms
     * Bug 18127: Add LXC support for building with Debian guest VMs
     * Bug 16224: Don't use BUILD_HOSTNAME anymore in Firefox builds
   * Windows
     * Bug 17895: Use NSIS 2.51 for installer to avoid DLL hijacking
     * Bug 18290: Bump mingw-w64 commit we use
   * OS X
     * Bug 18331: Update toolchain for Firefox 45 ESR
     * Bug 18690: Switch to Debian Wheezy guest VMs
   * Linux
     * Bug 18699: Stripping fails due to obsolete Browser/components directory
     * Bug 18698: Include libgconf2-dev for our Linux builds

Tor Browser 5.5.5 -- April 26 2016
 * All Platforms
   * Update Firefox to 38.8.0esr
   * Update Tor Launcher to *******
     * Bug 10534: Don't advertise the help desk directly anymore
     * Translation updates
   * Update HTTPS-Everywhere to 5.1.6
   * Update NoScript to ********
   * Bug 18726: Add new default obfs4 bridge (GreenBelt)

Tor Browser 6.0a4-hardened -- March 17 2016
 * All Platforms
   * Update Firefox to 38.7.1esr
   * Update Torbutton to *******
     * Bug 18557: Exempt Graphite from the Security Slider
   * Bug 18536: Make Mosaddegh and MaBishomarim available on port 80 and 443

Tor Browser 6.0a4 -- March 17 2016
 * All Platforms
   * Update Firefox to 38.7.1esr
   * Update Torbutton to *******
     * Bug 18557: Exempt Graphite from the Security Slider
   * Bug 18536: Make Mosaddegh and MaBishomarim available on port 80 and 443

Tor Browser 5.5.4 -- March 16 2016
 * All Platforms
   * Update Firefox to 38.7.1esr
   * Update Torbutton to *******
     * Bug 18557: Exempt Graphite from the Security Slider
   * Bug 18536: Make Mosaddegh and MaBishomarim available on port 80 and 443

Tor Browser 6.0a3-hardened -- March 8 2016
 * All Platforms
   * Update Firefox to 38.7.0esr
   * Update Tor to *******-alpha
   * Update OpenSSL to 1.0.1s
   * Update NoScript to *******
   * Update HTTPS Everywhere to 5.1.4
   * Update Torbutton to *******
     * Bug 16990: Don't mishandle multiline commands
     * Bug 18144: about:tor update arrow position is wrong
     * Bug 16725: Allow resizing with non-default homepage
     * Bug 16917: Allow users to more easily set a non-tor SSH proxy
     * Translation updates
   * Bug 18030: Isolate favicon requests on Page Info dialog
   * Bug 18297: Use separate Noto JP,KR,SC,TC fonts
   * Bug 18170: Make sure the homepage is shown after an update as well
   * Bug 16728: Add test cases for favicon isolation
 * Windows
   * Bug 18292: Disable staged updates on Windows

Tor Browser 6.0a3 -- March 8 2016
 * All Platforms
   * Update Firefox to 38.7.0esr
   * Update Tor to *******-alpha
   * Update OpenSSL to 1.0.1s
   * Update NoScript to *******
   * Update HTTPS Everywhere to 5.1.4
   * Update Torbutton to *******
     * Bug 16990: Don't mishandle multiline commands
     * Bug 18144: about:tor update arrow position is wrong
     * Bug 16725: Allow resizing with non-default homepage
     * Bug 16917: Allow users to more easily set a non-tor SSH proxy
     * Translation updates
   * Bug 18030: Isolate favicon requests on Page Info dialog
   * Bug 18297: Use separate Noto JP,KR,SC,TC fonts
   * Bug 18170: Make sure the homepage is shown after an update as well
   * Bug 16728: Add test cases for favicon isolation
 * Windows
   * Bug 18292: Disable staged updates on Windows

Tor Browser 5.5.3 -- March 8 2016
 * All Platforms
   * Update Firefox to 38.7.0esr
   * Update OpenSSL to 1.0.1s
   * Update NoScript to *******
   * Update HTTPS Everywhere to 5.1.4
   * Update Torbutton to *******
     * Bug 16990: Don't mishandle multiline commands
     * Bug 18144: about:tor update arrow position is wrong
     * Bug 16725: Allow resizing with non-default homepage
     * Translation updates
   * Bug 18030: Isolate favicon requests on Page Info dialog
   * Bug 18297: Use separate Noto JP,KR,SC,TC fonts
   * Bug 18170: Make sure the homepage is shown after an update as well
 * Windows
   * Bug 18292: Disable staged updates on Windows

Tor Browser 6.0a2-hardened -- February 15 2016
 * All Platforms
   * Update Firefox to 38.6.1esr
   * Update NoScript to *******
   * Bug 18168: Don't clear an iframe's window.name (fix of #16620)
   * Bug 18137: Add two new obfs4 default bridges
 * Windows
   * Bug 18169: Whitelist zh-CN UI font
 * OSX
   * Bug 18172: Add Emoji support
 * Linux
   * Bug 18172: Add Emoji support
 * Build System
   * Linux
     * Bug 15578: Switch to Debian Wheezy guest VMs (10.04 LTS is EOL)
     * Bug 18198: Building the hardened Tor Browser in a Debian Wheezy VM is broken

Tor Browser 6.0a2 -- February 15 2016
 * All Platforms
   * Update Firefox to 38.6.1esr
   * Update NoScript to *******
   * Bug 18168: Don't clear an iframe's window.name (fix of #16620)
   * Bug 18137: Add two new obfs4 default bridges
 * Windows
   * Bug 18169: Whitelist zh-CN UI font
 * OSX
   * Bug 18172: Add Emoji support
 * Linux
   * Bug 18172: Add Emoji support

Tor Browser 5.5.2 -- February 12 2016
 * All Platforms
   * Update Firefox to 38.6.1esr
   * Update NoScript to *******

Tor Browser 5.5.1 -- February 4 2016
 * All Platforms
   * Bug 18168: Don't clear an iframe's window.name (fix of #16620)
   * Bug 18137: Add two new obfs4 default bridges
 * Windows
   * Bug 18169: Whitelist zh-CN UI font
 * OS X
   * Bug 18172: Add Emoji support
 * Linux
   * Bug 18172: Add Emoji support

Tor Browser 6.0a1-hardened -- January 27 2016
 * All Platforms
   * Update Firefox to 38.6.0esr
   * Update NoScript to *******
   * Update Torbutton to 1.9.5
     * Bug 16990: Show circuit display for connections using multi-party channels
     * Bug 18019: Avoid empty prompt shown after non-en-US update
     * Bug 18004: Remove Tor fundraising donation banner
     * Code cleanup
     * Translation updates
   * Update Tor Launcher to *******
     * Bug 18113: Randomly permutate available default bridges of chosen type
     * Bug 11773: Setup wizard UI flow improvements
     * Translation updates
   * Bug 17428: Remove Flashproxy
   * Bug 18115+18104+18071+18091: Update/add new obfs4 bridge
   * Bug 18072: Change recommended pluggable transport type to obfs4
   * Bug 18008: Create a new MAR Signing key and bake it into Tor Browser
   * Bug 16322: Use onion address for DuckDuckGo search engine
   * Bug 17917: Changelog after update is empty if JS is disabled
   * Bug 17790: Map the proper SHIFT characters to the digit keys (fix of #15646)

Tor Browser 6.0a1 -- January 27 2016
 * All Platforms
   * Update Firefox to 38.6.0esr
   * Update NoScript to *******
   * Update Torbutton to 1.9.5
     * Bug 16990: Show circuit display for connections using multi-party channels
     * Bug 18019: Avoid empty prompt shown after non-en-US update
     * Bug 18004: Remove Tor fundraising donation banner
     * Code cleanup
     * Translation updates
   * Update Tor Launcher to 0.2.9
     * Bug 18113: Randomly permutate available default bridges of chosen type
     * Bug 11773: Setup wizard UI flow improvements
     * Translation updates
   * Bug 17428: Remove Flashproxy
   * Bug 18115+18104+18071+18091: Update/add new obfs4 bridge
   * Bug 18072: Change recommended pluggable transport type to obfs4
   * Bug 18008: Create a new MAR Signing key and bake it into Tor Browser
   * Bug 16322: Use onion address for DuckDuckGo search engine
   * Bug 17917: Changelog after update is empty if JS is disabled
   * Bug 17790: Map the proper SHIFT characters to the digit keys (fix of #15646)
 * Build System
   * Linux
     * Bug 15578: Switch to Debian Wheezy guest VMs (10.04 LTS is EOL)

Tor Browser 5.5 -- January 26 2016
 * All Platforms
   * Update Firefox to 38.6.0esr
   * Update libevent to 2.0.22-stable
   * Update NoScript to *******
   * Update Torbutton to *******
     * Bug 16990: Show circuit display for connections using multi-party channels
     * Bug 18019: Avoid empty prompt shown after non-en-US update
     * Bug 18004: Remove Tor fundraising donation banner
     * Bug 16940: After update, load local change notes
     * Bug 17108: Polish about:tor appearance
     * Bug 17568: Clean up tor-control-port.js
     * Bug 16620: Move window.name handling into a Firefox patch
     * Bug 17351: Code cleanup
     * Translation updates
   * Update Tor Launcher to *******
     * Bug 18113: Randomly permutate available default bridges of chosen type
   * Bug 13313: Bundle a fixed set of fonts to defend against fingerprinting
   * Bug 10140: Add new Tor Browser locale (Japanese)
   * Bug 17428: Remove Flashproxy
   * Bug 13512: Load a static tab with change notes after an update
   * Bug 9659: Avoid loop due to optimistic data SOCKS code (fix of #3875)
   * Bug 15564: Isolate SharedWorkers by first-party domain
   * Bug 16940: After update, load local change notes
   * Bug 17759: Apply whitelist to local fonts in @font-face (fix of #13313)
   * Bug 17009: Shift and Alt keys leak physical keyboard layout (fix of #15646)
   * Bug 17790: Map the proper SHIFT characters to the digit keys (fix of #15646)
   * Bug 17369: Disable RC4 fallback
   * Bug 17442: Remove custom updater certificate pinning
   * Bug 16620: Move window.name handling into a Firefox patch
   * Bug 17220: Support math symbols in font whitelist
   * Bug 10599+17305: Include updater and build patches needed for hardened builds
   * Bug 18115+18104+18071+18091: Update/add new obfs4 bridge
   * Bug 18072: Change recommended pluggable transport type to obfs4
   * Bug 18008: Create a new MAR Signing key and bake it into Tor Browser
   * Bug 16322: Use onion address for DuckDuckGo search engine
   * Bug 17917: Changelog after update is empty if JS is disabled
 * Windows
   * Bug 17250: Add localized font names to font whitelist
   * Bug 16707: Allow more system fonts to get used on Windows
   * Bug 13819: Ship expert bundles with console enabled
   * Bug 17250: Fix broken Japanese fonts
   * Bug 17870: Add intermediate certificate for authenticode signing
 * OS X
   * Bug 17122: Rename Japanese OS X bundle
   * Bug 16707: Allow more system fonts to get used on OS X
   * Bug 17661: Whitelist font .Helvetica Neue DeskInterface
 * Linux
   * Bug 16672: Don't use font whitelisting for Linux users

Tor Browser 5.5a6-hardened -- January 7 2016
 * All Platforms
   * Update NoScript to 2.9
   * Update HTTPS Everywhere to 5.1.2
   * Bug 17931: Tor Browser crashes in LogMessageToConsole()
   * Bug 17875: Discourage editing of torrc-defaults

Tor Browser 5.5a6 -- January 7 2016
 * All Platforms
   * Update NoScript to 2.9
   * Update HTTPS Everywhere to 5.1.2
   * Bug 17931: Tor Browser crashes in LogMessageToConsole()
   * Bug 17875: Discourage editing of torrc-defaults
   * Bug 17870: Add intermediate certificate for authenticode signing

Tor Browser 5.0.7 -- January 7 2016
 * All Platforms
   * Update NoScript to 2.9
   * Update HTTPS Everywhere to 5.1.2
   * Bug 17931: Tor Browser crashes in LogMessageToConsole()
   * Bug 17875: Discourage editing of torrc-defaults

Tor Browser 5.5a5-hardened -- December 18 2015
 * All Platforms
   * Update Firefox to 38.5.0esr
   * Update Tor to *******
   * Update OpenSSL to 1.0.1q
   * Update NoScript to 2.7
   * Update Torbutton to *******
     * Bug 16940: After update, load local change notes
     * Bug 16990: Avoid matching '250 ' to the end of node name
     * Bug 17565: Tor fundraising campaign donation banner
     * Bug 17770: Fix alignments on donation banner
     * Bug 17792: Include donation banner in some non en-US Tor Browsers
     * Bug 17108: Polish about:tor appearance
     * Bug 17568: Clean up tor-control-port.js
     * Translation updates
   * Update Tor Launcher to *******
     * Bug 17344: Enumerate available language packs for language prompt
     * Code clean-up
     * Translation updates
   * Bug 12516: Compile Tor Browser with -fwrapv
   * Bug 9659: Avoid loop due to optimistic data SOCKS code (fix of #3875)
   * Bug 15564: Isolate SharedWorkers by first-party domain
   * Bug 16940: After update, load local change notes
   * Bug 17759: Apply whitelist to local fonts in @font-face (fix of #13313)
   * Bug 17747: Add ndnop3 as new default obfs4 bridge
   * Bug 17009: Shift and Alt keys leak physical keyboard layout (fix of #15646)
   * Bug 17369: Disable RC4 fallback
   * Bug 17442: Remove custom updater certificate pinning
   * Bug 16863: Avoid confusing error when loop.enabled is false
   * Bug 17502: Add a preference for hiding "Open with" on download dialog
   * Bug 17446: Prevent canvas extraction by third parties (fixup of #6253)
   * Bug 16441: Suppress "Reset Tor Browser" prompt

Tor Browser 5.5a5 -- December 18 2015
 * All Platforms
   * Update Firefox to 38.5.0esr
   * Update Tor to *******
   * Update OpenSSL to 1.0.1q
   * Update NoScript to 2.7
   * Update Torbutton to *******
     * Bug 16940: After update, load local change notes
     * Bug 16990: Avoid matching '250 ' to the end of node name
     * Bug 17565: Tor fundraising campaign donation banner
     * Bug 17770: Fix alignments on donation banner
     * Bug 17792: Include donation banner in some non en-US Tor Browsers
     * Bug 17108: Polish about:tor appearance
     * Bug 17568: Clean up tor-control-port.js
     * Translation updates
   * Bug 9659: Avoid loop due to optimistic data SOCKS code (fix of #3875)
   * Bug 15564: Isolate SharedWorkers by first-party domain
   * Bug 16940: After update, load local change notes
   * Bug 17759: Apply whitelist to local fonts in @font-face (fix of #13313)
   * Bug 17747: Add ndnop3 as new default obfs4 bridge
   * Bug 17009: Shift and Alt keys leak physical keyboard layout (fix of #15646)
   * Bug 17369: Disable RC4 fallback
   * Bug 17442: Remove custom updater certificate pinning
   * Bug 16863: Avoid confusing error when loop.enabled is false
   * Bug 17502: Add a preference for hiding "Open with" on download dialog
   * Bug 17446: Prevent canvas extraction by third parties (fixup of #6253)
   * Bug 16441: Suppress "Reset Tor Browser" prompt
 * Windows
   * Bug 13819: Ship expert bundles with console enabled
   * Bug 17250: Fix broken Japanese fonts
 * OS X
   * Bug 17661: Whitelist font .Helvetica Neue DeskInterface

Tor Browser 5.0.6 -- December 18 2015
  * All Platforms
    * Bug 17877: Tor Browser 5.0.5 is using the wrong Mozilla build tag

Tor Browser 5.0.5 -- December 15 2015
 * All Platforms
   * Update Firefox to 38.5.0esr
   * Update Tor to *******
   * Update OpenSSL to 1.0.1q
   * Update NoScript to 2.7
   * Update HTTPS Everywhere to 5.1.1
   * Update Torbutton to *******
     * Bug 16990: Avoid matching '250 ' to the end of node name
     * Bug 17565: Tor fundraising campaign donation banner
     * Bug 17770: Fix alignments on donation banner
     * Bug 17792: Include donation banner in some non en-US Tor Browsers
     * Translation updates
   * Bug 17207: Hide MIME types and plugins from websites
   * Bug 16909+17383: Adapt to HTTPS-Everywhere build changes
   * Bug 16863: Avoid confusing error when loop.enabled is false
   * Bug 17502: Add a preference for hiding "Open with" on download dialog
   * Bug 17446: Prevent canvas extraction by third parties (fixup of #6253)
   * Bug 16441: Suppress "Reset Tor Browser" prompt
   * Bug 17747: Add ndnop3 as new default obfs4 bridge

Tor Browser 5.5a4 -- November 3 2015
 * All Platforms
   * Update Firefox to 38.4.0esr
   * Update Tor to *******-rc
   * Update NoScript to *******9
   * Update HTTPS-Everywhere to 5.1.1
   * Update Torbutton to *******
     * Bug 9623: Spoof Referer when leaving a .onion domain
     * Bug 16620: Remove old window.name handling code
     * Bug 17164: Don't show text-select cursor on circuit display
     * Bug 17351: Remove unused code
     * Translation updates
   * Bug 17207: Hide MIME types and plugins from websites
   * Bug 16909+17383: Adapt to HTTPS-Everywhere build changes
   * Bug 16620: Move window.name handling into a Firefox patch
   * Bug 17220: Support math symbols in font whitelist
   * Bug 10599+17305: Include updater and build patches needed for hardened builds
   * Bug 17318: Remove dead ScrambleSuit bridge
   * Bug 17428: Remove default Flashproxy bridges
   * Bug 17473: Update meek-amazon fingerprint
 * Windows
   * Bug 17250: Add localized font names to font whitelist
 * OS X
   * Bug 17122: Rename Japanese OS X bundle
 * Linux
   * Bug 17329: Ensure that non-ASCII characters can be typed (fixup of #5926)

Tor Browser 5.0.4 -- November 3 2015
 * All Platforms
   * Update Firefox to 38.4.0esr
   * Update NoScript to *******9
   * Update Torbutton to *******
     * Bug 9623: Spoof Referer when leaving a .onion domain
     * Bug 16735: about:tor should accommodate different fonts/font sizes
     * Bug 16937: Don't translate the homepage/spellchecker dictionary string
     * Bug 17164: Don't show text-select cursor on circuit display
     * Bug 17351: Remove unused code
     * Translation updates
   * Bug 16937: Remove the en-US dictionary from non en-US Tor Browser bundles
   * Bug 17318: Remove dead ScrambleSuit bridge
   * Bug 17473: Update meek-amazon fingerprint
   * Bug 16983: Isolate favicon requests caused by the tab list dropdown
   * Bug 17102: Don't crash while opening a second Tor Browser
 * Windows:
   * Bug 16906: Don't depend on Windows crypto DLLs
 * Linux:
   * Bug 17329: Ensure that non-ASCII characters can be typed (fixup of #5926)

Tor Browser 5.5a3 -- September 22 2015
 * All Platforms
   * Update Firefox to 38.3.0esr
   * Update libevent to 2.0.22-stable
   * Update Torbutton to 1.9.4
     * Bug 16937: Don't translate the homepage/spellchecker dictionary string
     * Bug 16735: about:tor should accommodate different fonts/font sizes
     * Bug 16887: Update intl.accept_languages value
     * Bug 15493: Update circuit display on new circuit info
     * Bug 16797: brandShorterName is missing from brand.properties
     * Translation updates
   * Bug 10140: Add new Tor Browser locale (Japanese)
   * Bug 17102: Don't crash while opening a second Tor Browser
   * Bug 16983: Isolate favicon requests caused by the tab list dropdown
   * Bug 13512: Load a static tab with change notes after an update
   * Bug 16937: Remove the en-US dictionary from non en-US Tor Browser bundles
   * Bug 7446: Tor Browser should not "fix up" .onion domains (or any domains)
   * Bug 16837: Disable Firefox Hotfix updates
   * Bug 16855: Allow blobs to be downloaded on first-party pages (fixes mega.nz)
   * Bug 16781: Allow saving pdf files in built-in pdf viewer
   * Bug 16842: Restore Media tab on Page information dialog
   * Bug 16727: Disable about:healthreport page
   * Bug 16783: Normalize NoScript default whitelist
   * Bug 16775: Fix preferences dialog with security slider set to "High"
   * Bug 13579: Update download progress bar automatically
   * Bug 15646: Reduce keyboard layout fingerprinting in KeyboardEvent
   * Bug 17046: Event.timeStamp should not reveal startup time
   * Bug 16872: Fix warnings when opening about:downloads
   * Bug 17097: Fix intermittent crashes when using the print dialog
 * Windows
  * Bug 16906: Fix Mingw-w64 compilation/Don't depend on Windows crypto DLLs
  * Bug 16707: Allow more system fonts to get used on Windows
 * OS X
  * Bug 16910: Update copyright year in OS X bundles
  * Bug 16707: Allow more system fonts to get used on OS X
 * Linux
  * Bug 16672: Don't use font whitelisting for Linux users

Tor Browser 5.0.3 -- September 22 2015
 * All Platforms
   * Update Firefox to 38.3.0esr
   * Update Torbutton to *******
     * Bug 16887: Update intl.accept_languages value
     * Bug 15493: Update circuit display on new circuit info
     * Bug 16797: brandShorterName is missing from brand.properties
     * Bug 14429: Make sure the automatic resizing is disabled
     * Translation updates
   * Bug 7446: Tor Browser should not "fix up" .onion domains (or any domains)
   * Bug 16837: Disable Firefox Hotfix updates
   * Bug 16855: Allow blobs to be downloaded on first-party pages (fixes mega.nz)
   * Bug 16781: Allow saving pdf files in built-in pdf viewer
   * Bug 16842: Restore Media tab on Page information dialog
   * Bug 16727: Disable about:healthreport page
   * Bug 16783: Normalize NoScript default whitelist
   * Bug 16775: Fix preferences dialog with security slider set to "High"
   * Bug 13579: Update download progress bar automatically
   * Bug 15646: Reduce keyboard layout fingerprinting in KeyboardEvent
   * Bug 17046: Event.timeStamp should not reveal startup time
   * Bug 16872: Fix warnings when opening about:downloads
   * Bug 17097: Fix intermittent crashes when using the print dialog
 * Windows
  * Bug 16906: Fix Mingw-w64 compilation breakage
 * OS X
  * Bug 16910: Update copyright year in OS X bundles

Tor Browser 5.5a2 -- August 28 2015
 * All Platforms:
   * Update Firefox to 38.2.1esr
   * Update NoScript to *******6
   * Bug 16771: Fix crash on some websites due to blob URIs
 * Linux
   * Bug 16860: Avoid duplicate desktop icons on Gnome and Unity

Tor Browser 5.0.2 -- August 27 2015
 * All Platforms
   * Update Firefox to 38.2.1esr
   * Update NoScript to *******6
 * Linux
   * Bug 16860: Avoid duplicate icons on Unity and Gnome

Tor Browser 5.0.1 -- August 18 2015
 * All Platforms
   * Bug 16771: Fix crash on some websites due to blob URIs

Tor Browser 5.5a1 -- August 11 2015
 * All Platforms
   * Update Firefox to 38.2.0esr
   * Update NoScript to *******4
   * Update Torbutton to *******
     * Bug 16731: TBB 5.0 a3/a4 fails to download a file on right click
     * Bug 16730: Reset NoScript whitelist on upgrade
     * Bug 16722: Prevent "Tiles" feature from being enabled after upgrade
     * Bug 16488: Remove "Sign in to Sync" from the browser menu (fixup)
     * Bug 14429: Make sure the automatic resizing is enabled
     * Translation updates
   * Update Tor Launcher to *******
     * Translation updates
   * Bug 16730: Prevent NoScript from updating the default whitelist
   * Bug 16715: Use ThreadsafeIsCallerChrome() instead of IsCallerChrome()
   * Bug 16572: Verify cache isolation for XMLHttpRequests in Web Workers
   * Bug 16311: Fix navigation timing in ESR 38
   * Bug 15646: Prevent keyboard layout fingerprinting in KeyboardEvent (fixup)
   * Bug 16672: Change font whitelists and configs for rendering issues (partial)

Tor Browser 5.0 -- August 11 2015
 * All Platforms
   * Update Firefox to 38.2.0esr
   * Update OpenSSL to 1.0.1p
   * Update HTTPS-Everywhere to 5.0.7
   * Update NoScript to *******4
   * Update meek to 0.20
   * Update Tor to *******0 with patches:
     * Bug 16674: Allow FQDNs ending with a single '.' in our SOCKS host name checks.
     * Bug 16430: Allow DNS names with _ characters in them (fixes nytimes.com)
     * Bug 15482: Don't allow circuits to change while a site is in use
   * Update Torbutton to *******
     * Bug 16731: TBB 5.0 a3/a4 fails to download a file on right click
     * Bug 16730: Reset NoScript whitelist on upgrade
     * Bug 16722: Prevent "Tiles" feature from being enabled after upgrade
     * Bug 16488: Remove "Sign in to Sync" from the browser menu (fixup)
     * Bug 16268: Show Tor Browser logo on About page
     * Bug 16639: Check for Updates menu item can cause update download failure
     * Bug 15781: Remove the sessionstore filter
     * Bug 15656: Sync privacy.resistFingerprinting with Torbutton pref
     * Bug 16427: Use internal update URL to block updates (instead of 127.0.0.1)
     * Bug 16200: Update Cache API usage and prefs for FF38
     * Bug 16357: Use Mozilla API to wipe permissions db
     * Bug 14429: Make sure the automatic resizing is disabled
     * Translation updates
   * Update Tor Launcher to *******
     * Bug 16428: Use internal update URL to block updates (instead of 127.0.0.1)
     * Bug 15145: Visually distinguish "proxy" and "bridge" screens.
     * Translation updates
   * Bug 16730: Prevent NoScript from updating the default whitelist
   * Bug 16715: Use ThreadsafeIsCallerChrome() instead of IsCallerChrome()
   * Bug 16572: Verify cache isolation for XMLHttpRequests in Web Workers
   * Bug 16884: Prefer IPv6 when supported by the current Tor exit
   * Bug 16488: Remove "Sign in to Sync" from the browser menu
   * Bug 16662: Enable network.http.spdy.* prefs in meek-http-helper
   * Bug 15703: Isolate mediasource URIs and media streams to first party
   * Bug 16429+16416: Isolate blob URIs to first party
   * Bug 16632: Turn on the background updater and restart prompting
   * Bug 16528: Prevent indexedDB Modernizr site breakage on Twitter and elsewhere
   * Bug 16523: Fix in-browser JavaScript debugger
   * Bug 16236: Windows updater: avoid writing to the registry
   * Bug 16625: Fully disable network connection prediction
   * Bug 16495: Fix SVG crash when security level is set to "High"
   * Bug 13247: Fix meek profile error after bowser restarts
   * Bug 16005: Relax WebGL minimal mode
   * Bug 16300: Isolate Broadcast Channels to first party
   * Bug 16439: Remove Roku screencasting code
   * Bug 16285: Disabling EME bits
   * Bug 16206: Enforce certificate pinning
   * Bug 15910: Disable Gecko Media Plugins for now
   * Bug 13670: Isolate OCSP requests by first party domain
   * Bug 16448: Isolate favicon requests by first party
   * Bug 7561: Disable FTP request caching
   * Bug 6503: Fix single-word URL bar searching
   * Bug 15526: ES6 page crashes Tor Browser
   * Bug 16254: Disable GeoIP-based search results.
   * Bug 16222: Disable WebIDE to prevent remote debugging and addon downloads.
   * Bug 13024: Disable DOM Resource Timing API
   * Bug 16340: Disable User Timing API
   * Bug 14952: Disable HTTP/2
   * Bug 1517: Reduce precision of time for Javascript
   * Bug 13670: Ensure OCSP & favicons respect URL bar domain isolation
   * Bug 16311: Fix navigation timing in ESR 38
 * Windows
   * Bug 16014: Staged update fails if meek is enabled
   * Bug 16269: repeated add-on compatibility check after update (meek enabled)
 * Mac OS
   * Use OSX 10.7 SDK
   * Bug 16253: Tor Browser menu on OS X is broken with ESR 38
   * Bug 15773: Enable ICU on OS X
 * Build System
   * Bug 16351: Upgrade our toolchain to use GCC 5.1
   * Bug 15772 and child tickets: Update build system for Firefox 38
   * Bugs 15921+15922: Fix build errors during Mozilla Tryserver builds
   * Bug 15864: rename sha256sums.txt to sha256sums-unsigned-build.txt

Tor Browser 5.0a4 -- August 3 2015
 * All Platforms
   * Update Tor to *******-alpha with patches:
     * Bug 15482: Don't allow circuits to change while a site is in use
   * Update OpenSSL to 1.0.1p
   * Update HTTPS-Everywhere to 5.0.7
   * Update NoScript to *******1
   * Update Torbutton to *******
     * Bug 16268: Show Tor Browser logo on About page
     * Bug 16639: Check for Updates menu item can cause update download failure
     * Bug 15781: Remove the sessionstore filter
     * Bug 15656: Sync privacy.resistFingerprinting with Torbutton pref
     * Translation updates
   * Bug 16884: Prefer IPv6 when supported by the current Tor exit
   * Bug 16488: Remove "Sign in to Sync" from the browser menu
   * Bug 13313: Bundle a fixed set of fonts to defend against fingerprinting
   * Bug 16662: Enable network.http.spdy.* prefs in meek-http-helper
   * Bug 15646: Prevent keyboard layout fingerprinting in KeyboardEvent (fixup)
   * Bug 15703: Isolate mediasource URIs and media streams to first party
   * Bug 16429+16416: Isolate blob URIs to first party
   * Bug 16632: Turn on the background updater and restart prompting
   * Bug 16528: Prevent indexedDB Modernizr site breakage on Twitter and elsewhere
   * Bug 16523: Fix in-browser JavaScript debugger
   * Bug 16236: Windows updater: avoid writing to the registry
   * Bug 16005: Restrict WebGL minimal mode a bit (fixup)
   * Bug 16625: Fully disable network connection prediction
   * Bug 16495: Fix SVG crash when security level is set to "High"
 * Build System
   * Bug 15864: rename sha256sums.txt to sha256sums-unsigned-build.txt

Tor Browser 5.0a3 -- June 30 2015
 * All Platforms
   * Update Firefox to 38.1.0esr
   * Update OpenSSL to 1.0.1o
   * Update NoScript to ********
   * Update meek to 0.20
   * Tor patch backport
     * Bug 16430: Allow DNS names with _ characters in them (fixes nytimes.com)
   * Update Torbutton to *******
     * Bug 16403: Set search parameters for Disconnect
     * Bug 14429: Make sure the automatic resizing is disabled
     * Bug 16427: Use internal update URL to block updates (instead of 127.0.0.1)
     * Bug 16200: Update Cache API usage and prefs for FF38
     * Bug 16357: Use Mozilla API to wipe permissions db
     * Translation updates
   * Update Tor Launcher to *******
     * Bug 16428: Use internal update URL to block updates (instead of 127.0.0.1)
     * Bug 15145: Visually distinguish "proxy" and "bridge" screens.
     * Translation updates
   * Bug 13247: Fix meek profile error after bowser restarts
   * Bug 16397: Fix crash related to disabling SVG
   * Bug 16403: Set search parameters for Disconnect
   * Bug 16446: Update FTE bridge #1 fingerprint
   * Bug 15646: Prevent keyboard layout fingerprinting in KeyboardEvent
   * Bug 16005: Relax WebGL minimal mode
   * Bug 16300: Isolate Broadcast Channels to first party
   * Bug 16439: Remove Roku screencasting code
   * Bug 16285: Disabling EME bits
   * Bug 16206: Enforce certificate pinning
   * Bug 15910: Disable GMPs for now
   * Bug 13670: Isolate OCSP requests by first party domain
   * Bug 16448: Isolate favicon requests by first party
   * Bug 7561: Disable FTP request caching
   * Bug 6503: Fix single-word URL bar searching
   * Bug 15526: ES6 page crashes Tor Browser
   * Bug 16254: Disable GeoIP-based search results.
   * Bug 16222: Disable WebIDE to prevent remote debugging and addon downloads.
   * Bug 13024: Disable DOM Resource Timing API
   * Bug 16340: Disable User Timing API
   * Bug 14952: Disable HTTP/2
 * Mac OS
   * Use OSX 10.7 SDK
   * Bug 16253: Tor Browser menu on OS X is broken with ESR 38
 * Build System
   * Bug 16351: Upgrade our toolchain to use GCC 5.1
   * Bug 15772 and child tickets: Update build system for Firefox 38

Tor Browser 4.5.3 -- June 30 2015
 * All Platforms
   * Update Firefox to 31.8.0esr
   * Update OpenSSL to 1.0.1o
   * Update NoScript to ********
   * Update Torbutton to *******
     * Bug 16403: Set search parameters for Disconnect
     * Bug 14429: Make sure the automatic resizing is disabled
     * Translation updates
   * Bug 16397: Fix crash related to disabling SVG
   * Bug 16403: Set search parameters for Disconnect
   * Bug 16446: Update FTE bridge #1 fingerprint
   * Tor patch backport
     * Bug 16430: Allow DNS names with _ characters in them (fixes nytimes.com)

Tor Browser 5.0a2 -- June 15 2015
 * All Platforms
   * Update Tor to *******-alpha
   * Update HTTPS-Everywhere to 5.0.5
   * Update OpenSSL to 1.0.1n
   * Update NoScript to ********
   * Update meek to 0.19
   * Update Torbutton to *******
     * Bug 15984: Disabling Torbutton breaks the Add-ons Manager
     * Bug 14429: Make sure the automatic resizing is enabled
     * Translation updates
   * Bug 16130: Defend against logjam attack
   * Bug 15984: Disabling Torbutton breaks the Add-ons Manager
 * Windows
   * Bug 16014: Staged update fails if meek is enabled
   * Bug 16269: repeated add-on compatibility check after update (meek enabled)
 * Linux
   * Bug 16026: Fix crash in GStreamer
   * Bug 16083: Update comment in start-tor-browser

Tor Browser 4.5.2 -- June 15 2015
 * All Platforms
   * Update Tor to *******
   * Update HTTPS-Everywhere to 5.0.5
   * Update OpenSSL to 1.0.1n
   * Update NoScript to ********
   * Update Torbutton to *******
     * Bug 15984: Disabling Torbutton breaks the Add-ons Manager
     * Bug 14429: Make sure the automatic resizing is disabled
     * Translation updates
   * Bug 16130: Defend against logjam attack
   * Bug 15984: Disabling Torbutton breaks the Add-ons Manager
 * Linux
   * Bug 16026: Fix crash in GStreamer
   * Bug 16083: Update comment in start-tor-browser

Tor Browser 5.0a1 -- May 14 2015
 * All Platforms
   * Update Firefox to 31.7.0esr
   * Update meek to 0.18
   * Update Tor Launcher to *******
     * Translation updates only
   * Update Torbutton to *******
     * Bug 15837: Show descriptions if unchecking custom mode
     * Bug 15927: Force update of the NoScript UI when changing security level
     * Bug 15915: Hide circuit display if it is disabled.
     * Bug 14429: Improved automatic window resizing
     * Translation updates
   * Bug 15945: Disable NoScript's ClearClick protection for now
   * Bug 15933: Isolate by base (top-level) domain name instead of FQDN
   * Bug 15857: Fix file descriptor leak in updater that caused update failures
   * Bug 15899: Fix errors with downloading and displaying PDFs
   * Bug 15773: Enable ICU on OS X
   * Bug 1517: Reduce precision of time for Javascript
   * Bug 13670: Ensure OCSP & favicons respect URL bar domain isolation
   * Bug 13875: Improve the spoofing of window.devicePixelRatio
 * Windows
   * Bug 15872: Fix meek pluggable transport startup issue with Windows 7
 * Build System
   * Bug 15947: Support Ubuntu 14.04 LXC hosts via LXC_EXECUTE=lxc-execute env var
   * Bugs 15921+15922: Fix build errors during Mozilla Tryserver builds

Tor Browser 4.5.1 -- May 12 2015
 * All Platforms
   * Update Firefox to 31.7.0esr
   * Update meek to 0.18
   * Update Tor Launcher to *******
     * Translation updates only
   * Update Torbutton to *******
     * Bug 15837: Show descriptions if unchecking custom mode
     * Bug 15927: Force update of the NoScript UI when changing security level
     * Bug 15915: Hide circuit display if it is disabled.
     * Translation updates
   * Bug 15945: Disable NoScript's ClearClick protection for now
   * Bug 15933: Isolate by base (top-level) domain name instead of FQDN
   * Bug 15857: Fix file descriptor leak in updater that caused update failures
   * Bug 15899: Fix errors with downloading and displaying PDFs
 * Windows
   * Bug 15872: Fix meek pluggable transport startup issue with Windows 7
 * Build System
   * Bug 15947: Support Ubuntu 14.04 LXC hosts via LXC_EXECUTE=lxc-execute env var
   * Bugs 15921+15922: Fix build errors during Mozilla Tryserver builds

Tor Browser 4.5 -- Apr 28 2015
 * All Platforms
   * Update Tor to ******* with additional patches:
     * Bug 15482: Reset timestamp_dirty each time a SOCKSAuth circuit is used
   * Update NoScript to ********
   * Update HTTPS-Everywhere to 5.0.3
     * Bug 15689: Resume building HTTPS-Everywhere from git tags
   * Update meek to 0.17
   * Update obfs4proxy to 0.0.5
   * Update Tor Launcher to *******
     * Bug 15704: Do not enable network if wizard is opened
     * Bug 11879: Stop bootstrap if Cancel or Open Settings is clicked
     * Bug 13576: Don't strip "bridge" from the middle of bridge lines
     * Bug 15657: Display the host:port of any connection faiures in bootstrap
   * Update Torbutton to *******
     * Bug 15562: Bind SharedWorkers to thirdparty pref
     * Bug 15533: Restore default security level when restoring defaults
     * Bug 15510: Close Tor Circuit UI control port connections on New Identity
     * Bug 15472: Make node text black in circuit status UI
     * Bug 15502: Wipe blob URIs on New Identity
     * Bug 15795: Some security slider prefs do not trigger custom checkbox
     * Bug 14429: Disable automatic window resizing for now
   * Bug 4100: Raise HTTP Keep-Alive back to 115 second default
   * Bug 13875: Spoof window.devicePixelRatio to avoid DPI fingerprinting
   * Bug 15411: Remove old (and unused) cacheDomain cache isolation mechanism
   * Bugs 14716+13254: Fix issues with HTTP Auth usage and TLS connection info display
   * Bug 15502: Isolate blob URI scope to URL domain; block WebWorker access
   * Bug 15794: Crash on some pages with SVG images if SVG is disabled
   * Bug 15562: Disable Javascript SharedWorkers due to third party tracking
   * Bug 15757: Disable Mozilla video statistics API extensions
   * Bug 15758: Disable Device Sensor APIs
 * Linux
   * Bug 15747: Improve start-tor-browser argument handling
   * Bug 15672: Provide desktop app registration+unregistration for Linux
 * Windows
   * Bug 15539: Make installer exe signatures reproducibly removable
   * Bug 10761: Fix instances of shutdown crashes

Tor Browser 4.5a5 -- Mar 31 2015
 * All Platforms
   * Update Firefox to 31.6.0esr
   * Update OpenSSL to 1.0.1m
   * Update Tor to *******
   * Update NoScript to *******9
   * Update HTTPS-Everywhere to 5.0
   * Update meek to 0.16
   * Update Tor Launcher to *******
     * Bug 13983: Directory search path fix for Tor Messanger+TorBirdy
   * Update Torbutton to *******
     * Bug 9387: "Security Slider 1.0"
       * Include descriptions and tooltip hints for security levels
       * Notify users that the security slider exists
       * Flip slider so that "low" is on the bottom
       * Make use of new SVG and MathML prefs
     * Bug 13766: Set a 10 minute circuit lifespan for non-content requests
     * Bug 15460: Ensure FTP urls use content-window circuit isolation
     * Bug 13650: Clip initial window height to 1000px
     * Bug 14429: Ensure windows can only be resized to 200x100px multiples
     * Bug 15334: Display Cookie Protections menu if disk records are enabled
     * Bug 14324: Show HS circuit in Tor circuit display
     * Bug 15086: Handle RTL text in Tor circuit display
     * Bug 15085: Fix about:tor RTL text alignment problems
     * Bug 10216: Add a pref to disable the local tor control port test
     * Bug 14937: Show meek and flashproxy bridges in tor circuit display
     * Bugs 13891+15207: Fix exceptions/errors in circuit display with bridges
     * Bug 13019: Change locale hiding pref to boolean
     * Bug 7255: Warn users about maximizing windows
     * Bug 14631: Improve profile access error msgs (strings).
   * Pluggable Transport Dependency Updates:
     * Bug 15448: Use golang 1.4.2 for meek and obs4proxy
     * Bug 15265: Switch go.net repo to golang.org/x/net
   * Bug 14937: Hard-code meek and flashproxy node fingerprints
   * Bug 13019: Prevent Javascript from leaking system locale
   * Bug 10280: Improved fix to prevent loading plugins into address space
   * Bug 15406: Only include addons in incremental updates if they actually update
   * Bug 15029: Don't prompt to include missing plugins
   * Bug 12827: Create preference to disable SVG images (for security slider)
   * Bug 13548: Create preference to disable MathML (for security slider)
   * Bug 14631: Improve startup error messages for filesystem permissions issues
   * Bug 15482: Don't allow circuits to change while a site is in use
 * Linux
   * Bug 13375: Create a hybrid GUI/desktop/shell launcher wrapper
   * Bug 12468: Only print/write log messages if launched with --debug
 * Windows
   * Bug 3861: Begin signing Tor Browser for Windows the Windows way
   * Bug 15201: Disable 'runas Administrator' codepaths in updater
   * Bug 14688: Create shortcuts to desktop and start menu by default (optional)

Tor Browser 4.0.6 -- Mar 31 2015
 * All Platforms
   * Update Firefox to 31.6.0esr
   * Update meek to 0.16
   * Update OpenSSL to 1.0.1m

Tor Browser 4.0.5 -- Mar 23 2015
 * All Platforms
   * Update Firefox to 31.5.3esr
   * Update Tor to ********
   * Update NoScript to *******9

Tor Browser 4.5a4 -- Feb 24 2015
 * All Platforms
   * Update Firefox to 31.5.0esr
   * Update Tor to *******-alpha
   * Update OpenSSL to 1.0.1l
   * Update NoScript to *******5
   * Update obfs4proxy to 0.0.4
     * Use obfs4proxy for ScrambleSuit bridges
   * Update Torbutton to *******
     * Bug 13882: Fix display of bridges after bridge settings have been changed
     * Bug 5698: Use "Tor Browser" branding in "About Tor Browser" dialog
     * Bug 10280: Strings and pref for preventing plugin initialization.
     * Bug 14866: Show correct circuit when more than one exists for a given domain
     * Bug 9442: Add New Circuit button to Torbutton menu
     * Bug 9906: Warn users before closing all windows and performing new identity.
     * Bug 8400: Prompt for restart if disk records are enabled/disabled.
     * Bug 14630: Hide Torbutton's proxy settings tab.
     * Bug 14632: Disable Cookie Manager until we get it working.
     * Bug 11175: Remove "About Torbutton" from onion menu.
     * Bug 13900: Remove remaining SafeCache code in favor of C++ patch
     * Bug 14490: Use Disconnect search in about:tor search box
     * Bug 14392: Don't steal input focus in about:tor search box
     * Bug 11236: Don't set omnibox order in Torbutton (to prevent translation)
     * Bug 13406: Stop directing users to download-easy.html.en on update
     * Bug 9387: Handle "custom" mode better in Security Slider
     * Bug 12430: Bind jar: pref to Security Slider
     * Bug 14448: Restore Torbutton menu operation on non-English localizations
     * Translation updates
   * Update Tor Launcher to *******
     * Bug 13271: Display Bridge Configuration wizard pane before Proxy pane
     * Bug 14336: Fix navigation button display issues on some wizard panes
     * Translation updates
   * Bug 14203: Prevent meek from displaying an extra update notification
   * Bug 14849: Remove new NoScript menu option to make permissions permanent
   * Bug 14851: Set NoScript pref to disable permanent permissions
   * Bug 14490: Make Disconnect the default omnibox search engine
   * Bug 11236: Fix omnibox order for non-English builds
     * Also remove Amazon, eBay and bing; add Youtube and Twitter
   * Bug 10280: Don't load any plugins into the address space.
   * Bug 14392: Make about:tor hide itself from the URL bar
   * Bug 12430: Provide a preference to disable remote jar: urls
   * Bug 13900: Remove 3rd party HTTP auth tokens via Firefox patch
   * Bug 5698: Fix branding in "About Torbrowser" window
 * Windows:
   * Bug 13169: Don't use /dev/random on Windows for SSP
 * Linux:
   * Bug 13717: Make sure we use the bash shell on Linux

Tor Browser 4.0.4 -- Feb 24 2015
 * All Platforms
   * Update Firefox to 31.5.0esr
   * Update OpenSSL to 1.0.1l
   * Update NoScript to *******5
   * Update HTTPS-Everywhere to 4.0.3
   * Bug 14203: Prevent meek from displaying an extra update notification
   * Bug 14849: Remove new NoScript menu option to make permissions permanent
   * Bug 14851: Set NoScript pref to disable permanent permissions

Tor Browser 4.5a3 -- Jan 19 2015
 * All Platforms
   * Update Firefox to 31.4.0esr
   * Update Tor to *******-alpha
   * Update NoScript to ********
   * Update HTTPS Everywhere to 5.0development.2
   * Update meek to 0.15
   * Update Torbutton to *******
     * Bug 13998: Handle changes in NoScript *******+
     * Bug 14100: Option to hide NetworkSettings menuitem
     * Bug 13079: Option to skip control port verification
     * Bug 13835: Option to change default Tor Browser homepage
     * Bug 11449: Fix new identity error if NoScript is not enabled
     * Bug 13881: Localize strings for tor circuit display
     * Bug 9387: Incorporate user feedback
     * Bug 13671: Fixup for circuit display if bridges are used
     * Translation updates
   * Update Tor Launcher to *******
     * Bug 14122: Hide logo if TOR_HIDE_BROWSER_LOGO set
     * Translation updates
   * Bug 13379: Sign our MAR files
   * Bug 13788: Fix broken meek in 4.5-alpha series
   * Bug 13439: No canvas prompt for content callers

Tor Browser 4.0.3 -- Jan 13 2015
 * All Platforms
   * Update Firefox to 31.4.0esr
   * Update NoScript to ********
   * Update meek to 0.15
   * Update Tor Launcher to *******.2
     * Translation updates only

Tor Browser 4.5-alpha-2 -- Dec 5 2014
 * All Platforms
   * Update Firefox to 31.3.0esr
   * Update NoScript to *******
   * Update HTTPS Everywhere to 5.0development.1
   * Update Torbutton to *******
     * Bug 13672: Make circuit display optional
     * Bug 13671: Make bridges visible on circuit display
     * Bug 9387: Incorporate user feedback
     * Bug 13784: Remove third party authentication tokens
   * Bug 13435: Remove our custom POODLE fix (fixed by Mozilla in ESR 31.3.0)

Tor Browser 4.0.2 -- Dec 2 2014
 * All Platforms
   * Update Firefox to 31.3.0esr
   * Update NoScript to *******
   * Update HTTPS Everywhere to 4.0.2
   * Update Torbutton to *******
     * Bug 13019: Synchronize locale spoofing pref with our Firefox patch
     * Bug 13746: Properly link Torbutton UI to thirdparty pref.
   * Bug 13742: Fix domain isolation for content cache and disk-enabled browsing mode
   * Bug 5926: Prevent JS engine locale leaks (by setting the C library locale)
   * Bug 13504: Remove unreliable/unreachable non-public bridges
   * Bug 13435: Remove our custom POODLE fix
 * Windows
   * Bug 13443: Re-enable DirectShow; fix crash with mingw patch.
   * Bug 13558: Fix crash on Windows XP during download folder changing
   * Bug 13594: Fix update failure for Windows XP users

Tor Browser 4.5-alpha-1 -- Nov 14 2014
 * All Platforms
   * Bug 3455: Patch Firefox SOCKS and proxy filters to allow user+pass isolation
   * Bug 11955: Backport HTTPS Certificate Pinning patches from Firefox 32
   * Bug 13684: Backport Mozilla bug #1066190 (pinning issue fixed in Firefox 33)
   * Bug 13019: Make JS engine use English locale if a pref is set by Torbutton
   * Bug 13301: Prevent extensions incompatibility error after upgrades
   * Bug 13460: Fix MSVC compilation issue
   * Bug 13504: Remove stale bridges from default bridge set
   * Bug 13742: Fix domain isolation for content cache and disk-enabled browsing mode
   * Update Tor to *******-alpha
   * Update NoScript to *******
   * Update Torbutton to *******
     * Bug 9387: Provide a "Security Slider" for vulnerability surface reduction
     * Bug 13019: Synchronize locale spoofing pref with our Firefox patch
     * Bug 3455: Use SOCKS user+pass to isolate all requests from the same url domain
     * Bug 8641: Create browser UI to indicate current tab's Tor circuit IPs
     * Bug 13651: Prevent circuit-status related UI hang.
     * Bug 13666: Various circuit status UI fixes
     * Bugs 13742+13751: Remove cache isolation code in favor of direct C++ patch
     * Bug 13746: Properly update third party isolation pref if disabled from UI
   * Bug 13586: Make meek use TLS session tickets (to look like stock Firefox).
   * Bug 12903: Include obfs4proxy pluggable transport
 * Windows
   * Bug 13443: Re-enable DirectShow; fix crash with mingw patch.
   * Bug 13558: Fix crash on Windows XP during download folder changing
   * Bug 13091: Make app name "Tor Browser" instead of "Tor"
   * Bug 13594: Fix update failure for Windows XP users
 * Mac
   * Bug 10138: Switch to 64bit builds for MacOS

Tor Browser 4.0.1 -- Oct 30 2014
 * All Platforms
   * Update Tor to ********
   * Update NoScript to *******
   * Bug 13301: Prevent extensions incompatibility error after upgrades
   * Bug 13460: Fix MSVC compilation issue
 * Windows
   * Bug 13443: Disable DirectShow to prevent crashes on many sites
   * Bug 13091: Make app name "Tor Browser" instead of "Tor"

Tor Browser 4.0 -- Oct 15 2014
 * All Platforms
   * Update Firefox to 31.2.0esr
   * Update Torbutton to *******
      * Bug 13378: Prevent addon reordering in toolbars on first-run.
      * Bug 10751: Adapt Torbutton to ESR31's Australis UI.
      * Bug 13138: ESR31-about:tor shows "Tor is not working"
      * Bug 12947: Adapt session storage blocker to ESR 31.
      * Bug 10716: Take care of drag/drop events in ESR 31.
      * Bug 13366: Fix cert exemption dialog when disk storage is enabled.
   * Update Tor Launcher to *******.1
     * Translation updates only
   * Udate fteproxy to 0.2.19
   * Update NoScript to *******
   * Bug 13416: Defend against new SSLv3 attack (poodle).
   * Bug 13027: Spoof window.navigator useragent values in JS WebWorker threads
   * Bug 13016: Hide CSS -moz-osx-font-smoothing values.
   * Bug 13356: Meek and other symlinks missing after complete update.
   * Bug 13025: Spoof screen orientation to landscape-primary.
   * Bug 13346: Disable Firefox "slow to start" warnings and recordkeeping.
   * Bug 13318: Minimize number of buttons on the browser toolbar.
   * Bug 10715: Enable WebGL on Windows (still click-to-play via NoScript)
   * Bug 13023: Disable the gamepad API.
   * Bug 13021: Prompt before allowing Canvas isPointIn*() calls.
   * Bug 12460: Several cross-compilation and gitian fixes (see child tickets)
   * Bug 13186: Disable DOM Performance timers
   * Bug 13028: Defense-in-depth checks for OCSP/Cert validation proxy usage

Tor Browser 4.0-alpha-3 -- Sep 24 2014
 * All Platforms
   * Update Tor to *******-rc
   * Update Firefox to 24.8.1esr
   * Update meek to 0.11
   * Update NoScript to ********
   * Update Torbutton to ********
     * Bug 13091: Use "Tor Browser" everywhere
     * Bug 10804: Workaround fix for some cases of startup hang
   * Bug 13091: Use "Tor Browser" everywhere
   * Bug 13049: Browser update failure (self.update is undefined)
   * Bug 13047: Updater should not send Kernel and GTK version
   * Bug 12998: Prevent intermediate certs from being written to disk
   * Bug 13245: Prevent non-english TBBs from upgrading to english version.
 * Linux:
   * Bug 9150: Make RPATH unavailable on Tor binary.
   * Bug 13031: Add full RELRO protection.

Tor Browser Bundle 3.6.6 -- Sep 24 2014
 * All Platforms
   * Update Tor to tor-********
   * Update Firefox to 24.8.1esr
   * Update NoScript to ********
   * Update HTTPS Everywhere to 4.0.1
   * Bug 12998: Prevent intermediate certs from being written to disk
   * Update Torbutton to ********
     * Bug 13091: Use "Tor Browser" everywhere
     * Bug 10804: Workaround fix for some cases of startup hang
 * Linux
   * Bug 9150: Make RPATH unavailable on Tor binary.

Tor Browser Bundle 4.0-alpha-2 -- Sep 2 2014
 * All Platforms
   * Update Firefox to 24.8.0esr
   * Update NoScript to ********
   * Update Tor Launcher to *******
     * Bug 11405: Remove firewall prompt from wizard.
     * Bug 12895: Mention @riseup.net as a valid bridge request email address
     * Bug 12444: Provide feedback when “Copy Tor Log” is clicked.
     * Bug 11199: Improve error messages if Tor exits unexpectedly
   * Update Torbutton to ********
     * Bug 12684: New strings for canvas image extraction message
     * Bug 8940: Move RecommendedTBBVersions file to www.torproject.org
   * Bug 12684: Improve Canvas image extraction permissions prompt
   * Bug 7265: Only prompt for first party canvas access. Log all scripts
               that attempt to extract canvas images to Browser console.
   * Bug 12974: Disable NTLM and Negotiate HTTP Auth
   * Bug 2874: Remove Components.* from content access (regression)
   * Bug 4234: Automatic Update support (off by default)
   * Bug 9881: Open popups in new tabs by default
   * Meek Pluggable Transport:
     * Bug 12766: Use TLSv1.0 in meek-http-helper to blend in with Firefox 24
 * Windows:
   * Bug 10065: Enable DEP, ASLR, and SSP hardening options
 * Linux:
   * Bug 12103: Adding RELRO hardening back to browser binaries.

Tor Browser Bundle 3.6.5 -- Sep 2 2014
 * All Platforms
   * Update Firefox to 24.8.0esr
   * Update NoScript to ********
   * Update HTTPS Everywhere to 4.0.0
   * Update Torbutton to ********
     * Bug 12684: New strings for canvas image extraction message
     * Bug 8940: Move RecommendedTBBVersions file to www.torproject.org
     * Bug 9531: Workaround to avoid rare hangs during New Identity
   * Bug 12684: Improve Canvas image extraction permissions prompt
   * Bug 7265: Only prompt for first party canvas access. Log all scripts
               that attempt to extract canvas images to Browser console.
   * Bug 12974: Disable NTLM and Negotiate HTTP Auth
   * Bug 2874: Remove Components.* from content access (regression)
   * Bug 9881: Open popups in new tabs by default
 * Linux:
   * Bug 12103: Adding RELRO hardening back to browser binaries.

Tor Browser Bundle 4.0-alpha-1 -- Aug 8 2014
 * All Platforms
   * Ticket 10935: Include the Meek Pluggable Transport (version 0.10)
     * Two modes of Meek are provided: Meek over Google and Meek over Amazon
   * Update Firefox to 24.7.0esr
   * Update Tor to *******-alpha
   * Update OpenSSL to 1.0.1i
   * Update NoScript to ********
     * Script permissions now apply based on URL bar
   * Update HTTPS Everywhere to 5.0development.0
   * Update Torbutton to ********
     * Bug 12221: Remove obsolete Javascript components from the toggle era
     * Bug 10819: Bind new third party isolation pref to Torbutton security UI
     * Bug 9268: Fix some window resizing corner cases with DPI and taskbar size.
     * Bug 12680: Change Torbutton URL in about dialog.
     * Bug 11472: Adjust about:tor font and logo positioning to avoid overlap
     * Bug 9531: Workaround to avoid rare hangs during New Identity
   * Update Tor Launcher to *******
     * Bug 11199: Improve behavior if tor exits
     * Bug 12451: Add option to hide TBB's logo
     * Bug 11193: Change "Tor Browser Bundle" to "Tor Browser"
     * Bug 11471: Ensure text fits the initial configuration dialog
     * Bug 9516: Send Tor Launcher log messages to Browser Console
   * Bug 11641: Reorganize bundle directory structure to mimic Firefox
   * Bug 10819: Create a preference to enable/disable third party isolation
   * Backported Tor Patches:
     * Bug 11200: Fix a hang during bootstrap introduced in the initial
                  bug11200 patch.
 * Linux:
   * Bug 10178: Make it easier to set an alternate Tor control port and password
   * Bug 11102: Set Window Class to "Tor Browser" to aid in Desktop navigation
   * Bug 12249: Don't create PT debug files anymore

Tor Browser Bundle 3.6.4 -- Aug 8 2014
 * All Platforms
   * Update Tor to ********
   * Update Tor launcher to *******
     * Bug 9516: Show Tor log in TorBrowser's Browser Console
   * Update OpenSSL to 1.0.1i
   * Backported Tor Patches:
     * Bug 11654: Properly apply the fix for malformed bug11156 log message
     * Bug 11200: Fix a hang during bootstrap introduced in the initial
                  bug11200 patch.
   * Update NoScript to ********
   * Update Torbutton to ********
     * Bug 11472: Adjust about:tor font and logo positioning to avoid overlap
     * Bug 12680: Fix Torbutton about url.

Tor Browser Bundle 3.6.3 -- Jul 24 2014
 * All Platforms
   * Update Firefox to 24.7.0esr
   * Update obfsproxy to 0.2.12
   * Update FTE to 0.2.17
   * Update NoScript to ********
   * Update HTTPS Everywhere to 3.5.3
   * Bug 12673: Update FTE bridges
   * Update Torbutton to ********
     * Bug 12221: Remove obsolete Javascript components from the toggle era
     * Bug 10819: Bind new third party isolation pref to Torbutton security UI
     * Bug 9268: Fix some window resizing corner cases with DPI and taskbar size.
 * Linux:
   * Bug 11102: Set Window Class to "Tor Browser" to aid in Desktop navigation
   * Bug 12249: Don't create PT debug files anymore

Tor Browser Bundle 3.6.2 -- Jun 9 2014
 * All Platforms
   * Update Firefox to 24.6.0esr
   * Update OpenSSL to 1.0.1h
   * Update NoScript to ********
   * Update Tor to ********
   * Update Tor Launcher to *******
     * Bug 10425: Provide geoip6 file location to Tor process
     * Bug 11754: Remove untranslated locales that were dropped from Transifex
     * Bug 11772: Set Proxy Type menu correctly after restart
     * Bug 11699: Change &amp;#160 to &#160; in UI elements
   * Update Torbutton to ********
     * Bug 11510: about:tor should not report success if tor proxy is unreachable
     * Bug 11783: Avoid b.webProgress error when double-clicking on New Identity
     * Bug 11722: Add hidden pref to force remote Tor check
     * Bug 11763: Fix pref dialog double-click race that caused settings to be reset
   * Bug 11629: Support proxies with Pluggable Transports
     * Updates FTEProxy to 0.2.15
     * Updates obfsproxy to 0.2.9
   * Backported Tor Patches:
     * Bug 11654: Fix malformed log message in bug11156 patch.
   * Bug 10425: Add in Tor's geoip6 files to the bundle distribution
   * Bugs 11834 and 11835: Include Pluggable Transport documentation
   * Bug 9701: Prevent ClipBoardCache from writing to disk.
   * Bug 12146: Make the CONNECT Host header the same as the Request-URI.
   * Bug 12212: Disable deprecated webaudio API
   * Bug 11253: Turn on TLS 1.1 and 1.2.
   * Bug 11817: Don't send startup time information to Mozilla.

Tor Browser Bundle 3.6.1 -- May 6 2014
 * All Platforms
   * Update HTTPS-Everywhere to 3.5.1
   * Update NoScript to *******2
   * Bug 11658: Fix proxy configuration for non-Pluggable Transports users
   * Backport Pending Tor Patches:
     * Bug 8402: Allow Tor proxy configuration while PTs are present
   * Note: The Pluggable Transports themselves have not been updated to
           support proxy configuration yet.

Tor Browser Bundle 3.6 -- Apr 29 2014
 * All Platforms
   * Update Firefox to 24.5.0esr
   * Update Tor Launcher to *******
     * Bug #11482: Hide bridge settings prompt if no default bridges.
     * Bug #11484: Show help button even if no default bridges.
   * Update Torbutton to *******
     * Bug 7439: Improve download warning dialog text.
     * Bug 11384: Completely remove hidden toggle menu item.
   * Update NoScript to *******0
   * Update fte transport to 0.2.13
   * Backport Pending Tor Patches:
     * Bug 11156: Additional obfsproxy startup error message fixes
   * Bug 11586: Include license files for component software in Docs directory.
 * Windows and Mac:
   * Bug 9308: Prevent install path from leaking in some JS exceptions
               on Mac and Windows builds

Tor Browser Bundle 3.6-beta-2 -- Apr 8 2014
 * All Platforms
   * Update OpenSSL to 1.0.1g
   * Bug 9010: Add Turkish language support.
   * Bug 9387 testing: Disable JS JIT, type inference, asmjs, and ion.
   * Update fte transport to 0.2.12
   * Update NoScript to ********
   * Update Torbutton to *******
     * Bug 11242: Fix improper "update needed" message after in-place upgrade.
     * Bug 10398: Ease translation of about:tor page elements
   * Update Tor Launcher to *******
     * Bug 9665: Localize Tor's unreachable bridges bootstrap error
   * Backport Pending Tor Patches:
     * Bug 9665: Report a bootstrap error if all bridges are unreachable
     * Bug 11200: Prevent spurious error message prior to enabling network.
 * Linux:
   * Bug 11190: Switch linux PT build process to python2
   * Bug 10383: Enable NIST P224 and P256 accel support for 64bit builds.
 * Windows:
   * Bug 11286: Fix fte transport launch error

Tor Browser Bundle 3.5.4 -- Apr 7 2014
 * All Platforms
   * Update OpenSSL to 1.0.1g

Tor Browser Bundle 3.5.3 -- Mar 19 2014
 * All Platforms
   * Update Firefox to 24.4.0esr
   * Update Torbutton to *******:
     * Bug 9901: Fix browser freeze due to content type sniffing
     * Bug 10611: Add Swedish (sv) to extra locales to update
   * Update NoScript to ********
   * Update Tor to ********
   * Bug 10237: Disable the media cache to prevent disk leaks for videos
   * Bug 10703: Force the default charset to avoid locale fingerprinting
   * Bug 10104: Update gitian to fix LXC build issues (for non-KVM/VT builders)
 * Linux:
   * Bug 9353: Fix keyboard input on Ubuntu 13.10
   * Bug 9896: Provide debug symbols for Tor Browser binary
   * Bug 10472: Pass arguments to the browser from Linux startup script

Tor Browser Bundle 3.6-beta-1 -- Mar 17 2014
 * All Platforms
   * Update Firefox to 24.4.0esr
   * Include Pluggable Transports by default:
     * Obfsproxy3 0.2.4, Flashproxy 1.6, and FTE 0.2.6 are now included
   * Update Tor Launcher to *******
     * Bug 10418: Provide UI configuration for Pluggable Transports
     * Bug 10604: Allow Tor status & error messages to be translated
     * Bug 10894: Make bridge UI clear that helpdesk is a last resort for
                  bridges
     * Bug 10610: Clarify wizard UI text describing obstacles/blocking
     * Bug 11074: Support Tails use case (XULRunner and optional
                  customizations)
   * Update Torbutton to *******:
     * Bug 9901: Fix browser freeze due to content type sniffing
     * Bug 10611: Add Swedish (sv) to extra locales to update
   * Update NoScript to ********
   * Update Tor to ********
   * Backport Pending Tor Patches:
     * Bug 5018: Don't launch Pluggable Transport helpers if not in use
     * Bug 9229: Eliminate 60 second stall during bootstrap with some PTs
     * Bug 11069: Detect and report Pluggable Transport bootstrap failures
     * Bug 11156: Prevent spurious warning about missing pluggable transports
   * Bug 10237: Disable the media cache to prevent disk leaks for videos
   * Bug 10703: Force the default charset to avoid locale fingerprinting
   * Bug 10104: Update gitian to fix LXC build issues (for non-KVM/VT builders)
 * Mac:
   * Bug 4261: Use DMG instead of ZIP for Mac packages
 * Linux:
   * Bug 9353: Fix keyboard input on Ubuntu 13.10
   * Bug 9896: Provide debug symbols for Tor Browser binary
   * Bug 10472: Pass arguments to the browser from Linux startup script

Tor Browser Bundle ******* -- Feb 14 2014
 * All Platforms
   * Bug 10895: Fix broken localized bundles
 * Windows:
   * Bug 10323: Remove unneeded gcc/libstdc++ libraries from dist

Tor Browser Bundle 3.5.2 -- Feb 8 2014
 * All Platforms
   * Rebase Tor Browser to Firefox 24.3.0ESR
   * Bug 10419: Block content window connections to localhost
   * Update Torbutton to *******
     * Bug 10800: Prevent findbox exception and popup in New Identity
     * Bug 10640: Fix about:tor's update pointer position for RTL languages.
     * Bug 10095: Fix some cases where resolution is not a multiple of 200x100
     * Bug 10374: Clear site permissions on New Identity
     * Bug 9738: Fix for auto-maximizing on browser start
     * Bug 10682: Workaround to really disable updates for Torbutton
     * Bug 10419: Don't allow connections to localhost if Torbutton is toggled
     * Bug 10140: Move Japanese to extra locales (not part of TBB dist)
     * Bug 10687: Add Basque (eu) to extra locales (not part of TBB dist)
   * Update Tor Launcher to *******
     * Bug 10682: Workaround to really disable updates for Tor Launcher
   * Update NoScript to ********

Tor Browser Bundle 3.5.1 -- Jan 22 2014
 * All Platforms
   * Bug 10447: Remove SocksListenAddress to allow multiple socks ports.
   * Bug 10464: Remove addons.mozilla.org from NoScript whitelist
   * Bug 10537: Build an Arabic version of TBB 3.5
   * Update Torbutton to *******
     * Bug 9486: Clear NoScript Temporary Permissions on New Identity
     * Include Arabic translations
   * Update Tor Launcher to *******
     * Include Arabic translations
   * Update Tor to ********
   * Update OpenSSL to 1.0.1f
   * Update NoScript to ********
   * Update HTTPS-Everywhere to 3.4.5
 * Windows
   * Bug 9259: Enable Accessibility (screen reader) support
 * Mac
   * misc: Update bundle version field in Info.plist (for MacUpdates service)

Tor Browser Bundle 3.5 -- Dec 17 2013
 * All Platforms
   * Update Tor to *******9
   * Update Tor Launcher to *******
     * Bug 10382: Fix a Tor Launcher hang on TBB exit
   * Update Torbutton to *******
     * Misc: Switch update download URL back to download-easy

Tor Browser Bundle 3.5rc1 -- Dec 12 2013
 * All Platforms
   * Update Firefox to 24.2.0esr
   * Update NoScript to *******
   * Update HTTPS-Everywhere to 3.4.4tbb (special TBB tag)
     * Tag includes a patch to handle enabling/disabling Mixed Content Blocking
   * Bug 5060: Disable health report service
   * Bug 10367: Disable prompting about health report and Mozilla Sync
   * Misc Prefs: Disable HTTPS-Everywhere first-run tooltips
   * Misc Prefs: Disable layer acceleration to avoid crashes on Windows
   * Misc Prefs: Disable Mixed Content Blocker pending backport of Mozilla Bug 878890
   * Update Tor Launcher to *******
     * Bug 10147: Adblock Plus interferes w/Tor Launcher dialog
     * Bug 10201: FF ESR 24 hangs during exit on Mac OS
     * Bug 9984: Support running Tor Launcher from InstantBird
     * Misc: Support browser directory location API changes in Firefox 24
   * Update Torbutton to *******
     * Bug 10352: Clear FF24 Private Browsing Mode data during New Identity
     * Bug 8167: Update cache isolation for FF24 API changes
     * Bug 10201: FF ESR 24 hangs during exit on Mac OS
     * Bug 10078: Properly clear crypto tokens during New Identity on FF24
     * Bug 9454: Support changes to Private Browsing Mode and plugin APIs in FF24
 * Linux
   * Bug 10213; Use LD_LIBRARY_PATH (fixes launch issues on old Linux distros)

Tor Browser Bundle 3.0rc1 -- Nov 21 2013
 * All Platforms:
   * Update Firefox to 17.0.11esr
   * Update Tor to *******8-rc
   * Remove unsupported PDF.JS addon from the bundle
   * Bug #7277: TBB's Tor client will now omit its timestamp in the TLS handshake.
   * Update Torbutton to *******
     * Bug #10002: Make the TBB3.0 blog tag our update download URL for now
 * Windows
   * Bug #10102: Patch binutils to remove nondeterministic bytes in compiled binaries
 * Linux
   * Bug #10049: Fix architecture check to work from outside TBB's directory
   * Bug #10126: Remove libz and firefox-bin, and strip unstripped binaries
   * Misc: Disable Firefox updater during compile time (in addition to pref)

Tor Browser Bundle 3.0beta1 -- Oct 31 2013
 * All Platforms:
   * Update Firefox to 17.0.10esr
   * Update NoScript to *******
   * Update HTTPS-Everywhere to 3.4.2
   * Bug #9114: Reorganize the bundle directory structure to ease future
                autoupdates
   * Bug #9173: Patch Tor Browser to auto-detect profile directory if
                launched without the wrapper script.
   * Bug #9012: Hide Tor Browser infobar for missing plugins.
   * Bug #8364: Change the default entry page for the addons tab to the
                installed addons page.
   * Bug #9867: Make flash objects really be click-to-play if flash is enabled.
   * Bug #8292: Make getFirstPartyURI log+handle errors internally to simplify
                caller usage of the API
   * Bug #3661: Remove polipo and privoxy from the banned ports list.
   * misc: Fix a potential memory leak in the Image Cache isolation
   * misc: Fix a potential crash if OS theme information is ever absent
   * Update Tor-Launcher to *******-beta
     * Bug #9114: Handle new directory structure
     * misc: Tor Launcher now supports Thunderbird
   * Update Torbutton to 1.6.4
     * Bug #9224: Support multiple Tor socks ports for about:tor status check
     * Bug #9587: Add TBB version number to about:tor
     * Bug #9144: Workaround to handle missing translation properties
 * Windows:
   * Bug #9084: Fix startup crash on Windows XP.
 * Linux:
   * Bug #9487: Create detached debuginfo files for Linux Tor and Tor
                Browser binaries.

Tor Browser Bundle 3.0alpha4 -- Sep 24 2013
 * All Platforms:
   * Bug #8751: Randomize TLS HELLO timestamp in HTTPS connections
   * Bug #9790 (workaround): Temporarily re-enable JS-Ctypes for cache
                             isolation and SSL Observatory
   * Update Firefox to 17.0.9esr
   * Update Tor to *******7-rc
   * Update NoScript to *******
   * Update Tor-Launcher to 0.2.2-alpha
     * Bug #9675: Provide feedback mechanism for clock-skew and other early
                  startup issues
     * Bug #9445: Allow user to enter bridges with or without 'bridge' keyword
     * Bug #9593: Use UTF16 for Tor process launch to handle unicode paths.
     * misc: Detect when Tor exits and display appropriate notification
   * Update Torbutton to *******
     * Bug 9492: Fix Torbutton logo on OSX and Windows (and related
                 initialization code)
     * Bug 8839: Disable Google/Startpage search filters using Tor-specific urls


Tor Browser Bundle 3.0alpha3 -- Aug 01 2013
 * All Platforms:
   * Update Firefox to 17.0.8esr
   * Update Tor to *******5-rc
   * Update HTTPS-Everywhere to 3.3.1
   * Update NoScript to *******
   * Improve build input fetching and authentication
   * Bug #9283: Update NoScript prefs for usability.
   * Bug #6152 (partial): Disable JSCtypes support at compile time
   * Update Torbutton to 1.6.1
     * Bug 8478: Change when window resize code fires to avoid rounding errors
     * Bug 9331: Hack an update URL for the next TBB release
     * Bug 9144: Change an aboutTor.dtd string so transifex will accept it
   * Update Tor-Launcher to 0.2.1-alpha
     * Bug #9128: Remove dependency on JSCtypes
 * Windows
   * Bug #9195: Disable download manager AV scanning (to prevent cloud
     reporting+scanning of downloaded files)
 * Mac:
   * Bug #9173 (partial): Launch firefox-bin on MacOS instead of TorBrowser.app
     (improves dock behavior).


Tor Browser Bundle 3.0alpha2 -- June 27 2013
 * All Platforms:
   * Update Firefox to 17.0.7esr
   * Update Tor to *******4-alpha
   * Include Tor's GeoIP file
     * This should fix custom torrc issues with country-based node
       restrictions
   * Fix several build determinism issues
   * Include ChangeLog in bundles.
 * Linux:
   * Use Ubuntu's 'hardening-wrapper' to build our Linux binaries
 * Windows:
   * Fix many crash issues by disabling Direct2D support for now.
 * Mac:
   * Bug 8987: Disable TBB's 'Saved Application State' disk records on OSX 10.7+

Tor Browser Bundle 3.0alpha1 -- June 17 2013
 * All Platforms:
   * Remove Vidalia; Use the new Tor Launcher Firefox Addon instead
   * Update Torbutton to 1.6.0
     * bug 7494: Create a local home page for TBB as about:tor
     * misc: Perform a control port test of proper Tor configuration by default.
             Only use https://check.torproject.org if the control port is
             unavailable.
     * misc: Add an icon menu option for Tor Launcher's Network Settings
     * misc: Add branding string overrides (primarily controls browser name and
             homepage)
   * Update HTTPS-Everywhere to 3.2.2
   * Update NoScript to *******
   * Update PDF.JS to 0.8.1
 * Windows:
   * Use MinGW-w64 (via Gitian) to cross-compile the bundles from Ubuntu
   * Use TBB-Windows-Installer to guide Windows users through TBB extraction
   * Temporarily disable WebGL and Accessibility support due to minor MinGW
     issues
 * Mac:
   * Use 'Toolchain4' fork by Ray Donnelley to cross-compile the bundles from
     Ubuntu
