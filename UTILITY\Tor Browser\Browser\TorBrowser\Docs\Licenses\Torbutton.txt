Copyright (c) 2006 <PERSON> Squires, Oleg <PERSON>ov

Permission is hereby granted, free of charge, to any person obtaining a
copy of this software and associated documentation files (the "Software"),
to deal in the Software without restriction, including without limitation
the rights to use, copy, modify, merge, publish, distribute, sublicense,
and/or sell copies of the Software, and to permit persons to whom the
Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included
in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL
THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR
OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE,
ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
OTHER DEALINGS IN THE SOFTWARE.

--------------------------------------------------------------------------------

Date: Fri, 3 Mar 2006 03:16:21 +0200
From: Oleg Ivanov
To: Scott Squires
Subject: Re: ProxyButton licensing question

Hello Scott,

I'm glad to support your project so you can use the Proxybutton in any way you
need under any open source license as it's stated in mozdev's copyright policy.
I'll just ask you to put in the Tor or it's source code any credits with
references to me and the original Proxybutton.  Feel free to ask if you have
any questions regarding the extension - I'll be glad to help you.

On Thursday 02 March 2006 05:01, you wrote:
> Hello,
>
> I am a volunteer for the Tor project, which is a network proxy with strong
> anonymnity.  We would like to make it easier for users to install and
> configure the software, and would like a firefox button to enable/disable
> Tor.  Your extension is very close to what we need, our version would just
> set the proxy for the user instead of the user needing to enter the
> information.  So I was wondering what license your software is released under
> and whether we can use it as a base for this extension.
>
> Thanks!
> --Scott

-- 
Oleg Ivanov
mailto: <EMAIL>
ICQ #69991809
