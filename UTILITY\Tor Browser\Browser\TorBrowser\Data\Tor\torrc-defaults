# torrc-defaults for Tor Browser
#
# DO NOT EDIT THIS FILE
#
# This file is distributed with Tor Browser and SHOULD NOT be modified (it
# may be overwritten during the next Tor Browser update). To customize your
# Tor configuration, shut down Tor Browser and edit the torrc file.
#
# If non-zero, try to write to disk less frequently than we would otherwise.
AvoidDiskWrites 1
# Where to send logging messages.  Format is minSeverity[-maxSeverity]
# (stderr|stdout|syslog|file FILENAME).
Log notice stdout
CookieAuthentication 1
DormantCanceledByStartup 1
## obfs4proxy configuration
ClientTransportPlugin meek_lite,obfs2,obfs3,obfs4,scramblesuit exec TorBrowser\Tor\PluggableTransports\obfs4proxy.exe

## snowflake configuration
ClientTransportPlugin snowflake exec TorBrowser\Tor\PluggableTransports\snowflake-client.exe
