PATCH DIRECTORY C:\Users\<USER>\Desktop\WEL<PERSON>ME BACK\UTILITY\Tor Browser\Browser\TorBrowser\UpdateInfo\updates\0
INSTALLATION DIRECTORY C:\Users\<USER>\Desktop\WELCOME BACK\UTILITY\Tor Browser\Browser
WORKING DIRECTORY C:\Users\<USER>\Desktop\WELCOME BACK\UTILITY\Tor Browser\Browser
UPDATE TYPE partial
PREPARE PATCH xul.dll
PREPARE PATCH updater.exe
PREPARE PATCH tbb_version.json
PREPARE PATCH softokn3.dll
PREPARE ADD removed-files
PREPARE PATCH qipcap64.dll
PREPARE ADD precomplete
PREPARE PATCH plugin-container.exe
PREPARE PATCH platform.ini
PREPARE PATCH osclientcerts.dll
PREPARE PATCH omni.ja
PREPARE PATCH nssckbi.dll
PREPARE PATCH nss3.dll
PREPARE PATCH mozglue.dll
PREPARE PATCH mozavutil.dll
PREPARE PATCH mozavcodec.dll
PREPARE PATCH libGLESv2.dll
PREPARE PATCH libEGL.dll
PREPARE PATCH lgpllibs.dll
PREPARE PATCH freebl3.dll
PREPARE PATCH firefox.exe
PREPARE PATCH browser/omni.ja
PREPARE PATCH application.ini
PREPARE PATCH TorBrowser/Tor/zlib1.dll
PREPARE PATCH TorBrowser/Tor/tor.exe
PREPARE PATCH TorBrowser/Tor/libssl-1_1-x64.dll
PREPARE PATCH TorBrowser/Tor/libcrypto-1_1-x64.dll
PREPARE PATCH TorBrowser/Tor/PluggableTransports/snowflake-client.exe
PREPARE PATCH TorBrowser/Tor/PluggableTransports/obfs4proxy.exe
PREPARE PATCH TorBrowser/Docs/ChangeLog.txt
PREPARE PATCH TorBrowser/Data/Tor/geoip6
PREPARE PATCH TorBrowser/Data/Tor/geoip
PREPARE ADD TorBrowser/Data/Browser/profile.default/extensions/{73a6fe31-595d-460b-a920-fcc0f8843232}.xpi
PREPARE PATCH IA2Marshal.dll
PREPARE PATCH AccessibleMarshal.dll
PREPARE PATCH AccessibleHandler.dll
EXECUTE PATCH xul.dll
EXECUTE PATCH updater.exe
EXECUTE PATCH tbb_version.json
EXECUTE PATCH softokn3.dll
EXECUTE ADD removed-files
EXECUTE PATCH qipcap64.dll
EXECUTE ADD precomplete
EXECUTE PATCH plugin-container.exe
EXECUTE PATCH platform.ini
EXECUTE PATCH osclientcerts.dll
EXECUTE PATCH omni.ja
EXECUTE PATCH nssckbi.dll
EXECUTE PATCH nss3.dll
EXECUTE PATCH mozglue.dll
EXECUTE PATCH mozavutil.dll
EXECUTE PATCH mozavcodec.dll
EXECUTE PATCH libGLESv2.dll
EXECUTE PATCH libEGL.dll
EXECUTE PATCH lgpllibs.dll
EXECUTE PATCH freebl3.dll
EXECUTE PATCH firefox.exe
EXECUTE PATCH browser/omni.ja
EXECUTE PATCH application.ini
EXECUTE PATCH TorBrowser/Tor/zlib1.dll
EXECUTE PATCH TorBrowser/Tor/tor.exe
EXECUTE PATCH TorBrowser/Tor/libssl-1_1-x64.dll
EXECUTE PATCH TorBrowser/Tor/libcrypto-1_1-x64.dll
EXECUTE PATCH TorBrowser/Tor/PluggableTransports/snowflake-client.exe
EXECUTE PATCH TorBrowser/Tor/PluggableTransports/obfs4proxy.exe
EXECUTE PATCH TorBrowser/Docs/ChangeLog.txt
EXECUTE PATCH TorBrowser/Data/Tor/geoip6
EXECUTE PATCH TorBrowser/Data/Tor/geoip
EXECUTE ADD TorBrowser/Data/Browser/profile.default/extensions/{73a6fe31-595d-460b-a920-fcc0f8843232}.xpi
EXECUTE PATCH IA2Marshal.dll
EXECUTE PATCH AccessibleMarshal.dll
EXECUTE PATCH AccessibleHandler.dll
FINISH PATCH xul.dll
FINISH PATCH updater.exe
ensure_remove: failed to remove file: C:\Users\<USER>\Desktop\WELCOME BACK\UTILITY\Tor Browser\Browser/updater.exe.moz-backup, rv: -1, err: 13
backup_discard: unable to remove: updater.exe.moz-backup
FINISH PATCH tbb_version.json
FINISH PATCH softokn3.dll
ensure_remove: failed to remove file: C:\Users\<USER>\Desktop\WELCOME BACK\UTILITY\Tor Browser\Browser/softokn3.dll.moz-backup, rv: -1, err: 13
backup_discard: unable to remove: softokn3.dll.moz-backup
FINISH ADD removed-files
FINISH PATCH qipcap64.dll
FINISH ADD precomplete
FINISH PATCH plugin-container.exe
FINISH PATCH platform.ini
FINISH PATCH osclientcerts.dll
FINISH PATCH omni.ja
FINISH PATCH nssckbi.dll
FINISH PATCH nss3.dll
ensure_remove: failed to remove file: C:\Users\<USER>\Desktop\WELCOME BACK\UTILITY\Tor Browser\Browser/nss3.dll.moz-backup, rv: -1, err: 13
backup_discard: unable to remove: nss3.dll.moz-backup
FINISH PATCH mozglue.dll
ensure_remove: failed to remove file: C:\Users\<USER>\Desktop\WELCOME BACK\UTILITY\Tor Browser\Browser/mozglue.dll.moz-backup, rv: -1, err: 13
backup_discard: unable to remove: mozglue.dll.moz-backup
FINISH PATCH mozavutil.dll
FINISH PATCH mozavcodec.dll
FINISH PATCH libGLESv2.dll
FINISH PATCH libEGL.dll
FINISH PATCH lgpllibs.dll
FINISH PATCH freebl3.dll
ensure_remove: failed to remove file: C:\Users\<USER>\Desktop\WELCOME BACK\UTILITY\Tor Browser\Browser/freebl3.dll.moz-backup, rv: -1, err: 13
backup_discard: unable to remove: freebl3.dll.moz-backup
FINISH PATCH firefox.exe
FINISH PATCH browser/omni.ja
FINISH PATCH application.ini
FINISH PATCH TorBrowser/Tor/zlib1.dll
FINISH PATCH TorBrowser/Tor/tor.exe
FINISH PATCH TorBrowser/Tor/libssl-1_1-x64.dll
FINISH PATCH TorBrowser/Tor/libcrypto-1_1-x64.dll
FINISH PATCH TorBrowser/Tor/PluggableTransports/snowflake-client.exe
FINISH PATCH TorBrowser/Tor/PluggableTransports/obfs4proxy.exe
FINISH PATCH TorBrowser/Docs/ChangeLog.txt
FINISH PATCH TorBrowser/Data/Tor/geoip6
FINISH PATCH TorBrowser/Data/Tor/geoip
FINISH ADD TorBrowser/Data/Browser/profile.default/extensions/{73a6fe31-595d-460b-a920-fcc0f8843232}.xpi
FINISH PATCH IA2Marshal.dll
FINISH PATCH AccessibleMarshal.dll
FINISH PATCH AccessibleHandler.dll
succeeded
calling QuitProgressUI
NS_main: unable to remove directory: tobedeleted, err: 41
