// Mozilla User Preferences

// DO NOT EDIT THIS FILE.
//
// If you make changes to this file while the application is running,
// the changes will be overwritten when the application exits.
//
// To change a preference value, you can either:
// - modify it via the UI (e.g. via about:config in the browser); or
// - set it within a user.js file in your profile.

user_pref("app.update.background.previous.reasons", "[\"app.update.background.enabled=false\",\"on Windows but cannot usually use BITS\",\"the maintenance service registry key is not present\"]");
user_pref("app.update.download.attempts", 0);
user_pref("app.update.elevate.attempts", 0);
user_pref("app.update.lastUpdateTime.addon-background-update-timer", 1675622478);
user_pref("app.update.lastUpdateTime.background-update-timer", 1675622118);
user_pref("app.update.lastUpdateTime.browser-cleanup-thumbnails", 1675621878);
user_pref("app.update.lastUpdateTime.search-engine-update-timer", 1675621998);
user_pref("app.update.lastUpdateTime.services-settings-poll-changes", **********);
user_pref("app.update.lastUpdateTime.xpi-signature-verification", 1675622238);
user_pref("browser.bookmarks.defaultLocation", "unfiled");
user_pref("browser.bookmarks.editDialog.confirmationHintShowCount", 3);
user_pref("browser.cache.disk.amount_written", 0);
user_pref("browser.cache.disk.capacity", 1048576);
user_pref("browser.cache.disk.filesystem_reported", 1);
user_pref("browser.contentblocking.category", "standard");
user_pref("browser.download.viewableInternally.typeWasRegistered.avif", true);
user_pref("browser.download.viewableInternally.typeWasRegistered.svg", true);
user_pref("browser.download.viewableInternally.typeWasRegistered.webp", true);
user_pref("browser.download.viewableInternally.typeWasRegistered.xml", true);
user_pref("browser.laterrun.bookkeeping.profileCreationTime", 1640126151);
user_pref("browser.laterrun.bookkeeping.sessionCount", 3);
user_pref("browser.launcherProcess.enabled", true);
user_pref("browser.migration.version", 128);
user_pref("browser.newtabpage.activity-stream.impressionId", "{355c50e3-5f5d-436b-92b6-9e85ab5dd24d}");
user_pref("browser.newtabpage.storageVersion", 1);
user_pref("browser.onboarding.seen-tourset-version", 5);
user_pref("browser.onboarding.tour-type", "new");
user_pref("browser.pageActions.persistedActions", "{\"version\":1,\"ids\":[\"bookmark\"],\"idsInUrlbar\":[\"bookmark\"],\"idsInUrlbarPreProton\":[\"bookmark\"]}");
user_pref("browser.pagethumbnails.storage_version", 3);
user_pref("browser.places.importBookmarksHTML", false);
user_pref("browser.proton.toolbar.version", 3);
user_pref("browser.rights.3.shown", true);
user_pref("browser.security_level.noscript_inited", true);
user_pref("browser.security_level.security_custom", false);
user_pref("browser.security_level.security_slider", 4);
user_pref("browser.security_level.security_slider_migration", 2);
user_pref("browser.startup.couldRestoreSession.count", 1);
user_pref("browser.startup.homepage_override.buildID", "20230702030101");
user_pref("browser.startup.homepage_override.mstone", "102.7.0");
user_pref("browser.startup.homepage_override.torbrowser.version", "12.0.2");
user_pref("browser.startup.lastColdStartupCheck", 1675621851);
user_pref("browser.theme.toolbar-theme", 0);
user_pref("browser.uiCustomization.state", "{\"placements\":{\"widget-overflow-fixed-list\":[],\"nav-bar\":[\"back-button\",\"forward-button\",\"stop-reload-button\",\"urlbar-container\",\"torbutton-button\",\"security-level-button\",\"new-identity-button\",\"downloads-button\",\"fxa-toolbar-menu-button\"],\"toolbar-menubar\":[\"menubar-items\"],\"TabsToolbar\":[\"tabbrowser-tabs\",\"new-tab-button\",\"alltabs-button\"],\"PersonalToolbar\":[\"personal-bookmarks\"],\"PanelUI-contents\":[\"home-button\",\"edit-controls\",\"zoom-controls\",\"new-window-button\",\"save-page-button\",\"print-button\",\"bookmarks-menu-button\",\"history-panelmenu\",\"find-button\",\"preferences-button\",\"add-ons-button\",\"developer-button\"],\"addon-bar\":[\"addonbar-closebutton\",\"status-bar\"]},\"seen\":[\"developer-button\",\"https-everywhere-eff_eff_org-browser-action\",\"_73a6fe31-595d-460b-a920-fcc0f8843232_-browser-action\"],\"dirtyAreaCache\":[\"PersonalToolbar\",\"nav-bar\",\"TabsToolbar\",\"toolbar-menubar\"],\"currentVersion\":17,\"newElementCount\":1}");
user_pref("browser.urlbar.placeholderName.private", "DuckDuckGo");
user_pref("browser.urlbar.resultGroups", "{\"children\":[{\"maxResultCount\":1,\"children\":[{\"group\":\"heuristicTest\"},{\"group\":\"heuristicExtension\"},{\"group\":\"heuristicSearchTip\"},{\"group\":\"heuristicOmnibox\"},{\"group\":\"heuristicEngineAlias\"},{\"group\":\"heuristicBookmarkKeyword\"},{\"group\":\"heuristicAutofill\"},{\"group\":\"heuristicPreloaded\"},{\"group\":\"heuristicTokenAliasEngine\"},{\"group\":\"heuristicFallback\"}]},{\"group\":\"extension\",\"availableSpan\":5},{\"flexChildren\":true,\"children\":[{\"children\":[{\"flexChildren\":true,\"children\":[{\"flex\":2,\"group\":\"formHistory\"},{\"flex\":4,\"group\":\"remoteSuggestion\"}]},{\"group\":\"tailSuggestion\"}],\"flex\":2},{\"group\":\"generalParent\",\"children\":[{\"availableSpan\":3,\"group\":\"inputHistory\"},{\"flexChildren\":true,\"children\":[{\"flex\":1,\"group\":\"remoteTab\"},{\"flex\":2,\"group\":\"general\"},{\"flex\":2,\"group\":\"aboutPages\"},{\"flex\":1,\"group\":\"preloaded\"}]},{\"group\":\"inputHistory\"}],\"flex\":1}]}]}");
user_pref("devtools.everOpened", true);
user_pref("devtools.toolsidebar-height.inspector", 350);
user_pref("devtools.toolsidebar-width.inspector", 700);
user_pref("devtools.toolsidebar-width.inspector.splitsidebar", 350);
user_pref("distribution.iniFile.exists.appversion", "102.7.0");
user_pref("distribution.iniFile.exists.value", false);
user_pref("doh-rollout.doneFirstRun", true);
user_pref("doh-rollout.home-region", "US");
user_pref("doh-rollout.uri", "https://mozilla.cloudflare-dns.com/dns-query");
user_pref("dom.security.https_only_mode_ever_enabled", true);
user_pref("dom.security.https_only_mode_ever_enabled_pbm", true);
user_pref("extensions.activeThemeID", "<EMAIL>");
user_pref("extensions.blocklist.pingCountVersion", -1);
user_pref("extensions.databaseSchema", 35);
user_pref("extensions.incognito.migrated", true);
user_pref("extensions.lastAppBuildId", "20230702030101");
user_pref("extensions.lastAppVersion", "102.7.0");
user_pref("extensions.lastPlatformVersion", "102.7.0");
user_pref("extensions.lastTorBrowserVersion", "12.0.2");
user_pref("extensions.systemAddonSet", "{\"schema\":1,\"addons\":{}}");
user_pref("extensions.torbutton.cookiejar_migrated", true);
user_pref("extensions.torbutton.pref_fixup_for_12", true);
user_pref("extensions.torbutton.pref_fixup_version", 1);
user_pref("extensions.torbutton.security_slider_migration", 2);
user_pref("extensions.torbutton.startup", true);
user_pref("extensions.torlauncher.prompt_at_startup", false);
user_pref("extensions.torlauncher.should_remove_meek_helper_profiles", false);
user_pref("extensions.torlauncher.torrc_fixup_version", 2);
user_pref("extensions.ui.dictionary.hidden", true);
user_pref("extensions.ui.extension.hidden", false);
user_pref("extensions.ui.locale.hidden", true);
user_pref("<EMAIL>", true);
user_pref("extensions.webextensions.ExtensionStorageIDB.migrated.{73a6fe31-595d-460b-a920-fcc0f8843232}", true);
user_pref("extensions.webextensions.uuids", "{\"{73a6fe31-595d-460b-a920-fcc0f8843232}\":\"7f1ed3db-855c-462b-ba9f-7df110695485\",\"<EMAIL>\":\"9a951c46-0f37-41c8-b5b5-68283c206cf4\",\"<EMAIL>\":\"015f133c-dc18-4c34-a364-ac3d8de303dc\",\"<EMAIL>\":\"8d369c75-c10b-45ad-a7fd-f883e20f5cf8\",\"<EMAIL>\":\"ee228666-7995-4cb2-8935-0635ea0d2593\",\"<EMAIL>\":\"965de1f5-17f7-4012-a7e5-1d9bc6217e9d\",\"<EMAIL>\":\"213552bf-f4b3-44d7-b221-9ac75e3884e6\",\"<EMAIL>\":\"68c448d2-13df-4b35-af13-94162fd7913f\",\"<EMAIL>\":\"7e7c8dc9-c900-4f78-8013-1b7efc39c5b9\",\"<EMAIL>\":\"9ef9efd2-17d0-4d4b-af05-4336f5c391bb\",\"<EMAIL>\":\"b4ecc2d7-14a6-4f5e-b244-b7f382356074\",\"<EMAIL>\":\"d97f11ad-ddef-44d8-ae4d-f626cfa135e9\",\"<EMAIL>\":\"54dbcae1-12e3-446b-a318-a2c9ff1d4b9d\",\"<EMAIL>\":\"2196024d-b9e8-41fa-832e-5ef4d8130bbe\"}");
user_pref("fission.experiment.max-origins.last-disqualified", 1650988220);
user_pref("fission.experiment.max-origins.last-qualified", 1655307141);
user_pref("fission.experiment.max-origins.qualified", true);
user_pref("gecko.handlerService.defaultHandlersVersion", 1);
user_pref("gfx-shader-check.build-version", "20230702030101");
user_pref("gfx-shader-check.device-id", "0x9bca");
user_pref("gfx-shader-check.driver-version", "30.0.101.1340");
user_pref("gfx-shader-check.ptr-size", 8);
user_pref("gfx.crash-guard.status.wmfvpxvideo", 2);
user_pref("gfx.crash-guard.wmfvpxvideo.appVersion", "91.13.0");
user_pref("gfx.crash-guard.wmfvpxvideo.deviceID", "0x9bca");
user_pref("gfx.crash-guard.wmfvpxvideo.driverVersion", "30.0.101.1340");
user_pref("idle.lastDailyNotification", 1675622150);
user_pref("intl.language_notification.shown", true);
user_pref("intl.locale.requested", "en-US");
user_pref("layers.mlgpu.sanity-test-failed", false);
user_pref("media.gmp-manager.buildID", "20230702030101");
user_pref("media.gmp-manager.lastCheck", 1675622110);
user_pref("media.gmp-manager.lastEmptyCheck", 1675622110);
user_pref("media.gmp.storage.version.observed", 1);
user_pref("media.hardware-video-decoding.failed", false);
user_pref("network.trr.blocklist_cleanup_done", true);
user_pref("pdfjs.enabledCache.state", true);
user_pref("pdfjs.migrationVersion", 2);
user_pref("places.database.lastMaintenance", 1675111488);
user_pref("places.history.enabled", false);
user_pref("places.history.expiration.transient_current_max_pages", 112348);
user_pref("privacy.history.custom", true);
user_pref("privacy.prioritizeonions.enabled", true);
user_pref("privacy.prioritizeonions.showNotification", false);
user_pref("privacy.purge_trackers.date_in_cookie_database", "0");
user_pref("privacy.sanitize.pending", "[]");
user_pref("sanity-test.advanced-layers", true);
user_pref("sanity-test.device-id", "0x9bca");
user_pref("sanity-test.driver-version", "30.0.101.1340");
user_pref("sanity-test.running", false);
user_pref("sanity-test.version", "20230702030101");
user_pref("sanity-test.webrender.force-disabled", false);
user_pref("security.sandbox.content.tempDirSuffix", "{f665e6a5-2903-4775-bc6b-bd0fc07b51e9}");
user_pref("security.sandbox.plugin.tempDirSuffix", "{def25ad8-32e4-45a3-b44b-cf5143a3679b}");
user_pref("services.blocklist.addons-mlbf.checked", **********);
user_pref("services.blocklist.gfx.checked", **********);
user_pref("services.settings.blocklists.addons-bloomfilters.last_check", **********);
user_pref("services.settings.blocklists.gfx.last_check", **********);
user_pref("services.settings.clock_skew_seconds", 0);
user_pref("services.settings.last_etag", "\"1675609035397\"");
user_pref("services.settings.last_update_seconds", **********);
user_pref("services.settings.main.addons-manager-settings.last_check", **********);
user_pref("services.settings.main.cfr.last_check", **********);
user_pref("services.settings.main.doh-config.last_check", **********);
user_pref("services.settings.main.doh-providers.last_check", **********);
user_pref("services.settings.main.hijack-blocklists.last_check", **********);
user_pref("services.settings.main.language-dictionaries.last_check", **********);
user_pref("services.settings.main.message-groups.last_check", **********);
user_pref("services.settings.main.password-rules.last_check", **********);
user_pref("services.settings.main.pioneer-study-addons-v1.last_check", **********);
user_pref("services.settings.main.query-stripping.last_check", **********);
user_pref("services.settings.main.search-default-override-allowlist.last_check", **********);
user_pref("services.settings.main.websites-with-shared-credential-backends.last_check", **********);
user_pref("services.settings.main.whats-new-panel.last_check", **********);
user_pref("services.settings.security-state.onecrl.last_check", **********);
user_pref("services.settings.security.onecrl.checked", **********);
user_pref("signon.importedFromSqlite", true);
user_pref("storage.vacuum.last.index", 1);
user_pref("storage.vacuum.last.places.sqlite", 1675111488);
user_pref("toolkit.startup.last_success", 1675621846);
user_pref("toolkit.telemetry.cachedClientID", "684ba8b6-29f8-47d1-b62b-0a6bdccbea96");
user_pref("toolkit.telemetry.pioneer-new-studies-available", true);
user_pref("toolkit.telemetry.reportingpolicy.firstRun", false);
user_pref("torbrowser.migration.version", 1);
user_pref("torbrowser.post_update.url", "https://blog.torproject.org/new-release-tor-browser-1202");
user_pref("torbrowser.settings.bridges.enabled", false);
user_pref("torbrowser.settings.enabled", true);
user_pref("torbrowser.settings.firewall.enabled", false);
user_pref("torbrowser.settings.proxy.enabled", false);
user_pref("torbrowser.settings.quickstart.enabled", true);
user_pref("ui.osk.debug.keyboardDisplayReason", "IKPOS: Touch screen not found.");
