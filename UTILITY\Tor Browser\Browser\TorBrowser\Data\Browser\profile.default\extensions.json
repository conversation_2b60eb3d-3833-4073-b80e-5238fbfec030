{"schemaVersion": 35, "addons": [{"id": "<EMAIL>", "syncGUID": "{b76a1bd2-f1d4-4c1c-b0b9-bd9328716693}", "version": "1.0", "type": "extension", "loader": null, "updateURL": null, "installOrigins": null, "manifestVersion": 2, "optionsURL": null, "optionsType": null, "optionsBrowserStyle": true, "aboutURL": null, "defaultLocale": {"name": "Onboarding", "creator": null, "developers": null, "translators": null, "contributors": null}, "visible": true, "active": true, "userDisabled": false, "appDisabled": false, "embedderDisabled": false, "installDate": ************, "updateDate": 1675621842214, "applyBackgroundUpdates": 1, "path": "C:\\Users\\<USER>\\Desktop\\WELCOME BACK\\UTILITY\\Tor Browser\\Browser\\browser\\features\\<EMAIL>", "skinnable": false, "sourceURI": null, "releaseNotesURI": null, "softDisabled": false, "foreignInstall": false, "strictCompatibility": true, "locales": [], "targetApplications": [{"id": "<EMAIL>", "minVersion": null, "maxVersion": null}], "targetPlatforms": [], "signedDate": null, "seen": true, "dependencies": [], "incognito": "spanning", "userPermissions": {"permissions": [], "origins": []}, "optionalPermissions": {"permissions": [], "origins": []}, "icons": {}, "iconURL": null, "blocklistState": 0, "blocklistURL": null, "startupData": {}, "hidden": true, "installTelemetryInfo": null, "recommendationState": null, "rootURI": "jar:file:///C:/Users/<USER>/Desktop/WELCOME%20BACK/UTILITY/Tor%20Browser/Browser/browser/features/<EMAIL>!/", "location": "app-system-defaults"}, {"id": "<EMAIL>", "syncGUID": "{d4333b7e-6ad5-4cf5-acd4-faf64cceec<PERSON>}", "version": "1.1", "type": "extension", "loader": null, "updateURL": null, "installOrigins": null, "manifestVersion": 2, "optionsURL": null, "optionsType": null, "optionsBrowserStyle": true, "aboutURL": null, "defaultLocale": {"name": "DuckDuckGo", "description": "Search DuckDuckGo", "creator": null, "developers": null, "translators": null, "contributors": null}, "visible": true, "active": true, "userDisabled": false, "appDisabled": false, "embedderDisabled": false, "installDate": 1640126155688, "applyBackgroundUpdates": 1, "path": null, "skinnable": false, "sourceURI": null, "releaseNotesURI": null, "softDisabled": false, "foreignInstall": false, "strictCompatibility": true, "locales": [], "targetApplications": [{"id": "<EMAIL>", "minVersion": null, "maxVersion": null}], "targetPlatforms": [], "signedDate": null, "seen": true, "dependencies": [], "incognito": "spanning", "userPermissions": {"permissions": [], "origins": []}, "optionalPermissions": {"permissions": [], "origins": []}, "icons": {"16": "favicon.png"}, "iconURL": null, "blocklistState": 0, "blocklistURL": null, "startupData": {}, "hidden": true, "installTelemetryInfo": null, "recommendationState": null, "rootURI": "resource://search-extensions/ddg/", "location": "app-builtin"}, {"id": "<EMAIL>", "syncGUID": "{bd37469a-9aec-4696-a6af-b5b3ec8c8afc}", "version": "1.0", "type": "extension", "loader": null, "updateURL": null, "installOrigins": null, "manifestVersion": 2, "optionsURL": null, "optionsType": null, "optionsBrowserStyle": true, "aboutURL": null, "defaultLocale": {"name": "DuckDuckGoOnion", "description": "<PERSON> Duck Go Onion", "creator": null, "developers": null, "translators": null, "contributors": null}, "visible": true, "active": true, "userDisabled": false, "appDisabled": false, "embedderDisabled": false, "installDate": 1640126156218, "applyBackgroundUpdates": 1, "path": null, "skinnable": false, "sourceURI": null, "releaseNotesURI": null, "softDisabled": false, "foreignInstall": false, "strictCompatibility": true, "locales": [], "targetApplications": [{"id": "<EMAIL>", "minVersion": null, "maxVersion": null}], "targetPlatforms": [], "signedDate": null, "seen": true, "dependencies": [], "incognito": "spanning", "userPermissions": {"permissions": [], "origins": []}, "optionalPermissions": {"permissions": [], "origins": []}, "icons": {"16": "favicon.ico"}, "iconURL": null, "blocklistState": 0, "blocklistURL": null, "startupData": {}, "hidden": true, "installTelemetryInfo": null, "recommendationState": null, "rootURI": "resource://search-extensions/ddg-onion/", "location": "app-builtin"}, {"id": "<EMAIL>", "syncGUID": "{e63e711c-1150-4d78-9d64-0591cef5ebe5}", "version": "1.0", "type": "extension", "loader": null, "updateURL": null, "installOrigins": null, "manifestVersion": 2, "optionsURL": null, "optionsType": null, "optionsBrowserStyle": true, "aboutURL": null, "defaultLocale": {"name": "Yahoo", "description": "Yahoo Search", "creator": null, "developers": null, "translators": null, "contributors": null}, "visible": true, "active": true, "userDisabled": false, "appDisabled": false, "embedderDisabled": false, "installDate": 1640126156361, "applyBackgroundUpdates": 1, "path": null, "skinnable": false, "sourceURI": null, "releaseNotesURI": null, "softDisabled": false, "foreignInstall": false, "strictCompatibility": true, "locales": [], "targetApplications": [{"id": "<EMAIL>", "minVersion": null, "maxVersion": null}], "targetPlatforms": [], "signedDate": null, "seen": true, "dependencies": [], "incognito": "spanning", "userPermissions": {"permissions": [], "origins": []}, "optionalPermissions": {"permissions": [], "origins": []}, "icons": {"16": "favicon.ico"}, "iconURL": null, "blocklistState": 0, "blocklistURL": null, "startupData": {}, "hidden": true, "installTelemetryInfo": null, "recommendationState": null, "rootURI": "resource://search-extensions/yahoo/", "location": "app-builtin"}, {"id": "<EMAIL>", "syncGUID": "{5891f6f0-5869-4a3a-bef8-4b17b7122335}", "version": "1.0", "type": "extension", "loader": null, "updateURL": null, "installOrigins": null, "manifestVersion": 2, "optionsURL": null, "optionsType": null, "optionsBrowserStyle": true, "aboutURL": null, "defaultLocale": {"name": "Twitter", "description": "Realtime Twitter Search", "creator": null, "developers": null, "translators": null, "contributors": null}, "visible": true, "active": true, "userDisabled": false, "appDisabled": false, "embedderDisabled": false, "installDate": 1640126156401, "applyBackgroundUpdates": 1, "path": null, "skinnable": false, "sourceURI": null, "releaseNotesURI": null, "softDisabled": false, "foreignInstall": false, "strictCompatibility": true, "locales": [], "targetApplications": [{"id": "<EMAIL>", "minVersion": null, "maxVersion": null}], "targetPlatforms": [], "signedDate": null, "seen": true, "dependencies": [], "incognito": "spanning", "userPermissions": {"permissions": [], "origins": []}, "optionalPermissions": {"permissions": [], "origins": []}, "icons": {"16": "favicon.ico"}, "iconURL": null, "blocklistState": 0, "blocklistURL": null, "startupData": {}, "hidden": true, "installTelemetryInfo": null, "recommendationState": null, "rootURI": "resource://search-extensions/twitter/", "location": "app-builtin"}, {"id": "<EMAIL>", "syncGUID": "{8aca2bf6-414e-4382-83f7-45f727c1308a}", "version": "1.1", "type": "extension", "loader": null, "updateURL": null, "installOrigins": null, "manifestVersion": 2, "optionsURL": null, "optionsType": null, "optionsBrowserStyle": true, "aboutURL": null, "defaultLocale": {"name": "Wikipedia (en)", "description": "Wikipedia, the Free Encyclopedia", "creator": null, "developers": null, "translators": null, "contributors": null}, "visible": true, "active": true, "userDisabled": false, "appDisabled": false, "embedderDisabled": false, "installDate": 1640126156437, "applyBackgroundUpdates": 1, "path": null, "skinnable": false, "sourceURI": null, "releaseNotesURI": null, "softDisabled": false, "foreignInstall": false, "strictCompatibility": true, "locales": [], "targetApplications": [{"id": "<EMAIL>", "minVersion": null, "maxVersion": null}], "targetPlatforms": [], "signedDate": null, "seen": true, "dependencies": [], "incognito": "spanning", "userPermissions": {"permissions": [], "origins": []}, "optionalPermissions": {"permissions": [], "origins": []}, "icons": {"16": "favicon.ico"}, "iconURL": null, "blocklistState": 0, "blocklistURL": null, "startupData": {}, "hidden": true, "installTelemetryInfo": null, "recommendationState": null, "rootURI": "resource://search-extensions/wikipedia/", "location": "app-builtin"}, {"id": "<EMAIL>", "syncGUID": "{5911094e-177f-47b1-ac5e-85eeb1a01c7f}", "version": "1.0", "type": "extension", "loader": null, "updateURL": null, "installOrigins": null, "manifestVersion": 2, "optionsURL": null, "optionsType": null, "optionsBrowserStyle": true, "aboutURL": null, "defaultLocale": {"name": "YouTube", "description": "YouTube - Videos", "creator": null, "developers": null, "translators": null, "contributors": null}, "visible": true, "active": true, "userDisabled": false, "appDisabled": false, "embedderDisabled": false, "installDate": 1640126156476, "applyBackgroundUpdates": 1, "path": null, "skinnable": false, "sourceURI": null, "releaseNotesURI": null, "softDisabled": false, "foreignInstall": false, "strictCompatibility": true, "locales": [], "targetApplications": [{"id": "<EMAIL>", "minVersion": null, "maxVersion": null}], "targetPlatforms": [], "signedDate": null, "seen": true, "dependencies": [], "incognito": "spanning", "userPermissions": {"permissions": [], "origins": []}, "optionalPermissions": {"permissions": [], "origins": []}, "icons": {"16": "favicon.ico"}, "iconURL": null, "blocklistState": 0, "blocklistURL": null, "startupData": {}, "hidden": true, "installTelemetryInfo": null, "recommendationState": null, "rootURI": "resource://search-extensions/youtube/", "location": "app-builtin"}, {"id": "<EMAIL>", "syncGUID": "{37e74e94-b079-44b1-9eae-73b42a2a080d}", "version": "1.0", "type": "extension", "loader": null, "updateURL": null, "installOrigins": null, "manifestVersion": 2, "optionsURL": null, "optionsType": null, "optionsBrowserStyle": true, "aboutURL": null, "defaultLocale": {"name": "Startpage", "description": "Startpage", "creator": null, "developers": null, "translators": null, "contributors": null}, "visible": true, "active": true, "userDisabled": false, "appDisabled": false, "embedderDisabled": false, "installDate": 1640126156512, "applyBackgroundUpdates": 1, "path": null, "skinnable": false, "sourceURI": null, "releaseNotesURI": null, "softDisabled": false, "foreignInstall": false, "strictCompatibility": true, "locales": [], "targetApplications": [{"id": "<EMAIL>", "minVersion": null, "maxVersion": null}], "targetPlatforms": [], "signedDate": null, "seen": true, "dependencies": [], "incognito": "spanning", "userPermissions": {"permissions": [], "origins": []}, "optionalPermissions": {"permissions": [], "origins": []}, "icons": {"16": "favicon.png"}, "iconURL": null, "blocklistState": 0, "blocklistURL": null, "startupData": {}, "hidden": true, "installTelemetryInfo": null, "recommendationState": null, "rootURI": "resource://search-extensions/startpage/", "location": "app-builtin"}, {"id": "<EMAIL>", "syncGUID": "{c7fe3f84-5130-4ea4-9425-75ddd76e44c7}", "version": "1.3", "type": "theme", "loader": null, "updateURL": null, "installOrigins": null, "manifestVersion": 2, "optionsURL": null, "optionsType": null, "optionsBrowserStyle": true, "aboutURL": null, "defaultLocale": {"name": "System theme — auto", "description": "Follow the operating system setting for buttons, menus, and windows.", "creator": "Mozilla", "developers": null, "translators": null, "contributors": null}, "visible": true, "active": true, "userDisabled": false, "appDisabled": false, "embedderDisabled": false, "installDate": 1642544955052, "applyBackgroundUpdates": 1, "path": null, "skinnable": false, "sourceURI": null, "releaseNotesURI": null, "softDisabled": false, "foreignInstall": false, "strictCompatibility": true, "locales": [], "targetApplications": [{"id": "<EMAIL>", "minVersion": null, "maxVersion": null}], "targetPlatforms": [], "signedDate": null, "seen": true, "dependencies": [], "userPermissions": null, "optionalPermissions": null, "icons": {"32": "icon.svg"}, "iconURL": null, "blocklistState": 0, "blocklistURL": null, "startupData": {}, "hidden": false, "installTelemetryInfo": null, "recommendationState": null, "rootURI": "resource://default-theme/", "location": "app-builtin"}, {"id": "<EMAIL>", "syncGUID": "{d5c50a93-2f2b-4176-99a9-e4e76429ca40}", "version": "1.2", "type": "theme", "loader": null, "updateURL": null, "installOrigins": null, "manifestVersion": 2, "optionsURL": null, "optionsType": null, "optionsBrowserStyle": true, "aboutURL": null, "defaultLocale": {"name": "Light", "description": "A theme with a light color scheme.", "creator": "Mozilla", "developers": null, "translators": null, "contributors": null}, "visible": true, "active": false, "userDisabled": true, "appDisabled": false, "embedderDisabled": false, "installDate": 1642544955055, "applyBackgroundUpdates": 1, "path": null, "skinnable": false, "sourceURI": null, "releaseNotesURI": null, "softDisabled": false, "foreignInstall": false, "strictCompatibility": true, "locales": [], "targetApplications": [{"id": "<EMAIL>", "minVersion": null, "maxVersion": null}], "targetPlatforms": [], "signedDate": null, "seen": true, "dependencies": [], "userPermissions": null, "optionalPermissions": null, "icons": {"32": "icon.svg"}, "iconURL": null, "blocklistState": 0, "blocklistURL": null, "startupData": null, "hidden": false, "installTelemetryInfo": null, "recommendationState": null, "rootURI": "resource://builtin-themes/light/", "location": "app-builtin"}, {"id": "<EMAIL>", "syncGUID": "{441687ec-a4e6-4589-a9a2-b8b34784a349}", "version": "1.2", "type": "theme", "loader": null, "updateURL": null, "installOrigins": null, "manifestVersion": 2, "optionsURL": null, "optionsType": null, "optionsBrowserStyle": true, "aboutURL": null, "defaultLocale": {"name": "Dark", "description": "A theme with a dark color scheme.", "creator": "Mozilla", "developers": null, "translators": null, "contributors": null}, "visible": true, "active": false, "userDisabled": true, "appDisabled": false, "embedderDisabled": false, "installDate": 1642544955057, "applyBackgroundUpdates": 1, "path": null, "skinnable": false, "sourceURI": null, "releaseNotesURI": null, "softDisabled": false, "foreignInstall": false, "strictCompatibility": true, "locales": [], "targetApplications": [{"id": "<EMAIL>", "minVersion": null, "maxVersion": null}], "targetPlatforms": [], "signedDate": null, "seen": true, "dependencies": [], "userPermissions": null, "optionalPermissions": null, "icons": {"32": "icon.svg"}, "iconURL": null, "blocklistState": 0, "blocklistURL": null, "startupData": null, "hidden": false, "installTelemetryInfo": null, "recommendationState": null, "rootURI": "resource://builtin-themes/dark/", "location": "app-builtin"}, {"id": "<EMAIL>", "syncGUID": "{b3922d11-fc5c-42a6-b826-c5fe6ae719be}", "version": "1.4", "type": "theme", "loader": null, "updateURL": null, "installOrigins": null, "manifestVersion": 2, "optionsURL": null, "optionsType": null, "optionsBrowserStyle": true, "aboutURL": null, "defaultLocale": {"name": "Firefox Alpenglow", "description": "Use a colorful appearance for buttons, menus, and windows.", "creator": null, "developers": null, "translators": null, "contributors": null}, "visible": true, "active": false, "userDisabled": true, "appDisabled": false, "embedderDisabled": false, "installDate": 1642544955060, "applyBackgroundUpdates": 1, "path": null, "skinnable": false, "sourceURI": null, "releaseNotesURI": null, "softDisabled": false, "foreignInstall": false, "strictCompatibility": true, "locales": [], "targetApplications": [{"id": "<EMAIL>", "minVersion": null, "maxVersion": null}], "targetPlatforms": [], "signedDate": null, "seen": true, "dependencies": [], "userPermissions": null, "optionalPermissions": null, "icons": {"32": "icon.svg"}, "iconURL": null, "blocklistState": 0, "blocklistURL": null, "startupData": null, "hidden": false, "installTelemetryInfo": null, "recommendationState": null, "rootURI": "resource://builtin-themes/alpenglow/", "location": "app-builtin"}, {"id": "<EMAIL>", "syncGUID": "{a2981663-080b-4a37-917a-16c3b76591bd}", "version": "1.0", "type": "extension", "loader": null, "updateURL": null, "installOrigins": null, "manifestVersion": 2, "optionsURL": null, "optionsType": null, "optionsBrowserStyle": true, "aboutURL": null, "defaultLocale": {"name": "Blockchair", "description": "Blockchair", "creator": null, "developers": null, "translators": null, "contributors": null}, "visible": true, "active": true, "userDisabled": false, "appDisabled": false, "embedderDisabled": false, "installDate": 1642544957016, "applyBackgroundUpdates": 1, "path": null, "skinnable": false, "sourceURI": null, "releaseNotesURI": null, "softDisabled": false, "foreignInstall": false, "strictCompatibility": true, "locales": [], "targetApplications": [{"id": "<EMAIL>", "minVersion": null, "maxVersion": null}], "targetPlatforms": [], "signedDate": null, "seen": true, "dependencies": [], "incognito": "spanning", "userPermissions": {"permissions": [], "origins": []}, "optionalPermissions": {"permissions": [], "origins": []}, "icons": {"16": "favicon.png"}, "iconURL": null, "blocklistState": 0, "blocklistURL": null, "startupData": {}, "hidden": true, "installTelemetryInfo": null, "recommendationState": null, "rootURI": "resource://search-extensions/blockchair/", "location": "app-builtin"}, {"id": "<EMAIL>", "syncGUID": "{943dd7f1-71e1-4b5d-b25c-63e6bdbc9b30}", "version": "1.2", "type": "extension", "loader": null, "updateURL": null, "installOrigins": null, "manifestVersion": 2, "optionsURL": null, "optionsType": null, "optionsBrowserStyle": true, "aboutURL": null, "defaultLocale": {"name": "Google", "description": "Google Search", "creator": null, "developers": null, "translators": null, "contributors": null}, "visible": true, "active": true, "userDisabled": false, "appDisabled": false, "embedderDisabled": false, "installDate": 1665090913305, "applyBackgroundUpdates": 1, "path": null, "skinnable": false, "sourceURI": null, "releaseNotesURI": null, "softDisabled": false, "foreignInstall": false, "strictCompatibility": true, "locales": [], "targetApplications": [{"id": "<EMAIL>", "minVersion": null, "maxVersion": null}], "targetPlatforms": [], "signedDate": null, "seen": true, "dependencies": [], "incognito": "spanning", "userPermissions": {"permissions": [], "origins": []}, "optionalPermissions": {"permissions": [], "origins": []}, "icons": {"16": "favicon.ico"}, "iconURL": null, "blocklistState": 0, "blocklistURL": null, "startupData": {}, "hidden": true, "installTelemetryInfo": null, "recommendationState": null, "rootURI": "resource://search-extensions/google/", "location": "app-builtin"}, {"id": "<EMAIL>", "syncGUID": "{ea20a3c2-4f06-4871-abc9-67fe058b292b}", "version": "1.0", "type": "extension", "loader": null, "updateURL": null, "installOrigins": null, "manifestVersion": 2, "optionsURL": null, "optionsType": null, "optionsBrowserStyle": true, "aboutURL": null, "defaultLocale": {"name": "BlockchairOnion", "description": "Blockchair Onion", "creator": null, "developers": null, "translators": null, "contributors": null}, "visible": true, "active": true, "userDisabled": false, "appDisabled": false, "embedderDisabled": false, "installDate": 1675621851424, "applyBackgroundUpdates": 1, "path": null, "skinnable": false, "sourceURI": null, "releaseNotesURI": null, "softDisabled": false, "foreignInstall": false, "strictCompatibility": true, "locales": [], "targetApplications": [{"id": "<EMAIL>", "minVersion": null, "maxVersion": null}], "targetPlatforms": [], "signedDate": null, "seen": true, "dependencies": [], "incognito": "spanning", "userPermissions": {"permissions": [], "origins": []}, "optionalPermissions": {"permissions": [], "origins": []}, "icons": {"16": "favicon.png"}, "iconURL": null, "blocklistState": 0, "blocklistURL": null, "startupData": null, "hidden": true, "installTelemetryInfo": null, "recommendationState": null, "rootURI": "resource://search-extensions/blockchair-onion/", "location": "app-builtin"}, {"id": "{73a6fe31-595d-460b-a920-fcc0f8843232}", "syncGUID": "{7ad431d2-c4b3-4b46-9d9a-df520e640e48}", "version": "11.4.15", "type": "extension", "loader": null, "updateURL": null, "installOrigins": null, "manifestVersion": 2, "optionsURL": "ui/options.html", "optionsType": 3, "optionsBrowserStyle": true, "aboutURL": null, "defaultLocale": {"name": "NoScript", "description": "Maximum protection for your browser: NoScript allows active content only for trusted domains of your choice to prevent exploitation.", "creator": null, "developers": null, "translators": null, "contributors": null}, "visible": true, "active": true, "userDisabled": false, "appDisabled": false, "embedderDisabled": false, "installDate": ************, "updateDate": 1675622481887, "applyBackgroundUpdates": 1, "path": "C:\\Users\\<USER>\\Desktop\\WELCOME BACK\\UTILITY\\Tor Browser\\Browser\\TorBrowser\\Data\\Browser\\profile.default\\extensions\\{73a6fe31-595d-460b-a920-fcc0f8843232}.xpi", "skinnable": false, "sourceURI": "https://addons.mozilla.org/firefox/downloads/file/4062354/noscript-11.4.15.xpi", "releaseNotesURI": "https://addons.mozilla.org/en-US/firefox/addon/noscript/versions/11.4.15/updateinfo/", "softDisabled": false, "foreignInstall": true, "strictCompatibility": true, "locales": [{"name": "NoScript", "description": "Maximum protection for your browser: NoScript allows active content only for trusted domains of your choice to prevent exploitation.", "creator": null, "developers": null, "translators": null, "contributors": null, "locales": ["en"]}, {"name": "NoScript", "description": "ブラウザの最大限の保護：NoScript は、悪用を防ぐために、選択した信頼できるドメインに対してのみアクティブコンテンツを許可します。", "creator": null, "developers": null, "translators": null, "contributors": null, "locales": ["ja"]}, {"name": "NoScript", "description": "Ekstra beskyttelse for Firefox: NoScript tillater JavaScript, Flash (og andre programtillegg) kun for betrodde domener du velger (f.eks. din nettbankside). Denne hvitlistebaserte forhåndsblokkeringsbaserte tilnærmingen forhindrer utnytting av sikkerhetssvakheter (kjente og ukjente!) uten funksjonalitetstap… Eksperter vil bekrefte: Firefox er faktisk tryggere med NoScript :-)", "creator": null, "developers": null, "translators": null, "contributors": null, "locales": ["nb-NO"]}, {"name": "NoScript", "description": "Une protection maximale pour votre navigateur. NoScript ne permet les contenus actifs que pour les domaines de votre choix, afin de prévenir l’exploitation de vulnérabilités informatiques.", "creator": null, "developers": null, "translators": null, "contributors": null, "locales": ["fr"]}, {"name": "NoScript", "description": "ব্রাউজারের জন্য সর্বোচ্চ সুরক্ষা : নোস্ক্রিপ্ট আপনার বিশ্বস্ত চিহ্নিত ডোমেইনগুলোর সক্রিয় কন্টেন্টকেই শুধু চলতে দেয়, যেন কেউ পরিস্থিতির সুযোগ নিতে না পারে।", "creator": null, "developers": null, "translators": null, "contributors": null, "locales": ["bn"]}, {"name": "NoScript", "description": "Hámarksvarnir fyrir vafrann þinn: NoScript leyfir virkt efni einungis frá þeim lénum sem þú hefur ákveðið að treysta, svo koma megi í veg fyrir misnotkun.", "creator": null, "developers": null, "translators": null, "contributors": null, "locales": ["is"]}, {"name": "NoScript", "description": "Максимальний захист для вашого браузера: NoScript дозволяє активний вміст лише для довірених доменів на ваш вибір, щоб запобігти дії шкідливого програмного забезпечення.", "creator": null, "developers": null, "translators": null, "contributors": null, "locales": ["uk"]}, {"name": "NoScript", "description": "Maximalt skydd för din webbläsare: NoScript tillåter aktivt innehåll endast för betrodda domäner som du väljer för att förhindra exploatering.", "creator": null, "developers": null, "translators": null, "contributors": null, "locales": ["sv-SE"]}, {"name": "NoScript", "description": "Protezione massima per il tuo browser: NoScript permette i contenuti attivi solo per i domini fidati di tua scelta per impedirne lo sfruttamento.", "creator": null, "developers": null, "translators": null, "contributors": null, "locales": ["it"]}, {"name": "NoScript", "description": "<PERSON><PERSON><PERSON><PERSON> jū<PERSON> naršyklės apsaugojimas: Aktyvų turinį NoScript leidžia tik iš jūsų pasirinktų patikimų domenų, kad apsisaugotumėte nuo išnaudojimo.", "creator": null, "developers": null, "translators": null, "contributors": null, "locales": ["lt"]}, {"name": "NoScript", "description": "M<PERSON>j<PERSON><PERSON> maksimale për shfle<PERSON>n tuaj: NoScript lejon lëndë aktive vetëm për përkatësi të besuara që doni, për të penguar shfrytë<PERSON><PERSON>.", "creator": null, "developers": null, "translators": null, "contributors": null, "locales": ["sq"]}, {"name": "NoScript", "description": "Максимална заштита за вашиот пребарувач: NoScript овозможува активна содржина само за доверливите домеини од вашиот избор за да спречи злоупотреба.", "creator": null, "developers": null, "translators": null, "contributors": null, "locales": ["mk"]}, {"name": "NoScript", "description": "<PERSON><PERSON><PERSON><PERSON> be<PERSON>e for nettleseren din: NoScript tillater bare aktivt innhold for pålitelige domener du ønsker, for å forhindre utnyttelse.", "creator": null, "developers": null, "translators": null, "contributors": null, "locales": ["nb"]}, {"name": "NoScript", "description": "Maximaler Schutz für Ihren Browser: NoScript lässt aktive Inhalte nur für vertrauenswürdige Domains Ihrer Wahl zu, um das Ausnutzen von Sicherheitslücken zu verhindern.", "creator": null, "developers": null, "translators": null, "contributors": null, "locales": ["de"]}, {"name": "NoScript", "description": "Protecció màxima per al vostre navegador: NoScript permet contingut actiu només per a dominis de confiança que escolliu per evitar l'explotació.", "creator": null, "developers": null, "translators": null, "contributors": null, "locales": ["ca"]}, {"name": "NoScript", "description": "Perlindungan maksimum untuk pelayar anda: NoScript hanya membolehkan kandungan aktif untuk domain yang dipercayai berdasarkan pilihan anda untuk menghindari eksploitasi.", "creator": null, "developers": null, "translators": null, "contributors": null, "locales": ["ms"]}, {"name": "NoScript", "description": "بیشترین محافظت برای مرورگر شما: NoScript به محتوا فعال فقط برای دامنه های مورد اعتماد انتخابی شما برای جلوگیری از بهره‌جویی اجازه می‌دهد.", "creator": null, "developers": null, "translators": null, "contributors": null, "locales": ["fa"]}, {"name": "NoScript", "description": "最大限度地保护您的浏览器：NoScript 只允许您选择的信任域名显示活动内容，以防止漏洞利用。", "creator": null, "developers": null, "translators": null, "contributors": null, "locales": ["zh-CN"]}, {"name": "NoScript", "description": "Максимальная защита вашего браузера: для предотвращения использования уязвимостей NoScript разрешает активное содержимое только для доверенных доменов, выбранных вами.", "creator": null, "developers": null, "translators": null, "contributors": null, "locales": ["ru"]}, {"name": "NoScript", "description": "Gwarez evit ho merdeer: NoScript a aotren an endalc'hadoù oberiant hepken evit an domanioù lakaet da fizius hervez ho tibaboù, kement-se evit mirout ouzh ar c'horvoiñ.", "creator": null, "developers": null, "translators": null, "contributors": null, "locales": ["br"]}, {"name": "NoScript", "description": "Maksymalna ochrona dla twojej przeglądarki: NoScript zezwala na aktywne treści tylko w zaufanych domenach, aby zapobiec ich wykorzystaniu.", "creator": null, "developers": null, "translators": null, "contributors": null, "locales": ["pl"]}, {"name": "NoScript", "description": "Maks<PERSON>alinen suojaus selaimell<PERSON>: NoScript sallii aktiivisen sisällön vain valitsemillesi luotetuille toimialueille hyväksikäytön estämiseksi.", "creator": null, "developers": null, "translators": null, "contributors": null, "locales": ["fi"]}, {"name": "NoScript", "description": "Protecție maximă pentru browser: NoScript permite conținut activ numai pentru domenii de încredere la alegerea dumneavoastră, pentru a preveni o exploatare.", "creator": null, "developers": null, "translators": null, "contributors": null, "locales": ["ro"]}, {"name": "NoScript", "description": "Maximum protection for your browser: NoScript allows active content only for trusted domains of your choice to prevent exploitation.", "creator": null, "developers": null, "translators": null, "contributors": null, "locales": ["el"]}, {"name": "NoScript", "description": "Máxima protección para tu navegador: NoScript permite contenido activo solamente para dominios en los que confías por elección, para prevenir la explotación.", "creator": null, "developers": null, "translators": null, "contributors": null, "locales": ["es"]}, {"name": "NoScript", "description": "Proteção máxima para o seu navegador: NoScript permite conteúdo ativo apenas para domínios confiáveis de sua escolha para evitar exploração.", "creator": null, "developers": null, "translators": null, "contributors": null, "locales": ["pt-BR"]}, {"name": "NoScript", "description": "給您的瀏覽器最大保護：NoScript 僅允許您選擇之信任網域的主動式內容，以防止入侵。", "creator": null, "developers": null, "translators": null, "contributors": null, "locales": ["zh-TW"]}, {"name": "NoScript", "description": "הגנה מירבית לדפדפן שלך: NoScript מתיר תוכן פעיל רק עבור תחומים מהימנים לבחירתך כדי למנוע ניצול פירצות.", "creator": null, "developers": null, "translators": null, "contributors": null, "locales": ["he"]}, {"name": "NoScript", "description": "<PERSON><PERSON><PERSON><PERSON> be<PERSON>ttelse til din browser: NoScript tillader kun aktivt indhold på troværdige domæner som du vælger, for at forhindre udnyttelse.", "creator": null, "developers": null, "translators": null, "contributors": null, "locales": ["da"]}, {"name": "NoScript", "description": "Tarayıcınız için en iyi koruma: NoScript, kötü niyetli girişimleri engellemek için, yalnız güvenilir olarak seçtiğiniz etki alanlarında bulunan etkin içeriklerin çalışmasına izin verir.", "creator": null, "developers": null, "translators": null, "contributors": null, "locales": ["tr"]}, {"name": "NoScript", "description": "Maximale bescherming voor uw browser: NoScript staat actieve inhoud alleen toe voor vertrouwde domeinen van uw keuze, zodat uitbuiting wordt voorkomen.", "creator": null, "developers": null, "translators": null, "contributors": null, "locales": ["nl"]}], "targetApplications": [{"id": "<EMAIL>", "minVersion": "59.0", "maxVersion": null}], "targetPlatforms": [], "signedState": 2, "signedDate": 1675155923000, "seen": true, "dependencies": [], "incognito": "spanning", "userPermissions": {"permissions": ["contextMenus", "storage", "tabs", "unlimitedStorage", "webNavigation", "webRequest", "webRequestBlocking", "dns"], "origins": ["<all_urls>", "file://*/*", "ftp://*/*"]}, "optionalPermissions": {"permissions": [], "origins": []}, "icons": {"48": "img/icon48.png", "96": "img/icon96.png", "256": "img/icon256.png"}, "iconURL": null, "blocklistState": 0, "blocklistURL": null, "startupData": null, "hidden": false, "installTelemetryInfo": {"source": "app-profile", "method": "sideload"}, "recommendationState": {"validNotAfter": 1832943923000, "validNotBefore": 1675155923000, "states": ["recommended-android", "recommended"]}, "rootURI": "jar:file:///C:/Users/<USER>/Desktop/WELCOME%20BACK/UTILITY/Tor%20Browser/Browser/TorBrowser/Data/Browser/profile.default/extensions/%7B73a6fe31-595d-460b-a920-fcc0f8843232%7D.xpi!/", "location": "app-profile"}]}